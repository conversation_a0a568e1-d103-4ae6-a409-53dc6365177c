package com.foxit.crm.common.converter;

import org.springframework.beans.BeanUtils;

/**
 * 抽象DTO转换器基类
 * 
 * 提供通用的转换逻辑实现，减少子类的重复代码
 * 使用Spring的BeanUtils进行属性复制，适用于大多数简单转换场景
 * 
 * @param <E> 实体类型
 * @param <D> DTO类型
 * <AUTHOR>
 * @since 2025-06-23
 */
public abstract class AbstractDtoConverter<E, D> implements DtoConverter<E, D> {
    
    /**
     * 获取DTO类的Class对象
     * 子类必须实现此方法以提供DTO类型信息
     * 
     * @return DTO类的Class对象
     */
    protected abstract Class<D> getDtoClass();
    
    /**
     * 获取实体类的Class对象（可选实现）
     * 只有在需要实现toEntity方法时才需要重写此方法
     * 
     * @return 实体类的Class对象
     */
    protected Class<E> getEntityClass() {
        throw new UnsupportedOperationException("getEntityClass method not implemented");
    }
    
    /**
     * 默认的实体转DTO实现
     * 使用BeanUtils进行属性复制，然后调用自定义转换逻辑
     * 
     * @param entity 实体对象
     * @return DTO对象
     */
    @Override
    public D toDto(E entity) {
        if (entity == null) {
            return null;
        }
        
        try {
            D dto = getDtoClass().getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(entity, dto);
            
            // 调用自定义转换逻辑
            customizeDto(entity, dto);
            
            return dto;
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert entity to DTO", e);
        }
    }
    
    /**
     * 默认的DTO转实体实现
     * 使用BeanUtils进行属性复制，然后调用自定义转换逻辑
     * 
     * @param dto DTO对象
     * @return 实体对象
     */
    @Override
    public E toEntity(D dto) {
        if (dto == null) {
            return null;
        }
        
        try {
            E entity = getEntityClass().getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(dto, entity);
            
            // 调用自定义转换逻辑
            customizeEntity(dto, entity);
            
            return entity;
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert DTO to entity", e);
        }
    }
    
    /**
     * 自定义DTO转换逻辑
     * 子类可以重写此方法来实现特殊的转换逻辑
     * 
     * @param entity 源实体对象
     * @param dto 目标DTO对象
     */
    protected void customizeDto(E entity, D dto) {
        // 默认空实现，子类可以重写
    }
    
    /**
     * 自定义实体转换逻辑
     * 子类可以重写此方法来实现特殊的转换逻辑
     * 
     * @param dto 源DTO对象
     * @param entity 目标实体对象
     */
    protected void customizeEntity(D dto, E entity) {
        // 默认空实现，子类可以重写
    }
}
