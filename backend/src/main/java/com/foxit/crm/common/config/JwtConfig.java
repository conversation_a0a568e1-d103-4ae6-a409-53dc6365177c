package com.foxit.crm.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * JWT配置类
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@Configuration
public class JwtConfig {

    @Value("${scrm.jwt.secret}")
    private String secret;

    @Value("${scrm.jwt.expire}")
    private Long expire;

    @Value("${scrm.jwt.header}")
    private String header;

    @Value("${scrm.jwt.prefix}")
    private String prefix;

    public String getSecret() {
        return secret;
    }

    public Long getExpire() {
        return expire;
    }

    public String getHeader() {
        return header;
    }

    public String getPrefix() {
        return prefix;
    }
}
