package com.foxit.crm.common.version;

import com.foxit.crm.common.annotation.ApiVersion;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.reflect.Method;

/**
 * API版本控制请求映射处理器
 * 根据@ApiVersion注解动态生成请求映射路径
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public class ApiVersionRequestMappingHandlerMapping extends RequestMappingHandlerMapping {

    private static final String VERSION_PREFIX = "/v";

    @Override
    protected RequestMappingInfo getMappingForMethod(Method method, Class<?> handlerType) {
        RequestMappingInfo info = super.getMappingForMethod(method, handlerType);
        if (info == null) {
            return null;
        }

        // 检查方法级别的@ApiVersion注解
        ApiVersion methodVersion = AnnotationUtils.findAnnotation(method, ApiVersion.class);
        if (methodVersion != null) {
            info = createVersionedRequestMappingInfo(info, methodVersion);
        } else {
            // 检查类级别的@ApiVersion注解
            ApiVersion classVersion = AnnotationUtils.findAnnotation(handlerType, ApiVersion.class);
            if (classVersion != null) {
                info = createVersionedRequestMappingInfo(info, classVersion);
            }
        }

        return info;
    }

    /**
     * 创建带版本信息的请求映射
     */
    private RequestMappingInfo createVersionedRequestMappingInfo(RequestMappingInfo info, ApiVersion apiVersion) {
        String version = apiVersion.value();

        // 构建版本化的路径
        RequestMappingInfo.Builder builder = RequestMappingInfo.paths(VERSION_PREFIX + version)
                .methods(info.getMethodsCondition().getMethods()
                        .toArray(new org.springframework.web.bind.annotation.RequestMethod[0]))
                .params(info.getParamsCondition().getExpressions().toArray(new String[0]))
                .headers(info.getHeadersCondition().getExpressions().toArray(new String[0]))
                .consumes(info.getConsumesCondition().getConsumableMediaTypes().stream()
                        .map(org.springframework.http.MediaType::toString)
                        .toArray(String[]::new))
                .produces(info.getProducesCondition().getProducibleMediaTypes().stream()
                        .map(org.springframework.http.MediaType::toString)
                        .toArray(String[]::new));

        // 添加自定义条件
        if (apiVersion.deprecated()) {
            builder.customCondition(new DeprecatedApiCondition(apiVersion.deprecatedMessage()));
        }

        return builder.build().combine(info);
    }

    /**
     * 废弃API条件
     */
    private static class DeprecatedApiCondition
            implements org.springframework.web.servlet.mvc.condition.RequestCondition<DeprecatedApiCondition> {

        private final String message;

        public DeprecatedApiCondition(String message) {
            this.message = message;
        }

        @Override
        public DeprecatedApiCondition combine(DeprecatedApiCondition other) {
            return new DeprecatedApiCondition(this.message + "; " + other.message);
        }

        @Override
        public DeprecatedApiCondition getMatchingCondition(jakarta.servlet.http.HttpServletRequest request) {
            // 在响应头中添加废弃警告
            return this;
        }

        @Override
        public int compareTo(DeprecatedApiCondition other, jakarta.servlet.http.HttpServletRequest request) {
            return 0;
        }

        public String getMessage() {
            return message;
        }
    }
}
