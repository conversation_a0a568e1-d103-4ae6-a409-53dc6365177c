package com.foxit.crm.common.cache;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.concurrent.Callable;

/**
 * 缓存管理服务
 * 提供统一的缓存操作接口和缓存管理功能
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CacheService {

    private final CacheManager cacheManager;

    /**
     * 获取缓存值
     */
    public <T> T get(String cacheName, String key, Class<T> type) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            Cache.ValueWrapper wrapper = cache.get(key);
            if (wrapper != null) {
                Object value = wrapper.get();
                if (type.isInstance(value)) {
                    return type.cast(value);
                }
            }
        }
        return null;
    }

    /**
     * 获取缓存值，如果不存在则执行回调函数获取并缓存
     */
    public <T> T get(String cacheName, String key, Class<T> type, Callable<T> valueLoader) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            try {
                Object value = cache.get(key, valueLoader);
                if (value != null && type.isInstance(value)) {
                    return type.cast(value);
                }
            } catch (Exception e) {
                log.error("获取缓存失败: cacheName={}, key={}", cacheName, key, e);
            }
        }
        return null;
    }

    /**
     * 设置缓存值
     */
    public void put(String cacheName, String key, Object value) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.put(key, value);
            log.debug("缓存设置成功: cacheName={}, key={}", cacheName, key);
        }
    }

    /**
     * 设置缓存值（如果不存在）
     */
    public <T> T putIfAbsent(String cacheName, String key, T value) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            Cache.ValueWrapper wrapper = cache.putIfAbsent(key, value);
            if (wrapper != null) {
                Object existingValue = wrapper.get();
                if (existingValue != null && value.getClass().isInstance(existingValue)) {
                    return (T) existingValue;
                }
            }
            log.debug("缓存设置成功: cacheName={}, key={}", cacheName, key);
        }
        return value;
    }

    /**
     * 删除缓存
     */
    public void evict(String cacheName, String key) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.evict(key);
            log.debug("缓存删除成功: cacheName={}, key={}", cacheName, key);
        }
    }

    /**
     * 清空指定缓存
     */
    public void clear(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.clear();
            log.info("缓存清空成功: cacheName={}", cacheName);
        }
    }

    /**
     * 清空所有缓存
     */
    public void clearAll() {
        Collection<String> cacheNames = cacheManager.getCacheNames();
        for (String cacheName : cacheNames) {
            clear(cacheName);
        }
        log.info("所有缓存清空成功");
    }

    /**
     * 检查缓存是否存在
     */
    public boolean exists(String cacheName, String key) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            return cache.get(key) != null;
        }
        return false;
    }

    /**
     * 获取所有缓存名称
     */
    public Collection<String> getCacheNames() {
        return cacheManager.getCacheNames();
    }

    /**
     * 预热缓存
     */
    public void warmUp(String cacheName, String key, Callable<Object> valueLoader) {
        try {
            Object value = valueLoader.call();
            if (value != null) {
                put(cacheName, key, value);
                log.info("缓存预热成功: cacheName={}, key={}", cacheName, key);
            }
        } catch (Exception e) {
            log.error("缓存预热失败: cacheName={}, key={}", cacheName, key, e);
        }
    }

    /**
     * 批量删除缓存（支持通配符）
     */
    public void evictByPattern(String cacheName, String pattern) {
        // 这里需要根据具体的缓存实现来处理通配符删除
        // 对于Redis缓存，可以使用RedisTemplate的keys方法
        log.info("批量删除缓存: cacheName={}, pattern={}", cacheName, pattern);
        // TODO: 实现通配符删除逻辑
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStats getCacheStats(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            // 这里返回基本的缓存信息，具体统计需要根据缓存实现来扩展
            return CacheStats.builder()
                    .cacheName(cacheName)
                    .exists(true)
                    .build();
        }
        return CacheStats.builder()
                .cacheName(cacheName)
                .exists(false)
                .build();
    }

    /**
     * 缓存统计信息
     */
    public static class CacheStats {
        private String cacheName;
        private boolean exists;
        private long hitCount;
        private long missCount;
        private long evictionCount;

        public static CacheStatsBuilder builder() {
            return new CacheStatsBuilder();
        }

        public static class CacheStatsBuilder {
            private String cacheName;
            private boolean exists;
            private long hitCount;
            private long missCount;
            private long evictionCount;

            public CacheStatsBuilder cacheName(String cacheName) {
                this.cacheName = cacheName;
                return this;
            }

            public CacheStatsBuilder exists(boolean exists) {
                this.exists = exists;
                return this;
            }

            public CacheStatsBuilder hitCount(long hitCount) {
                this.hitCount = hitCount;
                return this;
            }

            public CacheStatsBuilder missCount(long missCount) {
                this.missCount = missCount;
                return this;
            }

            public CacheStatsBuilder evictionCount(long evictionCount) {
                this.evictionCount = evictionCount;
                return this;
            }

            public CacheStats build() {
                CacheStats stats = new CacheStats();
                stats.cacheName = this.cacheName;
                stats.exists = this.exists;
                stats.hitCount = this.hitCount;
                stats.missCount = this.missCount;
                stats.evictionCount = this.evictionCount;
                return stats;
            }
        }

        // Getters
        public String getCacheName() {
            return cacheName;
        }

        public boolean isExists() {
            return exists;
        }

        public long getHitCount() {
            return hitCount;
        }

        public long getMissCount() {
            return missCount;
        }

        public long getEvictionCount() {
            return evictionCount;
        }

        public double getHitRate() {
            long total = hitCount + missCount;
            return total > 0 ? (double) hitCount / total : 0.0;
        }
    }
}
