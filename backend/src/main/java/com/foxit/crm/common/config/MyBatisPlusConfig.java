package com.foxit.crm.common.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.foxit.crm.common.datapermission.DataPermissionInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * MyBatis Plus配置
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Configuration
public class MyBatisPlusConfig implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * MyBatis-Plus 插件配置
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

        return interceptor;
    }

    /**
     * 自动填充配置
     */
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
                this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
                this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
            }
        };
    }

    /**
     * 注册数据权限拦截器
     * 使用ApplicationContext获取SqlSessionFactory，避免循环依赖
     */
    @PostConstruct
    public void addDataPermissionInterceptor() {
        try {
            // 获取所有SqlSessionFactory实例
            Map<String, SqlSessionFactory> sqlSessionFactoryMap = applicationContext
                    .getBeansOfType(SqlSessionFactory.class);

            // 获取DataPermissionInterceptor实例
            DataPermissionInterceptor dataPermissionInterceptor = applicationContext
                    .getBean(DataPermissionInterceptor.class);

            // 为每个SqlSessionFactory注册数据权限拦截器
            for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryMap.values()) {
                sqlSessionFactory.getConfiguration().addInterceptor(dataPermissionInterceptor);
            }
        } catch (Exception e) {
            // 如果获取失败，记录警告但不影响启动
            System.err.println("Warning: Failed to register DataPermissionInterceptor: " + e.getMessage());
        }
    }
}
