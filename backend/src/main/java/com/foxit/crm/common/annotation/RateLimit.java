package com.foxit.crm.common.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 接口限流注解
 * 支持基于IP、用户、接口等维度的限流控制
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RateLimit {

    /**
     * 限流key的前缀
     */
    String key() default "";

    /**
     * 限流时间窗口（秒）
     */
    int time() default 60;

    /**
     * 时间窗口内最大请求次数
     */
    int count() default 100;

    /**
     * 限流类型
     */
    LimitType limitType() default LimitType.DEFAULT;

    /**
     * 限流失败时的提示信息
     */
    String message() default "访问过于频繁，请稍后再试";

    /**
     * 时间单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * 是否启用
     */
    boolean enabled() default true;

    /**
     * 限流类型枚举
     */
    enum LimitType {
        /**
         * 默认策略全局限流
         */
        DEFAULT,

        /**
         * 根据请求者IP进行限流
         */
        IP,

        /**
         * 根据用户ID进行限流
         */
        USER,

        /**
         * 根据接口进行限流
         */
        API,

        /**
         * 自定义key限流
         */
        CUSTOM
    }
}
