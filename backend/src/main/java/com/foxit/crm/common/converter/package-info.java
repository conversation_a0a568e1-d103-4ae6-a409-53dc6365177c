/**
 * 通用DTO转换器包
 * 
 * <h2>概述</h2>
 * 本包提供了SCRM-Next项目中统一的DTO转换器架构，用于标准化实体对象与DTO对象之间的转换逻辑。
 * 
 * <h2>核心组件</h2>
 * <ul>
 *   <li>{@link com.foxit.crm.common.converter.DtoConverter} - 转换器接口，定义转换规范</li>
 *   <li>{@link com.foxit.crm.common.converter.AbstractDtoConverter} - 抽象转换器基类，提供通用实现</li>
 *   <li>{@link com.foxit.crm.common.converter.ConverterFactory} - 转换器工厂，提供转换器管理功能</li>
 * </ul>
 * 
 * <h2>使用方式</h2>
 * 
 * <h3>1. 创建转换器</h3>
 * <pre>{@code
 * @Component
 * public class UserConverter extends AbstractDtoConverter<User, UserDetailResponse> {
 *     
 *     @Override
 *     protected Class<UserDetailResponse> getDtoClass() {
 *         return UserDetailResponse.class;
 *     }
 *     
 *     @Override
 *     protected void customizeDto(User entity, UserDetailResponse dto) {
 *         // 自定义转换逻辑
 *         dto.setStatusText(entity.getStatus() == 1 ? "启用" : "禁用");
 *     }
 * }
 * }</pre>
 * 
 * <h3>2. 在服务中使用转换器</h3>
 * <pre>{@code
 * @Service
 * public class UserService {
 *     
 *     private final UserConverter userConverter;
 *     
 *     public UserDetailResponse getUserById(Long id) {
 *         User user = userRepository.findById(id);
 *         return userConverter.toDto(user);
 *     }
 *     
 *     public PageResponse<UserDetailResponse> getUserList(int page, int size) {
 *         List<User> users = userRepository.findByPage(page, size);
 *         List<UserDetailResponse> dtos = userConverter.toDtoList(users);
 *         return new PageResponse<>(dtos, total, page, size);
 *     }
 * }
 * }</pre>
 * 
 * <h3>3. 使用转换器工厂（高级用法）</h3>
 * <pre>{@code
 * @Service
 * public class GenericService {
 *     
 *     private final ConverterFactory converterFactory;
 *     
 *     public <E, D> D convertEntity(E entity, Class<D> dtoClass) {
 *         DtoConverter<E, D> converter = converterFactory.getConverter(
 *             (Class<E>) entity.getClass(), dtoClass
 *         );
 *         return converter.toDto(entity);
 *     }
 * }
 * }</pre>
 * 
 * <h2>设计原则</h2>
 * <ul>
 *   <li><strong>统一性</strong>：所有模块使用相同的转换器接口和规范</li>
 *   <li><strong>可扩展性</strong>：支持自定义转换逻辑，满足复杂业务需求</li>
 *   <li><strong>性能优化</strong>：使用缓存机制，避免重复查找转换器</li>
 *   <li><strong>类型安全</strong>：使用泛型确保编译时类型检查</li>
 *   <li><strong>Spring集成</strong>：与Spring容器深度集成，支持依赖注入</li>
 * </ul>
 * 
 * <h2>最佳实践</h2>
 * <ul>
 *   <li>每个实体类对应一个主要的转换器</li>
 *   <li>转换器类命名规范：{EntityName}Converter</li>
 *   <li>转换器放在对应模块的converter包下</li>
 *   <li>复杂转换逻辑在customizeDto方法中实现</li>
 *   <li>避免在转换器中进行数据库查询等重操作</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-06-23
 */
package com.foxit.crm.common.converter;
