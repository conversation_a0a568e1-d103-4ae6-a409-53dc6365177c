package com.foxit.crm.common.constant;

/**
 * Redis Key常量类
 * 统一管理所有Redis缓存键的命名规范
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
public class RedisKeyConstant {

    // ============================
    // 基础配置
    // ============================

    /**
     * 项目前缀
     */
    public static final String PROJECT_PREFIX = "scrm:";

    /**
     * 分隔符
     */
    public static final String SEPARATOR = ":";

    // ============================
    // 用户认证相关
    // ============================

    /**
     * 用户Token前缀
     */
    public static final String USER_TOKEN_PREFIX = PROJECT_PREFIX + "token" + SEPARATOR;

    /**
     * 用户信息缓存前缀
     */
    public static final String USER_INFO_PREFIX = PROJECT_PREFIX + "user:info" + SEPARATOR;

    /**
     * 验证码前缀
     */
    public static final String CAPTCHA_PREFIX = PROJECT_PREFIX + "captcha" + SEPARATOR;

    /**
     * 登录失败次数前缀
     */
    public static final String LOGIN_FAIL_COUNT_PREFIX = PROJECT_PREFIX + "login:fail" + SEPARATOR;

    /**
     * 在线用户前缀
     */
    public static final String ONLINE_USER_PREFIX = PROJECT_PREFIX + "online:user" + SEPARATOR;

    // ============================
    // 权限管理相关
    // ============================

    /**
     * 用户权限缓存前缀
     */
    public static final String USER_PERMISSIONS_PREFIX = PROJECT_PREFIX + "user:permissions" + SEPARATOR;

    /**
     * 用户角色缓存前缀
     */
    public static final String USER_ROLES_PREFIX = PROJECT_PREFIX + "user:roles" + SEPARATOR;

    /**
     * 角色权限缓存前缀
     */
    public static final String ROLE_PERMISSIONS_PREFIX = PROJECT_PREFIX + "role:permissions" + SEPARATOR;

    /**
     * 权限树缓存
     */
    public static final String PERMISSION_TREE = PROJECT_PREFIX + "permission:tree";

    /**
     * 菜单权限树缓存
     */
    public static final String MENU_PERMISSION_TREE = PROJECT_PREFIX + "menu:permission:tree";

    /**
     * 数据权限规则缓存前缀
     */
    public static final String DATA_PERMISSION_PREFIX = PROJECT_PREFIX + "data:permission" + SEPARATOR;

    /**
     * 用户数据权限规则缓存前缀
     */
    public static final String USER_DATA_PERMISSION_PREFIX = PROJECT_PREFIX + "user:data:permission" + SEPARATOR;

    // ============================
    // 系统配置相关
    // ============================

    /**
     * 系统配置缓存前缀
     */
    public static final String SYS_CONFIG_PREFIX = PROJECT_PREFIX + "config" + SEPARATOR;

    /**
     * 系统配置值缓存前缀
     */
    public static final String SYS_CONFIG_VALUE_PREFIX = PROJECT_PREFIX + "config:value" + SEPARATOR;

    /**
     * 配置分组缓存
     */
    public static final String CONFIG_GROUPS = PROJECT_PREFIX + "config:groups";

    // ============================
    // 产品线管理相关
    // ============================

    /**
     * 产品线信息缓存前缀
     */
    public static final String PRODUCT_LINE_PREFIX = PROJECT_PREFIX + "product:line" + SEPARATOR;

    /**
     * 产品线列表缓存前缀（按类型）
     */
    public static final String PRODUCT_LINE_LIST_PREFIX = PROJECT_PREFIX + "product:line:list" + SEPARATOR;

    /**
     * 启用的产品线列表缓存
     */
    public static final String ENABLED_PRODUCT_LINES = PROJECT_PREFIX + "product:line:enabled";

    /**
     * 产品线统计缓存前缀
     */
    public static final String PRODUCT_LINE_STATS_PREFIX = PROJECT_PREFIX + "product:line:stats" + SEPARATOR;

    // ============================
    // 操作日志相关
    // ============================

    /**
     * 操作日志统计缓存前缀
     */
    public static final String OPERATION_LOG_STATS_PREFIX = PROJECT_PREFIX + "log:stats" + SEPARATOR;

    /**
     * 用户操作统计缓存前缀
     */
    public static final String USER_OPERATION_STATS_PREFIX = PROJECT_PREFIX + "user:operation:stats" + SEPARATOR;

    /**
     * 操作类型统计缓存
     */
    public static final String OPERATION_TYPE_STATS = PROJECT_PREFIX + "operation:type:stats";

    /**
     * 每日操作统计缓存前缀
     */
    public static final String DAILY_OPERATION_STATS_PREFIX = PROJECT_PREFIX + "daily:operation:stats" + SEPARATOR;

    // ============================
    // 缓存过期时间常量（秒）
    // ============================

    /**
     * 短期缓存过期时间（5分钟）
     */
    public static final long SHORT_EXPIRE = 5 * 60;

    /**
     * 中期缓存过期时间（30分钟）
     */
    public static final long MEDIUM_EXPIRE = 30 * 60;

    /**
     * 长期缓存过期时间（2小时）
     */
    public static final long LONG_EXPIRE = 2 * 60 * 60;

    /**
     * 超长期缓存过期时间（24小时）
     */
    public static final long EXTRA_LONG_EXPIRE = 24 * 60 * 60;

    // ============================
    // Key构建工具方法
    // ============================

    /**
     * 构建用户Token缓存键
     */
    public static String buildUserTokenKey(String token) {
        return USER_TOKEN_PREFIX + token;
    }

    /**
     * 构建用户信息缓存键
     */
    public static String buildUserInfoKey(Long userId) {
        return USER_INFO_PREFIX + userId;
    }

    /**
     * 构建用户权限缓存键
     */
    public static String buildUserPermissionsKey(Long userId) {
        return USER_PERMISSIONS_PREFIX + userId;
    }

    /**
     * 构建用户角色缓存键
     */
    public static String buildUserRolesKey(Long userId) {
        return USER_ROLES_PREFIX + userId;
    }

    /**
     * 构建角色权限缓存键
     */
    public static String buildRolePermissionsKey(Long roleId) {
        return ROLE_PERMISSIONS_PREFIX + roleId;
    }

    /**
     * 构建产品线缓存键
     */
    public static String buildProductLineKey(Long productLineId) {
        return PRODUCT_LINE_PREFIX + productLineId;
    }

    /**
     * 构建产品线列表缓存键
     */
    public static String buildProductLineListKey(Integer type, Integer status) {
        return PRODUCT_LINE_LIST_PREFIX + type + SEPARATOR + status;
    }

    /**
     * 构建系统配置缓存键
     */
    public static String buildConfigKey(String configKey) {
        return SYS_CONFIG_PREFIX + configKey;
    }

    /**
     * 构建系统配置值缓存键
     */
    public static String buildConfigValueKey(String configKey) {
        return SYS_CONFIG_VALUE_PREFIX + configKey;
    }

    /**
     * 构建用户数据权限缓存键
     */
    public static String buildUserDataPermissionKey(Long userId, String tableName) {
        return USER_DATA_PERMISSION_PREFIX + userId + SEPARATOR + tableName;
    }

    /**
     * 构建操作日志统计缓存键
     */
    public static String buildOperationStatsKey(String dateRange) {
        return OPERATION_LOG_STATS_PREFIX + dateRange;
    }
}
