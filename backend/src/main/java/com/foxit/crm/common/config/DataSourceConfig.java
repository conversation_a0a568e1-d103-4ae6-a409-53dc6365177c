package com.foxit.crm.common.config;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * 多数据源配置
 * 配置MySQL主数据源和ClickHouse分析数据源
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@Configuration
public class DataSourceConfig {

    /**
     * MySQL主数据源配置
     * 用于用户管理、系统配置、业务元数据等
     */
    @Primary
    @Bean("mysqlDataSource")
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource mysqlDataSource() {
        log.info("初始化MySQL数据源");
        return DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
    }

    /**
     * ClickHouse分析数据源配置
     * 用于用户行为分析、实时统计等大数据场景
     */
    @Bean("clickhouseDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.clickhouse")
    public DataSource clickhouseDataSource() {
        log.info("初始化ClickHouse数据源");
        DataSource dataSource = DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
        log.info("ClickHouse数据源创建完成: {}", dataSource.getClass().getSimpleName());
        return dataSource;
    }

    /**
     * MySQL JdbcTemplate
     */
    @Primary
    @Bean("mysqlJdbcTemplate")
    public JdbcTemplate mysqlJdbcTemplate(@Qualifier("mysqlDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    /**
     * ClickHouse JdbcTemplate
     */
    @Bean("clickhouseJdbcTemplate")
    public JdbcTemplate clickhouseJdbcTemplate(@Qualifier("clickhouseDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}
