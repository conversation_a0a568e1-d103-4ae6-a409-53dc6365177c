package com.foxit.crm.common.util;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.util.StringUtils;

/**
 * IP工具类
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
public final class IpUtil {

    private static final String UNKNOWN = "unknown";
    private static final String X_FORWARDED_FOR = "X-Forwarded-For";
    private static final String X_REAL_IP = "X-Real-IP";
    private static final String PROXY_CLIENT_IP = "Proxy-Client-IP";
    private static final String WL_PROXY_CLIENT_IP = "WL-Proxy-Client-IP";
    private static final String AUTHORIZATION = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";

    private IpUtil() {
        throw new UnsupportedOperationException("Utility class");
    }

    /**
     * 获取客户端IP地址
     */
    public static String getClientIp(HttpServletRequest request) {
        if (request == null) {
            return null;
        }

        String ip = getHeaderValue(request, X_FORWARDED_FOR);
        if (isValidIp(ip)) {
            return ip.split(",")[0].trim();
        }

        ip = getHeaderValue(request, X_REAL_IP);
        if (isValidIp(ip)) {
            return ip;
        }

        ip = getHeaderValue(request, PROXY_CLIENT_IP);
        if (isValidIp(ip)) {
            return ip;
        }

        ip = getHeaderValue(request, WL_PROXY_CLIENT_IP);
        if (isValidIp(ip)) {
            return ip;
        }

        return request.getRemoteAddr();
    }

    /**
     * 提取JWT Token
     */
    public static String extractToken(HttpServletRequest request) {
        if (request == null) {
            return null;
        }

        String bearerToken = request.getHeader(AUTHORIZATION);
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith(BEARER_PREFIX)) {
            return bearerToken.substring(BEARER_PREFIX.length());
        }
        return null;
    }

    private static String getHeaderValue(HttpServletRequest request, String headerName) {
        return request.getHeader(headerName);
    }

    private static boolean isValidIp(String ip) {
        return StringUtils.hasText(ip) && !UNKNOWN.equalsIgnoreCase(ip);
    }
}
