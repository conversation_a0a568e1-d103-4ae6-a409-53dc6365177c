package com.foxit.crm.common.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.spring.cache.CacheConfig;
import org.redisson.spring.cache.RedissonSpringCacheManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.HashMap;
import java.util.Map;

/**
 * Redisson配置类
 * 配置Redisson客户端和Spring Cache集成
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Configuration
@EnableCaching
public class RedissonConfig {

    @Value("${spring.data.redis.host:localhost}")
    private String redisHost;

    @Value("${spring.data.redis.port:6379}")
    private int redisPort;

    @Value("${spring.data.redis.database:0}")
    private int database;

    @Value("${spring.data.redis.password:}")
    private String password;

    @Value("${spring.data.redis.timeout:5000ms}")
    private String timeout;

    /**
     * Redisson客户端配置
     */
    @Bean
    @Primary
    public RedissonClient redissonClient() {
        Config config = new Config();

        // 单机模式配置
        String address = "redis://" + redisHost + ":" + redisPort;
        config.useSingleServer()
                .setAddress(address)
                .setDatabase(database)
                .setConnectionMinimumIdleSize(5)
                .setConnectionPoolSize(10)
                .setIdleConnectionTimeout(10000)
                .setConnectTimeout(5000)
                .setTimeout(3000)
                .setRetryAttempts(3)
                .setRetryInterval(1500);

        // 设置密码（如果有）
        if (password != null && !password.trim().isEmpty()) {
            config.useSingleServer().setPassword(password);
        }

        return Redisson.create(config);
    }

    /**
     * Spring Cache管理器配置
     */
    @Bean
    @Primary
    public CacheManager cacheManager(RedissonClient redissonClient) {
        Map<String, CacheConfig> config = new HashMap<>();

        // Dashboard缓存 - 5分钟过期，高频访问
        config.put("dashboard", new CacheConfig(5 * 60 * 1000, 2 * 60 * 1000));

        // 用户缓存 - 1小时过期，中频访问
        config.put("user", new CacheConfig(60 * 60 * 1000, 30 * 60 * 1000));

        // 角色缓存 - 2小时过期，低频变更
        config.put("role", new CacheConfig(2 * 60 * 60 * 1000, 60 * 60 * 1000));

        // 权限缓存 - 4小时过期，极低频变更
        config.put("permission", new CacheConfig(4 * 60 * 60 * 1000, 2 * 60 * 60 * 1000));

        // 产品线缓存 - 1小时过期，中频访问
        config.put("productLine", new CacheConfig(60 * 60 * 1000, 30 * 60 * 1000));

        // 系统配置缓存 - 6小时过期，极低频变更
        config.put("config", new CacheConfig(6 * 60 * 60 * 1000, 3 * 60 * 60 * 1000));

        // 操作日志缓存 - 10分钟过期，用于统计查询
        config.put("operationLog", new CacheConfig(10 * 60 * 1000, 5 * 60 * 1000));

        // 兼容旧的缓存名称
        config.put("systemConfig", new CacheConfig(6 * 60 * 60 * 1000, 3 * 60 * 60 * 1000));
        config.put("systemConfigValue", new CacheConfig(6 * 60 * 60 * 1000, 3 * 60 * 60 * 1000));
        config.put("permissionTree", new CacheConfig(4 * 60 * 60 * 1000, 2 * 60 * 60 * 1000));
        config.put("menuPermissionTree", new CacheConfig(4 * 60 * 60 * 1000, 2 * 60 * 60 * 1000));
        config.put("userPermissions", new CacheConfig(60 * 60 * 1000, 30 * 60 * 1000));
        config.put("rolePermissions", new CacheConfig(2 * 60 * 60 * 1000, 60 * 60 * 1000));
        config.put("productLineList", new CacheConfig(60 * 60 * 1000, 30 * 60 * 1000));
        config.put("dataPermission", new CacheConfig(60 * 60 * 1000, 30 * 60 * 1000));
        config.put("operationStats", new CacheConfig(10 * 60 * 1000, 5 * 60 * 1000));
        config.put("userInfo", new CacheConfig(60 * 60 * 1000, 30 * 60 * 1000));

        return new RedissonSpringCacheManager(redissonClient, config);
    }
}
