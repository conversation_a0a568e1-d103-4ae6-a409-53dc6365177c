package com.foxit.crm.common.version;

import com.foxit.crm.common.annotation.ApiVersion;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * API版本控制拦截器
 * 处理API版本验证和响应头设置
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
public class ApiVersionInterceptor implements HandlerInterceptor {

    private static final String API_VERSION_HEADER = "API-Version";
    private static final String DEPRECATION_HEADER = "Deprecation";
    private static final String WARNING_HEADER = "Warning";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        ApiVersion apiVersion = getApiVersion(handlerMethod);
        
        if (apiVersion == null) {
            return true;
        }

        // 验证API版本
        String requestedVersion = getRequestedVersion(request);
        if (!isVersionSupported(requestedVersion, apiVersion)) {
            response.setStatus(HttpServletResponse.SC_NOT_ACCEPTABLE);
            response.setContentType("application/json");
            response.getWriter().write("{\"code\":406,\"message\":\"API版本不支持\",\"supportedVersion\":\"" + apiVersion.value() + "\"}");
            return false;
        }

        // 设置响应头
        response.setHeader(API_VERSION_HEADER, apiVersion.value());
        
        // 如果是废弃的API，添加警告头
        if (apiVersion.deprecated()) {
            response.setHeader(DEPRECATION_HEADER, "true");
            String warningMessage = apiVersion.deprecatedMessage().isEmpty() 
                ? "此API版本已废弃，请升级到最新版本" 
                : apiVersion.deprecatedMessage();
            response.setHeader(WARNING_HEADER, "299 - \"" + warningMessage + "\"");
            
            log.warn("使用了废弃的API: {} {}, 版本: {}, 警告: {}", 
                request.getMethod(), request.getRequestURI(), apiVersion.value(), warningMessage);
        }

        return true;
    }

    /**
     * 获取API版本注解
     */
    private ApiVersion getApiVersion(HandlerMethod handlerMethod) {
        // 优先获取方法级别的注解
        ApiVersion methodVersion = AnnotationUtils.findAnnotation(handlerMethod.getMethod(), ApiVersion.class);
        if (methodVersion != null) {
            return methodVersion;
        }
        
        // 获取类级别的注解
        return AnnotationUtils.findAnnotation(handlerMethod.getBeanType(), ApiVersion.class);
    }

    /**
     * 获取请求的API版本
     */
    private String getRequestedVersion(HttpServletRequest request) {
        // 1. 从Header中获取
        String version = request.getHeader(API_VERSION_HEADER);
        if (version != null && !version.isEmpty()) {
            return version;
        }

        // 2. 从URL路径中获取
        String requestURI = request.getRequestURI();
        if (requestURI.contains("/v")) {
            String[] parts = requestURI.split("/");
            for (String part : parts) {
                if (part.startsWith("v") && part.length() > 1) {
                    return part;
                }
            }
        }

        // 3. 从请求参数中获取
        version = request.getParameter("version");
        if (version != null && !version.isEmpty()) {
            return version.startsWith("v") ? version : "v" + version;
        }

        // 4. 默认版本
        return "v1";
    }

    /**
     * 验证版本是否支持
     */
    private boolean isVersionSupported(String requestedVersion, ApiVersion apiVersion) {
        String supportedVersion = apiVersion.value();
        
        // 如果请求的版本与支持的版本完全匹配
        if (requestedVersion.equals(supportedVersion)) {
            return true;
        }

        // 如果是默认版本且请求版本为v1
        if (apiVersion.isDefault() && "v1".equals(requestedVersion)) {
            return true;
        }

        // 检查版本范围
        if (!apiVersion.minVersion().isEmpty()) {
            if (compareVersion(requestedVersion, apiVersion.minVersion()) < 0) {
                return false;
            }
        }

        if (!apiVersion.maxVersion().isEmpty()) {
            if (compareVersion(requestedVersion, apiVersion.maxVersion()) > 0) {
                return false;
            }
        }

        return false;
    }

    /**
     * 比较版本号
     * @return 负数表示version1 < version2，0表示相等，正数表示version1 > version2
     */
    private int compareVersion(String version1, String version2) {
        // 移除v前缀
        String v1 = version1.startsWith("v") ? version1.substring(1) : version1;
        String v2 = version2.startsWith("v") ? version2.substring(1) : version2;
        
        String[] parts1 = v1.split("\\.");
        String[] parts2 = v2.split("\\.");
        
        int maxLength = Math.max(parts1.length, parts2.length);
        
        for (int i = 0; i < maxLength; i++) {
            int num1 = i < parts1.length ? Integer.parseInt(parts1[i]) : 0;
            int num2 = i < parts2.length ? Integer.parseInt(parts2[i]) : 0;
            
            if (num1 != num2) {
                return Integer.compare(num1, num2);
            }
        }
        
        return 0;
    }
}
