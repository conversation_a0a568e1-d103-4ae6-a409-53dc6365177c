package com.foxit.crm.common.annotation;

import java.lang.annotation.*;

/**
 * API版本控制注解
 * 用于标记API的版本信息，支持向后兼容
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ApiVersion {

    /**
     * API版本号
     * 格式：v1, v2, v1.1, v2.0 等
     */
    String value() default "v1";

    /**
     * 是否为默认版本
     * 当客户端未指定版本时，使用默认版本
     */
    boolean isDefault() default false;

    /**
     * 版本描述
     */
    String description() default "";

    /**
     * 废弃版本标记
     * 标记为废弃的版本会在响应头中添加警告信息
     */
    boolean deprecated() default false;

    /**
     * 废弃说明
     */
    String deprecatedMessage() default "";

    /**
     * 最小支持版本
     * 低于此版本的请求将被拒绝
     */
    String minVersion() default "";

    /**
     * 最大支持版本
     * 高于此版本的请求将被拒绝
     */
    String maxVersion() default "";
}
