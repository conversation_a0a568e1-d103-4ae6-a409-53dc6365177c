package com.foxit.crm.common.aspect;

import com.foxit.crm.common.annotation.RateLimit;
import com.foxit.crm.common.exception.BusinessException;
import com.foxit.crm.common.exception.ErrorCode;
import com.foxit.crm.common.util.IpUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * 限流切面
 * 基于Redisson实现分布式限流
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class RateLimitAspect {

    private final RedissonClient redissonClient;

    private static final String RATE_LIMIT_KEY_PREFIX = "rate_limit:";

    @Before("@annotation(com.foxit.crm.common.annotation.RateLimit)")
    public void doBefore(JoinPoint point) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        RateLimit rateLimit = AnnotationUtils.findAnnotation(method, RateLimit.class);

        if (rateLimit == null || !rateLimit.enabled()) {
            return;
        }

        String key = getCombineKey(rateLimit, point);
        long count = rateLimit.count();
        long time = rateLimit.time();
        TimeUnit timeUnit = rateLimit.timeUnit();

        // 转换为秒
        long timeInSeconds = timeUnit.toSeconds(time);
        if (timeInSeconds <= 0) {
            timeInSeconds = 1;
        }

        try {
            RRateLimiter rateLimiter = redissonClient.getRateLimiter(key);
            
            // 设置限流器参数：在指定时间窗口内允许的最大请求数
            rateLimiter.trySetRate(RateType.OVERALL, count, timeInSeconds, RateIntervalUnit.SECONDS);
            
            // 尝试获取许可
            boolean acquired = rateLimiter.tryAcquire(1);
            
            if (!acquired) {
                log.warn("接口限流触发: key={}, count={}, time={}s", key, count, timeInSeconds);
                throw new BusinessException(ErrorCode.RATE_LIMIT_EXCEEDED.getCode(), rateLimit.message());
            }
            
            log.debug("限流检查通过: key={}, 剩余许可数: {}", key, rateLimiter.availablePermits());
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("限流检查异常: key={}", key, e);
            // 限流组件异常时不阻断业务
        }
    }

    /**
     * 生成限流key
     */
    private String getCombineKey(RateLimit rateLimit, JoinPoint point) {
        StringBuilder key = new StringBuilder(RATE_LIMIT_KEY_PREFIX);
        
        // 添加自定义前缀
        if (!rateLimit.key().isEmpty()) {
            key.append(rateLimit.key()).append(":");
        }

        switch (rateLimit.limitType()) {
            case IP:
                key.append("ip:").append(getClientIP());
                break;
            case USER:
                key.append("user:").append(getCurrentUserId());
                break;
            case API:
                key.append("api:").append(getMethodKey(point));
                break;
            case CUSTOM:
                key.append("custom:").append(rateLimit.key());
                break;
            case DEFAULT:
            default:
                key.append("default:").append(getMethodKey(point));
                break;
        }

        return key.toString();
    }

    /**
     * 获取客户端IP
     */
    private String getClientIP() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return IpUtils.getClientIP(request);
            }
        } catch (Exception e) {
            log.warn("获取客户端IP失败", e);
        }
        return "unknown";
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                Object principal = authentication.getPrincipal();
                if (principal instanceof String) {
                    return (String) principal;
                }
                // 如果是自定义的用户详情对象，需要根据实际情况获取用户ID
                return principal.toString();
            }
        } catch (Exception e) {
            log.warn("获取当前用户ID失败", e);
        }
        return "anonymous";
    }

    /**
     * 获取方法标识
     */
    private String getMethodKey(JoinPoint point) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        return method.getDeclaringClass().getSimpleName() + "." + method.getName();
    }
}
