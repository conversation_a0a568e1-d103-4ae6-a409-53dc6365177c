package com.foxit.crm.common.exception;

/**
 * 错误码枚举
 * 统一定义系统中的错误码和错误信息
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public enum ErrorCode {

    // ========== 通用错误码 ==========
    SUCCESS(200, "操作成功"),
    SYSTEM_ERROR(500, "系统内部错误"),
    INVALID_PARAMETER(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "权限不足"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不支持"),
    CONFLICT(409, "资源冲突"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // ========== 业务错误码 ==========
    BUSINESS_ERROR(1000, "业务处理失败"),

    // ========== 用户相关错误码 (1100-1199) ==========
    USER_NOT_FOUND(1101, "用户不存在"),
    USER_ALREADY_EXISTS(1102, "用户已存在"),
    USER_DISABLED(1103, "用户已被禁用"),
    USER_PASSWORD_ERROR(1104, "密码错误"),
    USER_PASSWORD_WEAK(1105, "密码强度不足"),
    USER_EMAIL_EXISTS(1106, "邮箱已被使用"),
    USER_PHONE_EXISTS(1107, "手机号已被使用"),
    USER_LOGIN_FAILED(1108, "登录失败"),
    USER_TOKEN_EXPIRED(1109, "登录已过期"),
    USER_TOKEN_INVALID(1110, "登录凭证无效"),

    // ========== 角色相关错误码 (1200-1299) ==========
    ROLE_NOT_FOUND(1201, "角色不存在"),
    ROLE_ALREADY_EXISTS(1202, "角色已存在"),
    ROLE_IN_USE(1203, "角色正在使用中，无法删除"),
    ROLE_CODE_EXISTS(1204, "角色编码已存在"),
    ROLE_PERMISSION_DENIED(1205, "角色权限不足"),

    // ========== 权限相关错误码 (1300-1399) ==========
    PERMISSION_NOT_FOUND(1301, "权限不存在"),
    PERMISSION_ALREADY_EXISTS(1302, "权限已存在"),
    PERMISSION_CODE_EXISTS(1303, "权限编码已存在"),
    PERMISSION_DENIED(1304, "权限不足"),
    PERMISSION_HIERARCHY_ERROR(1305, "权限层级关系错误"),

    // ========== 产品线相关错误码 (1400-1499) ==========
    PRODUCT_LINE_NOT_FOUND(1401, "产品线不存在"),
    PRODUCT_LINE_ALREADY_EXISTS(1402, "产品线已存在"),
    PRODUCT_LINE_CODE_EXISTS(1403, "产品线编码已存在"),
    PRODUCT_LINE_IN_USE(1404, "产品线正在使用中，无法删除"),
    PRODUCT_LINE_OWNER_INVALID(1405, "产品线负责人无效"),

    // ========== 产品版本相关错误码 (1500-1599) ==========
    PRODUCT_VERSION_NOT_FOUND(1501, "产品版本不存在"),
    PRODUCT_VERSION_ALREADY_EXISTS(1502, "产品版本已存在"),
    PRODUCT_VERSION_STATUS_INVALID(1503, "产品版本状态无效"),
    PRODUCT_VERSION_CURRENT_CANNOT_DELETE(1504, "当前版本不能删除"),
    PRODUCT_VERSION_CURRENT_CANNOT_DEPRECATE(1505, "当前版本不能废弃"),

    // ========== 系统配置相关错误码 (1600-1699) ==========
    CONFIG_NOT_FOUND(1601, "配置项不存在"),
    CONFIG_KEY_EXISTS(1602, "配置键已存在"),
    CONFIG_VALUE_INVALID(1603, "配置值无效"),
    CONFIG_SYSTEM_READONLY(1604, "系统配置不能修改"),
    CONFIG_TYPE_MISMATCH(1605, "配置类型不匹配"),

    // ========== 操作日志相关错误码 (1700-1799) ==========
    OPERATION_LOG_NOT_FOUND(1701, "操作日志不存在"),
    OPERATION_LOG_EXPORT_FAILED(1702, "操作日志导出失败"),
    OPERATION_LOG_CLEANUP_FAILED(1703, "操作日志清理失败"),

    // ========== Dashboard相关错误码 (1800-1899) ==========
    DASHBOARD_CONFIG_NOT_FOUND(1801, "Dashboard配置不存在"),
    DASHBOARD_DATA_NOT_FOUND(1802, "Dashboard数据不存在"),
    DASHBOARD_PERMISSION_DENIED(1803, "Dashboard权限不足"),
    DASHBOARD_CACHE_ERROR(1804, "Dashboard缓存错误"),
    DASHBOARD_QUERY_INVALID(1805, "Dashboard查询参数无效"),

    // ========== 数据权限相关错误码 (1900-1999) ==========
    DATA_PERMISSION_DENIED(1901, "数据权限不足"),
    DATA_SCOPE_INVALID(1902, "数据范围无效"),
    DATA_FILTER_ERROR(1903, "数据过滤错误"),

    // ========== 缓存相关错误码 (2000-2099) ==========
    CACHE_ERROR(2001, "缓存操作失败"),
    CACHE_KEY_INVALID(2002, "缓存键无效"),
    CACHE_EXPIRED(2003, "缓存已过期"),

    // ========== 文件相关错误码 (2100-2199) ==========
    FILE_NOT_FOUND(2101, "文件不存在"),
    FILE_UPLOAD_FAILED(2102, "文件上传失败"),
    FILE_DOWNLOAD_FAILED(2103, "文件下载失败"),
    FILE_TYPE_NOT_SUPPORTED(2104, "文件类型不支持"),
    FILE_SIZE_EXCEEDED(2105, "文件大小超出限制"),

    // ========== 数据库相关错误码 (2200-2299) ==========
    DATABASE_ERROR(2201, "数据库操作失败"),
    DATABASE_CONNECTION_ERROR(2202, "数据库连接失败"),
    DATABASE_CONSTRAINT_VIOLATION(2203, "数据库约束违反"),
    DATABASE_DUPLICATE_KEY(2204, "数据库主键冲突"),

    // ========== 外部服务相关错误码 (2300-2399) ==========
    EXTERNAL_SERVICE_ERROR(2301, "外部服务调用失败"),
    EXTERNAL_SERVICE_TIMEOUT(2302, "外部服务调用超时"),
    EXTERNAL_SERVICE_UNAVAILABLE(2303, "外部服务不可用"),

    // ========== 限流相关错误码 (2400-2499) ==========
    RATE_LIMIT_EXCEEDED(2401, "请求频率超出限制"),
    CONCURRENT_LIMIT_EXCEEDED(2402, "并发数超出限制"),

    // ========== 数据验证相关错误码 (2500-2599) ==========
    DATA_VALIDATION_FAILED(2501, "数据验证失败"),
    DATA_FORMAT_ERROR(2502, "数据格式错误"),
    DATA_RANGE_ERROR(2503, "数据范围错误"),
    DATA_INTEGRITY_ERROR(2504, "数据完整性错误");

    private final int code;
    private final String message;

    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 根据错误码获取错误信息
     */
    public static String getMessageByCode(int code) {
        for (ErrorCode errorCode : ErrorCode.values()) {
            if (errorCode.getCode() == code) {
                return errorCode.getMessage();
            }
        }
        return "未知错误";
    }

    /**
     * 根据错误码获取ErrorCode枚举
     */
    public static ErrorCode getByCode(int code) {
        for (ErrorCode errorCode : ErrorCode.values()) {
            if (errorCode.getCode() == code) {
                return errorCode;
            }
        }
        return SYSTEM_ERROR;
    }
}
