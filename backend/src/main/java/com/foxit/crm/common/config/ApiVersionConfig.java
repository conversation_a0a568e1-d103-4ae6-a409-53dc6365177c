package com.foxit.crm.common.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.http.MediaType;

/**
 * API版本控制配置
 * 支持通过Header、URL路径、请求参数等方式进行版本控制
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Configuration
public class ApiVersionConfig implements WebMvcConfigurer {

    /**
     * 配置内容协商
     * 支持通过Accept Header进行版本控制
     */
    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        configurer
                // 启用参数支持
                .favorParameter(true)
                .parameterName("version")
                // 启用Header支持
                .ignoreAcceptHeader(false)
                // 默认内容类型
                .defaultContentType(MediaType.APPLICATION_JSON)
                // 支持的媒体类型
                .mediaType("json", MediaType.APPLICATION_JSON)
                .mediaType("xml", MediaType.APPLICATION_XML);
    }
}
