package com.foxit.crm.common.aspect;

import com.foxit.crm.common.annotation.DataPermission;
import com.foxit.crm.common.datapermission.DataPermissionHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 数据权限切面
 * 在查询方法执行前后处理数据权限逻辑
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class DataPermissionAspect {

    private final DataPermissionHandler dataPermissionHandler;

    @Around("@annotation(com.foxit.crm.common.annotation.DataPermission)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        DataPermission dataPermission = AnnotationUtils.findAnnotation(method, DataPermission.class);

        if (dataPermission == null || !dataPermission.enabled()) {
            return point.proceed();
        }

        // 在ThreadLocal中设置数据权限信息，供MyBatis拦截器使用
        String permissionCondition = dataPermissionHandler.buildDataPermissionCondition(dataPermission);
        DataPermissionContext.setDataPermission(dataPermission);
        DataPermissionContext.setPermissionCondition(permissionCondition);

        try {
            log.debug("应用数据权限: method={}, condition={}", method.getName(), permissionCondition);
            Object result = point.proceed();
            
            // 如果需要对结果进行字段过滤，可以在这里处理
            return result;
        } finally {
            // 清理ThreadLocal
            DataPermissionContext.clear();
        }
    }

    /**
     * 数据权限上下文
     * 使用ThreadLocal存储当前线程的数据权限信息
     */
    public static class DataPermissionContext {
        private static final ThreadLocal<DataPermission> DATA_PERMISSION_HOLDER = new ThreadLocal<>();
        private static final ThreadLocal<String> PERMISSION_CONDITION_HOLDER = new ThreadLocal<>();

        public static void setDataPermission(DataPermission dataPermission) {
            DATA_PERMISSION_HOLDER.set(dataPermission);
        }

        public static DataPermission getDataPermission() {
            return DATA_PERMISSION_HOLDER.get();
        }

        public static void setPermissionCondition(String condition) {
            PERMISSION_CONDITION_HOLDER.set(condition);
        }

        public static String getPermissionCondition() {
            return PERMISSION_CONDITION_HOLDER.get();
        }

        public static void clear() {
            DATA_PERMISSION_HOLDER.remove();
            PERMISSION_CONDITION_HOLDER.remove();
        }

        public static boolean hasDataPermission() {
            return DATA_PERMISSION_HOLDER.get() != null;
        }
    }
}
