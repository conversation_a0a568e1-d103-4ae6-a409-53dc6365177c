package com.foxit.crm.common.controller;

import com.foxit.crm.common.clickhouse.ClickHouseTemplate;
import com.foxit.crm.common.exception.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * ClickHouse健康检查控制器
 * 提供ClickHouse数据库连接状态检查接口
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@RestController
@RequestMapping("/admin/clickhouse")
@RequiredArgsConstructor
public class ClickHouseHealthController {

    private final ClickHouseTemplate clickHouseTemplate;

    /**
     * ClickHouse健康检查
     *
     * @return 健康检查结果
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> healthCheck() {
        log.info("执行ClickHouse健康检查");

        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("checkTime", LocalDateTime.now());

        try {
            // 测试连接
            boolean isConnected = clickHouseTemplate.testConnection();
            healthInfo.put("status", isConnected ? "UP" : "DOWN");

            if (isConnected) {
                // 获取版本信息
                String version = clickHouseTemplate.getVersion();
                healthInfo.put("version", version);

                // 获取数据库信息
                try {
                    String currentDatabase = clickHouseTemplate.queryForObject("SELECT currentDatabase()",
                            String.class);
                    healthInfo.put("database", currentDatabase);
                } catch (Exception e) {
                    log.warn("获取当前数据库名称失败", e);
                    healthInfo.put("database", "unknown");
                }

                // 测试查询性能
                long startTime = System.currentTimeMillis();
                clickHouseTemplate.queryForObject("SELECT count() FROM system.tables", Long.class);
                long queryTime = System.currentTimeMillis() - startTime;
                healthInfo.put("queryTime", queryTime + "ms");

                return Result.success(healthInfo);
            } else {
                healthInfo.put("error", "连接失败");
                Result<Map<String, Object>> result = Result.error("ClickHouse连接失败");
                result.setData(healthInfo);
                return result;
            }

        } catch (Exception e) {
            log.error("ClickHouse健康检查失败", e);
            healthInfo.put("status", "ERROR");
            healthInfo.put("error", e.getMessage());
            Result<Map<String, Object>> result = Result.error("ClickHouse健康检查异常: " + e.getMessage());
            result.setData(healthInfo);
            return result;
        }
    }

    /**
     * 获取ClickHouse表信息
     *
     * @return 表信息列表
     */
    @GetMapping("/tables")
    public Result<Object> getTables() {
        log.info("获取ClickHouse表信息");

        try {
            String sql = """
                    SELECT
                        database,
                        name as table_name,
                        engine,
                        total_rows,
                        total_bytes,
                        formatReadableSize(total_bytes) as size
                    FROM system.tables
                    WHERE database = 'ck_dev'
                    ORDER BY total_bytes DESC
                    """;

            var tables = clickHouseTemplate.queryForList(sql);
            return Result.success(tables);

        } catch (Exception e) {
            log.error("获取ClickHouse表信息失败", e);
            return Result.error("获取表信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取ClickHouse系统信息
     *
     * @return 系统信息
     */
    @GetMapping("/system-info")
    public Result<Map<String, Object>> getSystemInfo() {
        log.info("获取ClickHouse系统信息");

        try {
            Map<String, Object> systemInfo = new HashMap<>();

            // 基本信息
            systemInfo.put("version", clickHouseTemplate.getVersion());
            systemInfo.put("uptime", clickHouseTemplate.queryForObject("SELECT uptime()", Long.class));

            // 内存使用
            String memoryUsage = clickHouseTemplate.queryForObject(
                    "SELECT formatReadableSize(memory_usage) FROM system.processes WHERE query != ''",
                    String.class);
            systemInfo.put("memoryUsage", memoryUsage);

            // 当前连接数
            Long connections = clickHouseTemplate.queryForObject(
                    "SELECT count() FROM system.processes", Long.class);
            systemInfo.put("connections", connections);

            // 查询统计
            Map<String, Object> queryStats = new HashMap<>();
            queryStats.put("running", clickHouseTemplate.queryForObject(
                    "SELECT count() FROM system.processes WHERE query != ''", Long.class));
            queryStats.put("total", clickHouseTemplate.queryForObject(
                    "SELECT count() FROM system.query_log WHERE event_date = today()", Long.class));
            systemInfo.put("queries", queryStats);

            return Result.success(systemInfo);

        } catch (Exception e) {
            log.error("获取ClickHouse系统信息失败", e);
            return Result.error("获取系统信息失败: " + e.getMessage());
        }
    }
}
