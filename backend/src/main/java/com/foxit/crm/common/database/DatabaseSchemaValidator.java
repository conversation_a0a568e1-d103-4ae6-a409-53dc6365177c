package com.foxit.crm.common.database;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 数据库表结构验证器
 * 验证和优化MySQL数据库表结构
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@Component
@Order(50) // 在ClickHouse初始化之前执行
@RequiredArgsConstructor
public class DatabaseSchemaValidator implements CommandLineRunner {

    @Qualifier("mysqlJdbcTemplate")
    private final JdbcTemplate mysqlJdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始验证MySQL数据库表结构...");

        try {
            // 验证数据库连接
            if (!testMySQLConnection()) {
                log.warn("MySQL连接失败，跳过表结构验证");
                return;
            }

            // 验证现有表结构
            validateExistingTables();

            // 注意：表结构创建请手动执行 mysql_tables_schema.sql
            // 模拟数据导入请手动执行 mysql_mock_data.sql
            log.info("请手动执行SQL文件：mysql_tables_schema.sql 和 mysql_mock_data.sql");

            // 验证表结构完整性
            validateTableIntegrity();

            log.info("MySQL数据库表结构验证完成");

        } catch (Exception e) {
            log.error("MySQL数据库表结构验证失败", e);
            // 不抛出异常，避免影响应用启动
        }
    }

    /**
     * 测试MySQL连接
     */
    private boolean testMySQLConnection() {
        try {
            mysqlJdbcTemplate.queryForObject("SELECT 1", Integer.class);
            log.info("MySQL连接测试成功");
            return true;
        } catch (Exception e) {
            log.error("MySQL连接测试失败", e);
            return false;
        }
    }

    /**
     * 验证现有表结构
     */
    private void validateExistingTables() {
        log.info("验证现有表结构...");

        try {
            // 获取所有表
            String sql = "SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE()";
            List<String> tables = mysqlJdbcTemplate.queryForList(sql, String.class);

            log.info("当前数据库包含 {} 张表: {}", tables.size(), tables);

            // 验证核心表是否存在
            String[] requiredTables = {
                    "sys_user", "sys_role", "sys_permission", "sys_user_role",
                    "sys_role_permission", "sys_operation_log", "sys_config",
                    "product_line", "product_version"
            };

            for (String table : requiredTables) {
                if (tables.contains(table)) {
                    log.debug("核心表 {} 存在", table);
                } else {
                    log.warn("核心表 {} 不存在", table);
                }
            }

        } catch (Exception e) {
            log.error("验证现有表结构失败", e);
        }
    }

    /**
     * 验证表结构完整性
     */
    private void validateTableIntegrity() {
        log.info("验证表结构完整性...");

        try {
            // 验证新增表是否创建成功
            String[] newTables = {
                    "user_login_records", "user_segments", "user_segment_members",
                    "product_usage_stats", "data_permission_config", "feature_config"
            };

            for (String table : newTables) {
                try {
                    String sql = "SELECT COUNT(*) FROM " + table;
                    Long count = mysqlJdbcTemplate.queryForObject(sql, Long.class);
                    log.info("表 {} 验证成功，当前记录数: {}", table, count);
                } catch (Exception e) {
                    log.warn("表 {} 验证失败: {}", table, e.getMessage());
                }
            }

            // 验证索引是否创建成功
            validateIndexes();

        } catch (Exception e) {
            log.error("验证表结构完整性失败", e);
        }
    }

    /**
     * 验证索引
     */
    private void validateIndexes() {
        try {
            String sql = """
                    SELECT
                        TABLE_NAME,
                        INDEX_NAME,
                        COLUMN_NAME
                    FROM information_schema.STATISTICS
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND INDEX_NAME != 'PRIMARY'
                    ORDER BY TABLE_NAME, INDEX_NAME
                    """;

            List<Map<String, Object>> indexes = mysqlJdbcTemplate.queryForList(sql);
            log.info("数据库索引总数: {}", indexes.size());

            // 按表分组统计索引
            indexes.stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                            index -> index.get("TABLE_NAME").toString()))
                    .forEach((table, tableIndexes) -> log.debug("表 {} 包含 {} 个索引", table, tableIndexes.size()));

        } catch (Exception e) {
            log.error("验证索引失败", e);
        }
    }

    /**
     * 获取数据库版本信息
     */
    public String getDatabaseVersion() {
        try {
            return mysqlJdbcTemplate.queryForObject("SELECT VERSION()", String.class);
        } catch (Exception e) {
            log.error("获取数据库版本失败", e);
            return "Unknown";
        }
    }

    /**
     * 获取数据库统计信息
     */
    public Map<String, Object> getDatabaseStats() {
        try {
            String sql = """
                    SELECT
                        COUNT(*) as table_count,
                        SUM(TABLE_ROWS) as total_rows,
                        SUM(DATA_LENGTH + INDEX_LENGTH) as total_size
                    FROM information_schema.TABLES
                    WHERE TABLE_SCHEMA = DATABASE()
                    """;

            List<Map<String, Object>> result = mysqlJdbcTemplate.queryForList(sql);
            return result.isEmpty() ? Map.of() : result.get(0);

        } catch (Exception e) {
            log.error("获取数据库统计信息失败", e);
            return Map.of();
        }
    }
}
