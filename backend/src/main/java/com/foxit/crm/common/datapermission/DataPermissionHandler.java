package com.foxit.crm.common.datapermission;

import com.foxit.crm.common.annotation.DataPermission;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 数据权限处理器
 * 根据用户权限动态生成SQL条件和字段过滤
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Component
public class DataPermissionHandler {

    /**
     * 构建数据权限SQL条件
     */
    public String buildDataPermissionCondition(DataPermission dataPermission) {
        if (!dataPermission.enabled()) {
            return "";
        }

        // 获取当前用户信息
        UserPermissionInfo userInfo = getCurrentUserPermissionInfo();
        if (userInfo == null) {
            log.warn("无法获取当前用户权限信息，拒绝数据访问");
            return "1 = 0"; // 拒绝所有数据访问
        }

        // 如果是超级管理员，不限制数据权限
        if (userInfo.isSuperAdmin()) {
            return "";
        }

        StringBuilder condition = new StringBuilder();
        String tableAlias = dataPermission.tableAlias().isEmpty() ? "" : dataPermission.tableAlias() + ".";

        switch (dataPermission.dataScope()) {
            case ALL:
                // 全部数据权限，不添加条件
                break;

            case SELF:
                // 仅本人数据
                condition.append(tableAlias)
                        .append(dataPermission.userColumn())
                        .append(" = ")
                        .append(userInfo.getUserId());
                break;

            case DEPT:
                // 部门数据权限
                if (userInfo.getDeptId() != null) {
                    condition.append(tableAlias)
                            .append(dataPermission.deptColumn())
                            .append(" = ")
                            .append(userInfo.getDeptId());
                }
                break;

            case DEPT_AND_CHILD:
                // 部门及子部门数据权限
                List<Long> deptIds = userInfo.getDeptAndChildIds();
                if (!deptIds.isEmpty()) {
                    condition.append(tableAlias)
                            .append(dataPermission.deptColumn())
                            .append(" IN (")
                            .append(deptIds.stream().map(String::valueOf).collect(Collectors.joining(",")))
                            .append(")");
                }
                break;

            case PRODUCT_LINE:
                // 产品线数据权限
                List<Long> productLineIds = userInfo.getProductLineIds();
                if (!productLineIds.isEmpty()) {
                    condition.append(tableAlias)
                            .append(dataPermission.productLineColumn())
                            .append(" IN (")
                            .append(productLineIds.stream().map(String::valueOf).collect(Collectors.joining(",")))
                            .append(")");
                }
                break;

            case PRODUCT_LINE_AND_RELATED:
                // 产品线及关联数据权限
                List<Long> allProductLineIds = userInfo.getAllRelatedProductLineIds();
                if (!allProductLineIds.isEmpty()) {
                    condition.append(tableAlias)
                            .append(dataPermission.productLineColumn())
                            .append(" IN (")
                            .append(allProductLineIds.stream().map(String::valueOf).collect(Collectors.joining(",")))
                            .append(")");
                }
                break;

            case CUSTOM:
                // 自定义权限表达式
                if (!dataPermission.customExpression().isEmpty()) {
                    condition.append(processCustomExpression(dataPermission.customExpression(), userInfo));
                }
                break;

            default:
                log.warn("未知的数据权限类型: {}", dataPermission.dataScope());
                break;
        }

        String result = condition.toString();
        log.debug("构建数据权限条件: {}", result);
        return result;
    }

    /**
     * 过滤字段（列级权限控制）
     */
    public Set<String> filterColumns(DataPermission dataPermission, Set<String> originalColumns) {
        if (!dataPermission.enabled()) {
            return originalColumns;
        }

        Set<String> filteredColumns = originalColumns;

        // 排除指定字段
        if (dataPermission.excludeColumns().length > 0) {
            Set<String> excludeSet = Arrays.stream(dataPermission.excludeColumns()).collect(Collectors.toSet());
            filteredColumns = filteredColumns.stream()
                    .filter(column -> !excludeSet.contains(column))
                    .collect(Collectors.toSet());
        }

        // 如果指定了包含字段，则只返回这些字段
        if (dataPermission.includeColumns().length > 0) {
            Set<String> includeSet = Arrays.stream(dataPermission.includeColumns()).collect(Collectors.toSet());
            filteredColumns = filteredColumns.stream()
                    .filter(includeSet::contains)
                    .collect(Collectors.toSet());
        }

        return filteredColumns;
    }

    /**
     * 获取当前用户权限信息
     */
    private UserPermissionInfo getCurrentUserPermissionInfo() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                // 这里需要根据实际的用户认证体系来获取用户权限信息
                // 暂时返回模拟数据
                return UserPermissionInfo.builder()
                        .userId(1L)
                        .username("admin")
                        .deptId(1L)
                        .superAdmin(false)
                        .deptAndChildIds(List.of(1L, 2L, 3L))
                        .productLineIds(List.of(1L, 2L))
                        .allRelatedProductLineIds(List.of(1L, 2L, 3L, 4L))
                        .build();
            }
        } catch (Exception e) {
            log.error("获取用户权限信息失败", e);
        }
        return null;
    }

    /**
     * 处理自定义权限表达式
     */
    private String processCustomExpression(String expression, UserPermissionInfo userInfo) {
        // 替换表达式中的占位符
        return expression
                .replace("#{userId}", String.valueOf(userInfo.getUserId()))
                .replace("#{deptId}", String.valueOf(userInfo.getDeptId()))
                .replace("#{username}", "'" + userInfo.getUsername() + "'");
    }

    /**
     * 用户权限信息
     */
    public static class UserPermissionInfo {
        private Long userId;
        private String username;
        private Long deptId;
        private boolean superAdmin;
        private List<Long> deptAndChildIds;
        private List<Long> productLineIds;
        private List<Long> allRelatedProductLineIds;

        public static UserPermissionInfoBuilder builder() {
            return new UserPermissionInfoBuilder();
        }

        public static class UserPermissionInfoBuilder {
            private Long userId;
            private String username;
            private Long deptId;
            private boolean superAdmin;
            private List<Long> deptAndChildIds;
            private List<Long> productLineIds;
            private List<Long> allRelatedProductLineIds;

            public UserPermissionInfoBuilder userId(Long userId) {
                this.userId = userId;
                return this;
            }

            public UserPermissionInfoBuilder username(String username) {
                this.username = username;
                return this;
            }

            public UserPermissionInfoBuilder deptId(Long deptId) {
                this.deptId = deptId;
                return this;
            }

            public UserPermissionInfoBuilder superAdmin(boolean superAdmin) {
                this.superAdmin = superAdmin;
                return this;
            }

            public UserPermissionInfoBuilder deptAndChildIds(List<Long> deptAndChildIds) {
                this.deptAndChildIds = deptAndChildIds;
                return this;
            }

            public UserPermissionInfoBuilder productLineIds(List<Long> productLineIds) {
                this.productLineIds = productLineIds;
                return this;
            }

            public UserPermissionInfoBuilder allRelatedProductLineIds(List<Long> allRelatedProductLineIds) {
                this.allRelatedProductLineIds = allRelatedProductLineIds;
                return this;
            }

            public UserPermissionInfo build() {
                UserPermissionInfo info = new UserPermissionInfo();
                info.userId = this.userId;
                info.username = this.username;
                info.deptId = this.deptId;
                info.superAdmin = this.superAdmin;
                info.deptAndChildIds = this.deptAndChildIds;
                info.productLineIds = this.productLineIds;
                info.allRelatedProductLineIds = this.allRelatedProductLineIds;
                return info;
            }
        }

        // Getters
        public Long getUserId() { return userId; }
        public String getUsername() { return username; }
        public Long getDeptId() { return deptId; }
        public boolean isSuperAdmin() { return superAdmin; }
        public List<Long> getDeptAndChildIds() { return deptAndChildIds; }
        public List<Long> getProductLineIds() { return productLineIds; }
        public List<Long> getAllRelatedProductLineIds() { return allRelatedProductLineIds; }
    }
}
