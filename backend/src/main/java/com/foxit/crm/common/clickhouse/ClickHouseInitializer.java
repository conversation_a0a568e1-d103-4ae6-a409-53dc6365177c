package com.foxit.crm.common.clickhouse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import java.nio.charset.StandardCharsets;

/**
 * ClickHouse数据库初始化器
 * 在应用启动时自动执行ClickHouse表结构初始化
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@Component
@Order(100) // 确保在其他初始化完成后执行
@RequiredArgsConstructor
public class ClickHouseInitializer implements CommandLineRunner {

    private final ClickHouseTemplate clickHouseTemplate;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始初始化ClickHouse数据库...");

        try {
            // 测试连接
            if (!clickHouseTemplate.testConnection()) {
                log.warn("ClickHouse连接失败，跳过初始化");
                return;
            }

            // 验证ClickHouse连接
            if (!clickHouseTemplate.validateClickHouseConnection()) {
                log.error("ClickHouse连接验证失败，可能连接到了错误的数据库");
                return;
            }

            String version = clickHouseTemplate.getVersion();
            log.info("ClickHouse连接成功，版本: {}", version);

            // 执行初始化脚本
            executeInitScript();

            log.info("ClickHouse数据库初始化完成");

        } catch (Exception e) {
            log.error("ClickHouse数据库初始化失败", e);
            // 不抛出异常，避免影响应用启动
        }
    }

    /**
     * 执行初始化脚本
     */
    private void executeInitScript() {
        try {
            // 读取ClickHouse表结构脚本
            ClassPathResource resource = new ClassPathResource("sql/clickhouse_tables_schema.sql");
            String script = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);

            // 分割SQL语句并执行
            String[] statements = script.split(";");

            for (String statement : statements) {
                String trimmedStatement = statement.trim();
                if (!trimmedStatement.isEmpty() && !trimmedStatement.startsWith("--")) {
                    try {
                        clickHouseTemplate.execute(trimmedStatement);
                        log.debug("执行ClickHouse语句成功: {}",
                                trimmedStatement.substring(0, Math.min(50, trimmedStatement.length())));
                    } catch (Exception e) {
                        // 检查是否是表已存在的错误
                        if (e.getMessage() != null && e.getMessage().contains("TABLE_ALREADY_EXISTS")) {
                            log.debug("表已存在，跳过创建: {}",
                                    trimmedStatement.substring(0, Math.min(50, trimmedStatement.length())));
                        }
                        // 检查是否是索引已存在的错误
                        else if (e.getMessage() != null && (e.getMessage().contains("index with this name already exists")
                                || e.getMessage().contains("ILLEGAL_COLUMN"))) {
                            log.debug("索引已存在，跳过创建: {}",
                                    trimmedStatement.substring(0, Math.min(50, trimmedStatement.length())));
                        }
                        // 检查是否是物化视图已存在的错误
                        else if (e.getMessage() != null && e.getMessage().contains("already exists")) {
                            log.debug("对象已存在，跳过创建: {}",
                                    trimmedStatement.substring(0, Math.min(50, trimmedStatement.length())));
                        }
                        else {
                            // 其他错误记录警告但不中断
                            log.warn("执行ClickHouse语句失败: {}, 错误: {}",
                                    trimmedStatement.substring(0, Math.min(50, trimmedStatement.length())),
                                    e.getMessage());
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("读取或执行ClickHouse初始化脚本失败", e);
            // 不抛出异常，避免影响应用启动
            log.warn("ClickHouse初始化失败，但不影响应用启动");
        }
    }
}
