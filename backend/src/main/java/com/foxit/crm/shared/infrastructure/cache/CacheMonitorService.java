package com.foxit.crm.shared.infrastructure.cache;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RKeys;
import org.redisson.api.RedissonClient;
import org.springframework.cache.CacheManager;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 缓存监控服务
 * 监控缓存的使用情况和性能指标
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CacheMonitorService {

    private final RedissonClient redissonClient;
    private final CacheManager cacheManager;

    /**
     * 定时监控缓存状态（每5分钟执行一次）
     */
    @Scheduled(fixedRate = 5 * 60 * 1000)
    public void monitorCacheStatus() {
        try {
            logCacheStatistics();
        } catch (Exception e) {
            log.error("缓存监控失败", e);
        }
    }

    /**
     * 记录缓存统计信息
     */
    public void logCacheStatistics() {
        log.info("=== 缓存监控报告 ===");
        
        // Redis统计信息
        logRedisStatistics();
        
        // Spring Cache统计信息
        logSpringCacheStatistics();
        
        log.info("=== 缓存监控报告结束 ===");
    }

    /**
     * 记录Redis统计信息
     */
    private void logRedisStatistics() {
        try {
            RKeys keys = redissonClient.getKeys();
            long totalKeys = keys.count();
            
            log.info("Redis统计信息:");
            log.info("  总键数: {}", totalKeys);
            
            // 按前缀统计键数量
            Map<String, Long> keysByPrefix = getKeyCountByPrefix();
            keysByPrefix.forEach((prefix, count) -> 
                log.info("  {} 键数: {}", prefix, count));
                
        } catch (Exception e) {
            log.error("获取Redis统计信息失败", e);
        }
    }

    /**
     * 记录Spring Cache统计信息
     */
    private void logSpringCacheStatistics() {
        try {
            Collection<String> cacheNames = cacheManager.getCacheNames();
            
            log.info("Spring Cache统计信息:");
            log.info("  缓存数量: {}", cacheNames.size());
            
            for (String cacheName : cacheNames) {
                org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
                if (cache != null) {
                    log.info("  缓存 {}: 已配置", cacheName);
                }
            }
        } catch (Exception e) {
            log.error("获取Spring Cache统计信息失败", e);
        }
    }

    /**
     * 按前缀统计键数量
     */
    private Map<String, Long> getKeyCountByPrefix() {
        Map<String, Long> result = new HashMap<>();
        
        try {
            RKeys keys = redissonClient.getKeys();
            
            // 统计各个前缀的键数量
            result.put("scrm:token", keys.countExists("scrm:token:*"));
            result.put("scrm:user", keys.countExists("scrm:user:*"));
            result.put("scrm:config", keys.countExists("scrm:config:*"));
            result.put("scrm:permission", keys.countExists("scrm:permission:*"));
            result.put("scrm:product", keys.countExists("scrm:product:*"));
            result.put("scrm:data", keys.countExists("scrm:data:*"));
            result.put("scrm:log", keys.countExists("scrm:log:*"));
            
        } catch (Exception e) {
            log.error("统计键前缀失败", e);
        }
        
        return result;
    }

    /**
     * 获取缓存健康状态
     */
    public Map<String, Object> getCacheHealthStatus() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            // Redis连接状态
            boolean redisConnected = redissonClient.getKeys().count() >= 0;
            status.put("redisConnected", redisConnected);
            
            // 总键数
            long totalKeys = redissonClient.getKeys().count();
            status.put("totalKeys", totalKeys);
            
            // 按前缀统计
            status.put("keysByPrefix", getKeyCountByPrefix());
            
            // Spring Cache状态
            Collection<String> cacheNames = cacheManager.getCacheNames();
            status.put("springCacheNames", cacheNames);
            status.put("springCacheCount", cacheNames.size());
            
            status.put("healthy", true);
            
        } catch (Exception e) {
            log.error("获取缓存健康状态失败", e);
            status.put("healthy", false);
            status.put("error", e.getMessage());
        }
        
        return status;
    }

    /**
     * 清理过期的缓存键
     */
    public void cleanupExpiredKeys() {
        log.info("开始清理过期缓存键...");
        
        try {
            RKeys keys = redissonClient.getKeys();
            
            // 这里可以添加特定的清理逻辑
            // 例如清理特定模式的过期键
            
            log.info("过期缓存键清理完成");
        } catch (Exception e) {
            log.error("清理过期缓存键失败", e);
        }
    }

    /**
     * 获取缓存性能指标
     */
    public Map<String, Object> getCacheMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        try {
            // 基本指标
            RKeys keys = redissonClient.getKeys();
            metrics.put("totalKeys", keys.count());
            
            // 内存使用情况（如果Redis支持）
            // 这里可以添加更多性能指标的收集
            
            // 缓存命中率等指标需要在应用层面统计
            
        } catch (Exception e) {
            log.error("获取缓存性能指标失败", e);
            metrics.put("error", e.getMessage());
        }
        
        return metrics;
    }

    /**
     * 检查缓存是否正常工作
     */
    public boolean isCacheHealthy() {
        try {
            // 测试Redis连接
            redissonClient.getKeys().count();
            
            // 测试Spring Cache
            cacheManager.getCacheNames();
            
            return true;
        } catch (Exception e) {
            log.error("缓存健康检查失败", e);
            return false;
        }
    }

    /**
     * 获取缓存配置信息
     */
    public Map<String, Object> getCacheConfiguration() {
        Map<String, Object> config = new HashMap<>();
        
        try {
            // Spring Cache配置
            Collection<String> cacheNames = cacheManager.getCacheNames();
            config.put("springCacheNames", cacheNames);
            
            // Redisson配置信息
            config.put("redissonClientType", redissonClient.getClass().getSimpleName());
            
            // 可以添加更多配置信息
            
        } catch (Exception e) {
            log.error("获取缓存配置信息失败", e);
            config.put("error", e.getMessage());
        }
        
        return config;
    }
}
