package com.foxit.crm.shared.infrastructure.security;

import com.foxit.crm.modules.system.domain.model.aggregate.User;
import com.foxit.crm.modules.system.domain.model.valueobject.Username;
import com.foxit.crm.modules.system.domain.repository.UserRepository;
import com.foxit.crm.modules.system.domain.repository.PermissionRepository;
import com.foxit.crm.modules.system.domain.model.aggregate.Permission;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 用户详情服务实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserRepository userRepository;
    private final PermissionRepository permissionRepository;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        Username usernameVO = new Username(username);
        Optional<User> userOpt = userRepository.findByUsername(usernameVO);

        if (userOpt.isEmpty()) {
            throw new UsernameNotFoundException("用户不存在: " + username);
        }

        User user = userOpt.get();

        if (!user.isEnabled()) {
            throw new UsernameNotFoundException("用户已被禁用: " + username);
        }

        // 构建权限列表
        List<SimpleGrantedAuthority> authorities = new ArrayList<>();

        // 根据用户类型设置基础权限（不使用ROLE_前缀）
        if (user.isAdmin()) {
            authorities.add(new SimpleGrantedAuthority("ADMIN"));
            authorities.add(new SimpleGrantedAuthority("USER"));

            // 管理员拥有所有Dashboard权限
            authorities.add(new SimpleGrantedAuthority("dashboard"));
            authorities.add(new SimpleGrantedAuthority("dashboard:overview"));
            authorities.add(new SimpleGrantedAuthority("dashboard:product"));
            authorities.add(new SimpleGrantedAuthority("dashboard:realtime"));
            authorities.add(new SimpleGrantedAuthority("dashboard:config"));

            // 管理员拥有所有用户分析权限
            authorities.add(new SimpleGrantedAuthority("user_analysis"));
            authorities.add(new SimpleGrantedAuthority("user:active"));
            authorities.add(new SimpleGrantedAuthority("user:growth"));
            authorities.add(new SimpleGrantedAuthority("user:behavior"));

            // 管理员拥有所有行为分析权限
            authorities.add(new SimpleGrantedAuthority("behavior_analysis"));

            // 管理员拥有所有商业变现权限
            authorities.add(new SimpleGrantedAuthority("business_analysis"));

            // 管理员拥有所有系统管理权限
            authorities.add(new SimpleGrantedAuthority("system_management"));
            authorities.add(new SimpleGrantedAuthority("system:user"));
            authorities.add(new SimpleGrantedAuthority("system:role"));
            authorities.add(new SimpleGrantedAuthority("system:permission"));
            authorities.add(new SimpleGrantedAuthority("system:config"));
            authorities.add(new SimpleGrantedAuthority("system:log"));
        } else {
            authorities.add(new SimpleGrantedAuthority("USER"));

            // 普通用户从数据库加载具体权限
            try {
                List<Permission> permissions = permissionRepository.findByUserId(user.getUserId().getValue());
                for (Permission permission : permissions) {
                    authorities.add(new SimpleGrantedAuthority(permission.getPermissionCode()));
                }
            } catch (Exception e) {
                // 如果权限加载失败，记录日志但不影响登录
                System.err.println("加载用户权限失败: " + e.getMessage());
            }
        }

        return org.springframework.security.core.userdetails.User.builder()
                .username(user.getUsername().getValue())
                .password(user.getPassword().getEncodedValue())
                .authorities(authorities)
                .accountExpired(false)
                .accountLocked(!user.isEnabled())
                .credentialsExpired(false)
                .disabled(!user.isEnabled())
                .build();
    }
}
