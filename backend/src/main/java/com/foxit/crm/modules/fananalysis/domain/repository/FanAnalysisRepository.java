package com.foxit.crm.modules.fananalysis.domain.repository;

import com.foxit.crm.modules.fananalysis.domain.entity.FanAggregate;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;

import java.util.List;
import java.util.Optional;

/**
 * 粉丝分析仓储接口
 * 定义粉丝分析数据的查询和存储操作
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface FanAnalysisRepository {

    /**
     * 获取粉丝总览数据
     *
     * @param timeRange 时间范围
     * @param platforms 平台列表，为空时查询所有平台
     * @param source 来源筛选
     * @param fanType 粉丝类型筛选
     * @param dataScope 数据权限范围
     * @return 粉丝总览聚合根
     */
    Optional<FanAggregate> getFanOverview(TimeRange timeRange, List<String> platforms, 
                                         String source, String fanType, String dataScope);

    /**
     * 获取粉丝增长分析数据
     *
     * @param timeRange 时间范围
     * @param platforms 平台列表
     * @param dataScope 数据权限范围
     * @return 粉丝增长聚合根
     */
    Optional<FanAggregate> getFanGrowthAnalysis(TimeRange timeRange, List<String> platforms, String dataScope);

    /**
     * 获取粉丝活跃度分析数据
     *
     * @param timeRange 时间范围
     * @param platforms 平台列表
     * @param dataScope 数据权限范围
     * @return 活跃度分析聚合根
     */
    Optional<FanAggregate> getFanActivityAnalysis(TimeRange timeRange, List<String> platforms, String dataScope);

    /**
     * 获取粉丝价值分析数据
     *
     * @param timeRange 时间范围
     * @param platforms 平台列表
     * @param dataScope 数据权限范围
     * @return 价值分析聚合根
     */
    Optional<FanAggregate> getFanValueAnalysis(TimeRange timeRange, List<String> platforms, String dataScope);

    /**
     * 获取平台分析数据
     *
     * @param timeRange 时间范围
     * @param platforms 平台列表
     * @param dataScope 数据权限范围
     * @return 平台分析聚合根
     */
    Optional<FanAggregate> getPlatformAnalysis(TimeRange timeRange, List<String> platforms, String dataScope);

    /**
     * 保存粉丝分析结果
     *
     * @param aggregate 粉丝分析聚合根
     */
    void save(FanAggregate aggregate);

    /**
     * 根据ID查找粉丝分析
     *
     * @param id 聚合根ID
     * @return 粉丝分析聚合根
     */
    Optional<FanAggregate> findById(String id);

    /**
     * 粉丝实时统计数据
     */
    record FanRealTimeStats(
            Long totalFans,
            Long activeFans,
            Long newFansToday,
            Long unfollowedFansToday,
            Long highValueFans,
            Long mediumValueFans,
            Long lowValueFans,
            Double avgActivityLevel,
            Double retentionRate,
            Long totalInteractions
    ) {}

    /**
     * 获取实时粉丝统计
     *
     * @param platforms 平台列表
     * @param dataScope 数据权限范围
     * @return 实时统计数据
     */
    FanRealTimeStats getRealTimeFanStats(List<String> platforms, String dataScope);

    /**
     * 粉丝分析摘要数据
     */
    record FanAnalysisSummary(
            String analysisId,
            String analysisType,
            TimeRange timeRange,
            Long totalFans,
            Long activeFans,
            Double avgActivityLevel,
            String topPlatform,
            String summary
    ) {}

    /**
     * 获取粉丝分析摘要
     *
     * @param timeRange 时间范围
     * @param dataScope 数据权限范围
     * @return 分析摘要
     */
    FanAnalysisSummary getFanAnalysisSummary(TimeRange timeRange, String dataScope);

    /**
     * 粉丝分页查询参数
     */
    record FanPageQuery(
            TimeRange timeRange,
            List<String> platforms,
            String source,
            String fanType,
            String fanValue,
            String status,
            String keyword,
            int page,
            int size,
            String sortField,
            String sortOrder,
            String dataScope
    ) {}

    /**
     * 分页查询粉丝列表
     *
     * @param query 查询参数
     * @return 粉丝分页数据
     */
    record FanPageResult(
            List<FanAggregate.FanData> fans,
            long total,
            int page,
            int size
    ) {}

    /**
     * 分页查询粉丝
     *
     * @param query 查询参数
     * @return 分页结果
     */
    FanPageResult getFansByPage(FanPageQuery query);
}
