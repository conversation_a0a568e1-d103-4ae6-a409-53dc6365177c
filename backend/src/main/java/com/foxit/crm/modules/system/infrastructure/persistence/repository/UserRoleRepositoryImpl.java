package com.foxit.crm.modules.system.infrastructure.persistence.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.foxit.crm.modules.system.domain.model.aggregate.UserRole;
import com.foxit.crm.modules.system.domain.repository.UserRoleRepository;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.UserRolePO;
import com.foxit.crm.modules.system.infrastructure.persistence.mapper.UserRoleMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户角色关联仓储实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Repository
@RequiredArgsConstructor
public class UserRoleRepositoryImpl implements UserRoleRepository {

    private final UserRoleMapper userRoleMapper;

    @Override
    public UserRole save(UserRole userRole) {
        UserRolePO userRolePO = new UserRolePO();
        BeanUtils.copyProperties(userRole, userRolePO);
        
        if (userRole.getId() == null) {
            userRoleMapper.insert(userRolePO);
            userRole.setId(userRolePO.getId());
        } else {
            userRoleMapper.updateById(userRolePO);
        }
        
        return userRole;
    }

    @Override
    public Optional<UserRole> findById(Long id) {
        UserRolePO userRolePO = userRoleMapper.selectById(id);
        if (userRolePO == null) {
            return Optional.empty();
        }
        
        UserRole userRole = new UserRole();
        BeanUtils.copyProperties(userRolePO, userRole);
        return Optional.of(userRole);
    }

    @Override
    public List<UserRole> findByUserId(Long userId) {
        QueryWrapper<UserRolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        
        List<UserRolePO> userRolePOs = userRoleMapper.selectList(queryWrapper);
        return userRolePOs.stream()
                         .map(this::convertToUserRole)
                         .collect(Collectors.toList());
    }

    @Override
    public List<UserRole> findByRoleId(Long roleId) {
        QueryWrapper<UserRolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        
        List<UserRolePO> userRolePOs = userRoleMapper.selectList(queryWrapper);
        return userRolePOs.stream()
                         .map(this::convertToUserRole)
                         .collect(Collectors.toList());
    }

    @Override
    public Optional<UserRole> findByUserIdAndRoleId(Long userId, Long roleId) {
        QueryWrapper<UserRolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("role_id", roleId);
        
        UserRolePO userRolePO = userRoleMapper.selectOne(queryWrapper);
        if (userRolePO == null) {
            return Optional.empty();
        }
        
        UserRole userRole = new UserRole();
        BeanUtils.copyProperties(userRolePO, userRole);
        return Optional.of(userRole);
    }

    @Override
    public void saveBatch(List<UserRole> userRoles) {
        List<UserRolePO> userRolePOs = userRoles.stream()
            .map(userRole -> {
                UserRolePO userRolePO = new UserRolePO();
                BeanUtils.copyProperties(userRole, userRolePO);
                return userRolePO;
            })
            .collect(Collectors.toList());
        
        // 使用MyBatis-Plus的批量插入
        userRolePOs.forEach(userRoleMapper::insert);
    }

    @Override
    public void deleteByUserId(Long userId) {
        QueryWrapper<UserRolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        userRoleMapper.delete(queryWrapper);
    }

    @Override
    public void deleteByRoleId(Long roleId) {
        QueryWrapper<UserRolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        userRoleMapper.delete(queryWrapper);
    }

    @Override
    public void deleteByUserIdAndRoleId(Long userId, Long roleId) {
        QueryWrapper<UserRolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("role_id", roleId);
        userRoleMapper.delete(queryWrapper);
    }

    @Override
    public void deleteByUserIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return;
        }
        
        QueryWrapper<UserRolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("user_id", userIds);
        userRoleMapper.delete(queryWrapper);
    }

    @Override
    public void deleteByRoleIds(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return;
        }
        
        QueryWrapper<UserRolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("role_id", roleIds);
        userRoleMapper.delete(queryWrapper);
    }

    @Override
    public boolean existsByUserIdAndRoleId(Long userId, Long roleId) {
        QueryWrapper<UserRolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("role_id", roleId);
        return userRoleMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public long countByRoleId(Long roleId) {
        QueryWrapper<UserRolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        return userRoleMapper.selectCount(queryWrapper);
    }

    @Override
    public long countByUserId(Long userId) {
        QueryWrapper<UserRolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        return userRoleMapper.selectCount(queryWrapper);
    }

    @Override
    public List<Long> findRoleIdsByUserId(Long userId) {
        QueryWrapper<UserRolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .select("role_id");
        
        List<UserRolePO> userRolePOs = userRoleMapper.selectList(queryWrapper);
        return userRolePOs.stream()
                         .map(UserRolePO::getRoleId)
                         .collect(Collectors.toList());
    }

    @Override
    public List<Long> findUserIdsByRoleId(Long roleId) {
        QueryWrapper<UserRolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId)
                   .select("user_id");
        
        List<UserRolePO> userRolePOs = userRoleMapper.selectList(queryWrapper);
        return userRolePOs.stream()
                         .map(UserRolePO::getUserId)
                         .collect(Collectors.toList());
    }

    /**
     * 转换PO为领域对象
     */
    private UserRole convertToUserRole(UserRolePO userRolePO) {
        UserRole userRole = new UserRole();
        BeanUtils.copyProperties(userRolePO, userRole);
        return userRole;
    }
}
