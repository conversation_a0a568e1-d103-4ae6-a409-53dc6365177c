package com.foxit.crm.modules.useranalysis.application.dto;

import com.foxit.crm.modules.useranalysis.domain.entity.UserGrowthAggregate;
import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户增长分析响应DTO
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Builder
public class UserGrowthAnalysisResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 聚合根ID
     */
    private String aggregateId;

    /**
     * 分析类型
     */
    private String analysisType;

    /**
     * 时间范围
     */
    private TimeRangeDto timeRange;

    /**
     * 产品线ID列表
     */
    private List<Long> productLineIds;

    /**
     * 核心指标
     */
    private Map<String, MetricValueDto> coreMetrics;

    /**
     * 趋势数据
     */
    private Map<String, TrendDataDto> trendData;

    /**
     * 留存数据
     */
    private Map<String, RetentionDataDto> retentionData;

    /**
     * 用户来源数据
     */
    private Map<String, UserSourceDataDto> userSourceData;

    /**
     * 数据权限范围
     */
    private String dataScope;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 数据摘要
     */
    private String dataSummary;

    /**
     * 时间范围DTO
     */
    @Data
    @Builder
    public static class TimeRangeDto {
        private String startDate;
        private String endDate;
        private String granularity;
    }

    /**
     * 指标值DTO
     */
    @Data
    @Builder
    public static class MetricValueDto {
        private String name;
        private Number value;
        private Number previousValue;
        private Double changeRate;
        private String unit;
        private String type;
        private String trend;
    }

    /**
     * 趋势数据DTO
     */
    @Data
    @Builder
    public static class TrendDataDto {
        private String name;
        private List<String> categories;
        private List<Number> values;
        private String unit;
        private String chartType;
    }

    /**
     * 留存数据DTO
     */
    @Data
    @Builder
    public static class RetentionDataDto {
        private String name;
        private List<RetentionPeriodDto> periods;
        private String chartType;
    }

    /**
     * 留存周期DTO
     */
    @Data
    @Builder
    public static class RetentionPeriodDto {
        private String period;
        private Double retentionRate;
        private Long userCount;
        private Long baseUserCount;
    }

    /**
     * 用户来源数据DTO
     */
    @Data
    @Builder
    public static class UserSourceDataDto {
        private String name;
        private List<UserSourceItemDto> sources;
        private String chartType;
    }

    /**
     * 用户来源项DTO
     */
    @Data
    @Builder
    public static class UserSourceItemDto {
        private String sourceName;
        private Long userCount;
        private Double percentage;
        private String sourceType;
    }

    /**
     * 创建成功响应
     */
    public static UserGrowthAnalysisResponse success(UserGrowthAggregate aggregate) {
        if (aggregate == null) {
            return empty("聚合数据为空");
        }

        return UserGrowthAnalysisResponse.builder()
                .success(true)
                .message("获取用户增长分析数据成功")
                .aggregateId(aggregate.getId())
                .analysisType(aggregate.getAnalysisType().name())
                .timeRange(convertTimeRange(aggregate.getTimeRange()))
                .productLineIds(aggregate.getProductLineIds())
                .coreMetrics(convertCoreMetrics(aggregate.getCoreMetrics()))
                .trendData(convertTrendData(aggregate.getTrendData()))
                .retentionData(convertRetentionData(aggregate.getRetentionData()))
                .userSourceData(convertUserSourceData(aggregate.getUserSourceData()))
                .dataScope(aggregate.getDataScope())
                .lastUpdateTime(aggregate.getLastUpdateTime())
                .dataSummary(aggregate.getDataSummary())
                .build();
    }

    /**
     * 创建空响应
     */
    public static UserGrowthAnalysisResponse empty(String message) {
        return UserGrowthAnalysisResponse.builder()
                .success(false)
                .message(message != null ? message : "未找到用户增长分析数据")
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建错误响应
     */
    public static UserGrowthAnalysisResponse error(String message) {
        return UserGrowthAnalysisResponse.builder()
                .success(false)
                .message(message != null ? message : "获取用户增长分析数据失败")
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 转换时间范围
     */
    private static TimeRangeDto convertTimeRange(
            com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange timeRange) {
        if (timeRange == null) {
            return null;
        }

        return TimeRangeDto.builder()
                .startDate(timeRange.getStartDate().toString())
                .endDate(timeRange.getEndDate().toString())
                .granularity(timeRange.getGranularity().name())
                .build();
    }

    /**
     * 转换核心指标
     */
    private static Map<String, MetricValueDto> convertCoreMetrics(Map<String, MetricValue> coreMetrics) {
        if (coreMetrics == null) {
            return null;
        }

        return coreMetrics.entrySet().stream()
                .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> convertMetricValue(entry.getValue())));
    }

    /**
     * 转换指标值
     */
    private static MetricValueDto convertMetricValue(MetricValue metricValue) {
        if (metricValue == null) {
            return null;
        }

        return MetricValueDto.builder()
                .name(metricValue.getName())
                .value(metricValue.getValue())
                .previousValue(metricValue.getPreviousValue())
                .changeRate(metricValue.getGrowthRate())
                .unit(metricValue.getUnit())
                .type(metricValue.getType().name())
                .trend(metricValue.getGrowthTrend().name())
                .build();
    }

    /**
     * 转换趋势数据
     */
    private static Map<String, TrendDataDto> convertTrendData(Map<String, UserGrowthAggregate.TrendData> trendData) {
        if (trendData == null) {
            return null;
        }

        return trendData.entrySet().stream()
                .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> TrendDataDto.builder()
                                .name(entry.getValue().getName())
                                .categories(entry.getValue().getCategories())
                                .values(entry.getValue().getValues())
                                .unit(entry.getValue().getUnit())
                                .chartType(entry.getValue().getChartType())
                                .build()));
    }

    /**
     * 转换留存数据
     */
    private static Map<String, RetentionDataDto> convertRetentionData(
            Map<String, UserGrowthAggregate.RetentionData> retentionData) {
        if (retentionData == null) {
            return null;
        }

        return retentionData.entrySet().stream()
                .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> RetentionDataDto.builder()
                                .name(entry.getValue().getName())
                                .periods(entry.getValue().getPeriods().stream()
                                        .map(period -> RetentionPeriodDto.builder()
                                                .period(period.getPeriod())
                                                .retentionRate(period.getRetentionRate())
                                                .userCount(period.getUserCount())
                                                .baseUserCount(period.getBaseUserCount())
                                                .build())
                                        .toList())
                                .chartType(entry.getValue().getChartType())
                                .build()));
    }

    /**
     * 转换用户来源数据
     */
    private static Map<String, UserSourceDataDto> convertUserSourceData(
            Map<String, UserGrowthAggregate.UserSourceData> userSourceData) {
        if (userSourceData == null) {
            return null;
        }

        return userSourceData.entrySet().stream()
                .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> UserSourceDataDto.builder()
                                .name(entry.getValue().getName())
                                .sources(entry.getValue().getSources().stream()
                                        .map(source -> UserSourceItemDto.builder()
                                                .sourceName(source.getSourceName())
                                                .userCount(source.getUserCount())
                                                .percentage(source.getPercentage())
                                                .sourceType(source.getSourceType())
                                                .build())
                                        .toList())
                                .chartType(entry.getValue().getChartType())
                                .build()));
    }
}
