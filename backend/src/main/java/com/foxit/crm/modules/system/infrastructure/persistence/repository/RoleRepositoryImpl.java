package com.foxit.crm.modules.system.infrastructure.persistence.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.foxit.crm.modules.system.domain.model.aggregate.Role;
import com.foxit.crm.modules.system.domain.repository.RoleRepository;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.RolePO;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.UserRolePO;
import com.foxit.crm.modules.system.infrastructure.persistence.mapper.RoleMapper;
import com.foxit.crm.modules.system.infrastructure.persistence.mapper.UserRoleMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 角色仓储实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Repository
@RequiredArgsConstructor
public class RoleRepositoryImpl implements RoleRepository {

    private final RoleMapper roleMapper;
    private final UserRoleMapper userRoleMapper;

    @Override
    public Role save(Role role) {
        RolePO rolePO = new RolePO();
        BeanUtils.copyProperties(role, rolePO);

        if (role.getId() == null) {
            roleMapper.insert(rolePO);
            role.setId(rolePO.getId());
        } else {
            roleMapper.updateById(rolePO);
        }

        return role;
    }

    @Override
    public Optional<Role> findById(Long id) {
        RolePO rolePO = roleMapper.selectById(id);
        if (rolePO == null) {
            return Optional.empty();
        }

        Role role = new Role();
        BeanUtils.copyProperties(rolePO, role);
        return Optional.of(role);
    }

    @Override
    public Optional<Role> findByRoleCode(String roleCode) {
        QueryWrapper<RolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_code", roleCode);

        RolePO rolePO = roleMapper.selectOne(queryWrapper);
        if (rolePO == null) {
            return Optional.empty();
        }

        Role role = new Role();
        BeanUtils.copyProperties(rolePO, role);
        return Optional.of(role);
    }

    @Override
    public List<Role> findAllEnabled() {
        QueryWrapper<RolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                .orderByAsc("sort_order");

        List<RolePO> rolePOs = roleMapper.selectList(queryWrapper);
        return rolePOs.stream()
                .map(this::convertToRole)
                .collect(Collectors.toList());
    }

    @Override
    public List<Role> findByPage(int page, int size, String keyword) {
        Page<RolePO> pageParam = new Page<>(page, size);
        QueryWrapper<RolePO> queryWrapper = new QueryWrapper<>();

        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                    .like("role_name", keyword)
                    .or()
                    .like("role_code", keyword)
                    .or()
                    .like("description", keyword));
        }

        queryWrapper.orderByAsc("sort_order");

        Page<RolePO> result = roleMapper.selectPage(pageParam, queryWrapper);
        return result.getRecords().stream()
                .map(this::convertToRole)
                .collect(Collectors.toList());
    }

    @Override
    public long count(String keyword) {
        QueryWrapper<RolePO> queryWrapper = new QueryWrapper<>();

        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                    .like("role_name", keyword)
                    .or()
                    .like("role_code", keyword)
                    .or()
                    .like("description", keyword));
        }

        return roleMapper.selectCount(queryWrapper);
    }

    @Override
    public List<Role> findByUserId(Long userId) {
        // 先查询用户角色关联
        QueryWrapper<UserRolePO> userRoleQuery = new QueryWrapper<>();
        userRoleQuery.eq("user_id", userId);
        List<UserRolePO> userRoles = userRoleMapper.selectList(userRoleQuery);

        if (userRoles.isEmpty()) {
            return List.of();
        }

        // 获取角色ID列表
        List<Long> roleIds = userRoles.stream()
                .map(UserRolePO::getRoleId)
                .collect(Collectors.toList());

        // 查询角色信息
        List<RolePO> rolePOs = roleMapper.selectBatchIds(roleIds);
        return rolePOs.stream()
                .map(this::convertToRole)
                .collect(Collectors.toList());
    }

    @Override
    public void deleteById(Long id) {
        roleMapper.deleteById(id);
    }

    @Override
    public boolean existsByRoleCode(String roleCode) {
        QueryWrapper<RolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_code", roleCode);
        return roleMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean existsByRoleCodeAndIdNot(String roleCode, Long id) {
        QueryWrapper<RolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_code", roleCode)
                .ne("id", id);
        return roleMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean existsByRoleName(String roleName) {
        QueryWrapper<RolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_name", roleName);
        return roleMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 转换PO为领域对象
     */
    private Role convertToRole(RolePO rolePO) {
        Role role = new Role();
        BeanUtils.copyProperties(rolePO, role);
        return role;
    }
}
