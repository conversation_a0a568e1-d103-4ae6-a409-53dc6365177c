package com.foxit.crm.modules.behavioranalysis.application.dto;

import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 事件分析请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Builder
@Schema(description = "事件分析请求")
public class EventAnalysisRequest {

    @Schema(description = "开始日期", example = "2025-01-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2025-01-31")
    private LocalDate endDate;

    @Schema(description = "时间粒度", example = "DAY")
    private TimeRange.TimeGranularity granularity;

    @Schema(description = "产品线ID列表")
    private List<Long> productLineIds;

    @Schema(description = "事件ID列表")
    private List<String> eventIds;

    @Schema(description = "起始事件ID列表（用于路径分析）")
    private List<String> startEventIds;

    @Schema(description = "结束事件ID列表（用于路径分析）")
    private List<String> endEventIds;

    @Schema(description = "单个事件ID（用于属性分析）")
    private String eventId;

    @Schema(description = "事件属性名称列表")
    private List<String> propertyNames;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "数据权限范围")
    private String dataScope;

    @Schema(description = "是否包含对比数据")
    private Boolean includeComparison;

    @Schema(description = "是否包含详细数据")
    private Boolean includeDetails;

    @Schema(description = "分析类型")
    private String analysisType;

    @Schema(description = "筛选条件")
    private EventFilterCondition filterCondition;

    /**
     * 事件筛选条件
     */
    @Data
    @Builder
    @Schema(description = "事件筛选条件")
    public static class EventFilterCondition {

        @Schema(description = "事件类别")
        private List<String> eventCategories;

        @Schema(description = "用户群体ID")
        private List<String> userSegmentIds;

        @Schema(description = "设备类型")
        private List<String> deviceTypes;

        @Schema(description = "操作系统")
        private List<String> operatingSystems;

        @Schema(description = "应用版本")
        private List<String> appVersions;

        @Schema(description = "地域")
        private List<String> regions;

        @Schema(description = "渠道")
        private List<String> channels;

        @Schema(description = "最小事件次数")
        private Integer minEventCount;

        @Schema(description = "最大事件次数")
        private Integer maxEventCount;

        @Schema(description = "最小用户数")
        private Integer minUserCount;

        @Schema(description = "最大用户数")
        private Integer maxUserCount;

        @Schema(description = "自定义属性筛选")
        private List<PropertyFilter> propertyFilters;
    }

    /**
     * 属性筛选条件
     */
    @Data
    @Builder
    @Schema(description = "属性筛选条件")
    public static class PropertyFilter {

        @Schema(description = "属性名称")
        private String propertyName;

        @Schema(description = "操作符", example = "equals, contains, greater_than, less_than")
        private String operator;

        @Schema(description = "属性值")
        private String propertyValue;

        @Schema(description = "属性值列表（用于in操作）")
        private List<String> propertyValues;
    }

    /**
     * 创建事件趋势分析请求
     */
    public static EventAnalysisRequest createTrendAnalysisRequest(LocalDate startDate, LocalDate endDate, 
                                                                 List<String> eventIds, String dataScope) {
        return EventAnalysisRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .eventIds(eventIds)
                .dataScope(dataScope)
                .analysisType("EVENT_TREND")
                .includeComparison(false)
                .includeDetails(false)
                .build();
    }

    /**
     * 创建事件漏斗分析请求
     */
    public static EventAnalysisRequest createFunnelAnalysisRequest(LocalDate startDate, LocalDate endDate, 
                                                                  List<String> eventIds, String dataScope) {
        return EventAnalysisRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .eventIds(eventIds)
                .dataScope(dataScope)
                .analysisType("EVENT_FUNNEL")
                .includeComparison(false)
                .includeDetails(true)
                .build();
    }

    /**
     * 创建事件路径分析请求
     */
    public static EventAnalysisRequest createPathAnalysisRequest(LocalDate startDate, LocalDate endDate, 
                                                                List<String> startEventIds, List<String> endEventIds, 
                                                                String dataScope) {
        return EventAnalysisRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .startEventIds(startEventIds)
                .endEventIds(endEventIds)
                .dataScope(dataScope)
                .analysisType("EVENT_PATH")
                .includeComparison(false)
                .includeDetails(true)
                .build();
    }

    /**
     * 创建事件属性分析请求
     */
    public static EventAnalysisRequest createPropertyAnalysisRequest(LocalDate startDate, LocalDate endDate, 
                                                                    String eventId, List<String> propertyNames, 
                                                                    String dataScope) {
        return EventAnalysisRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .eventId(eventId)
                .propertyNames(propertyNames)
                .dataScope(dataScope)
                .analysisType("EVENT_PROPERTY")
                .includeComparison(false)
                .includeDetails(true)
                .build();
    }

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        if (startDate == null || endDate == null) {
            return false;
        }
        
        if (startDate.isAfter(endDate)) {
            return false;
        }
        
        if (dataScope == null || dataScope.trim().isEmpty()) {
            return false;
        }
        
        // 根据分析类型验证特定参数
        if (analysisType != null) {
            switch (analysisType) {
                case "EVENT_TREND":
                case "EVENT_FUNNEL":
                case "EVENT_COMPARISON":
                    return eventIds != null && !eventIds.isEmpty();
                case "EVENT_PATH":
                    return startEventIds != null && !startEventIds.isEmpty();
                case "EVENT_PROPERTY":
                    return eventId != null && !eventId.trim().isEmpty();
                default:
                    return true;
            }
        }
        
        return true;
    }

    /**
     * 获取请求摘要
     */
    public String getSummary() {
        return String.format("事件分析请求: 类型=%s, 时间范围=%s至%s, 事件数=%d", 
                analysisType != null ? analysisType : "未指定",
                startDate != null ? startDate.toString() : "未指定",
                endDate != null ? endDate.toString() : "未指定",
                eventIds != null ? eventIds.size() : 0);
    }
}
