package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 操作日志统计响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
public class OperationLogStatisticsResponse {

    /**
     * 总操作次数
     */
    private Long totalOperations;

    /**
     * 成功操作次数
     */
    private Long successOperations;

    /**
     * 失败操作次数
     */
    private Long failedOperations;

    /**
     * 成功率
     */
    private Double successRate;

    /**
     * 今日操作次数
     */
    private Long todayOperations;

    /**
     * 本周操作次数
     */
    private Long weekOperations;

    /**
     * 本月操作次数
     */
    private Long monthOperations;

    /**
     * 操作类型统计
     */
    private List<OperationTypeStatistics> operationTypeStatistics;

    /**
     * 用户操作统计
     */
    private List<UserOperationStatistics> userOperationStatistics;

    /**
     * 每日操作趋势
     */
    private Map<String, Long> dailyTrend;

    /**
     * 每小时操作分布
     */
    private Map<String, Long> hourlyDistribution;

    /**
     * 操作类型统计内部类
     */
    @Data
    public static class OperationTypeStatistics {
        private String operationType;
        private Long count;
        private Double percentage;
    }

    /**
     * 用户操作统计内部类
     */
    @Data
    public static class UserOperationStatistics {
        private Long userId;
        private String username;
        private Long count;
        private Double percentage;
    }

    /**
     * 计算成功率
     */
    public void calculateSuccessRate() {
        if (totalOperations != null && totalOperations > 0) {
            this.successRate = (double) successOperations / totalOperations * 100;
        } else {
            this.successRate = 0.0;
        }
    }
}
