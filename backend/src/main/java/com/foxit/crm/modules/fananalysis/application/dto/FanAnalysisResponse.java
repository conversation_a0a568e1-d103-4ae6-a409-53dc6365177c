package com.foxit.crm.modules.fananalysis.application.dto;

import com.foxit.crm.modules.fananalysis.domain.entity.FanAggregate;
import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 粉丝分析响应DTO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
@Builder
public class FanAnalysisResponse {

    /**
     * 是否成功
     */
    private final boolean success;

    /**
     * 消息
     */
    private final String message;

    /**
     * 聚合根ID
     */
    private final String aggregateId;

    /**
     * 分析类型
     */
    private final String analysisType;

    /**
     * 时间范围
     */
    private final TimeRange timeRange;

    /**
     * 平台列表
     */
    private final List<String> platforms;

    /**
     * 核心指标
     */
    private final Map<String, MetricValue> coreMetrics;

    /**
     * 粉丝列表数据
     */
    private final List<FanAggregate.FanData> fanList;

    /**
     * 趋势数据
     */
    private final Map<String, FanAggregate.TrendData> trendData;

    /**
     * 平台分析数据
     */
    private final Map<String, FanAggregate.PlatformData> platformData;

    /**
     * 数据权限范围
     */
    private final String dataScope;

    /**
     * 最后更新时间
     */
    private final LocalDateTime lastUpdateTime;

    /**
     * 数据摘要
     */
    private final String dataSummary;

    /**
     * 分页信息
     */
    private final PageInfo pageInfo;

    /**
     * 分页信息
     */
    @Getter
    @Builder
    public static class PageInfo {
        private final long total;
        private final int page;
        private final int pageSize;
        private final int totalPages;
    }

    /**
     * 创建成功响应
     */
    public static FanAnalysisResponse success(FanAggregate aggregate) {
        return FanAnalysisResponse.builder()
                .success(true)
                .message("获取粉丝分析数据成功")
                .aggregateId(aggregate.getId())
                .analysisType(aggregate.getAnalysisType().name())
                .timeRange(aggregate.getTimeRange())
                .platforms(aggregate.getPlatforms())
                .coreMetrics(aggregate.getCoreMetrics())
                .fanList(aggregate.getFanList())
                .trendData(aggregate.getTrendData())
                .platformData(aggregate.getPlatformData())
                .dataScope(aggregate.getDataScope())
                .lastUpdateTime(aggregate.getLastUpdateTime())
                .dataSummary(aggregate.getDataSummary())
                .build();
    }

    /**
     * 创建分页成功响应
     */
    public static FanAnalysisResponse successWithPage(FanAggregate aggregate, long total, int page, int pageSize) {
        int totalPages = (int) Math.ceil((double) total / pageSize);
        
        PageInfo pageInfo = PageInfo.builder()
                .total(total)
                .page(page)
                .pageSize(pageSize)
                .totalPages(totalPages)
                .build();

        return FanAnalysisResponse.builder()
                .success(true)
                .message("获取粉丝分析数据成功")
                .aggregateId(aggregate.getId())
                .analysisType(aggregate.getAnalysisType().name())
                .timeRange(aggregate.getTimeRange())
                .platforms(aggregate.getPlatforms())
                .coreMetrics(aggregate.getCoreMetrics())
                .fanList(aggregate.getFanList())
                .trendData(aggregate.getTrendData())
                .platformData(aggregate.getPlatformData())
                .dataScope(aggregate.getDataScope())
                .lastUpdateTime(aggregate.getLastUpdateTime())
                .dataSummary(aggregate.getDataSummary())
                .pageInfo(pageInfo)
                .build();
    }

    /**
     * 创建空响应
     */
    public static FanAnalysisResponse empty(String message) {
        return FanAnalysisResponse.builder()
                .success(false)
                .message(message)
                .build();
    }

    /**
     * 创建错误响应
     */
    public static FanAnalysisResponse error(String message) {
        return FanAnalysisResponse.builder()
                .success(false)
                .message(message)
                .build();
    }
}
