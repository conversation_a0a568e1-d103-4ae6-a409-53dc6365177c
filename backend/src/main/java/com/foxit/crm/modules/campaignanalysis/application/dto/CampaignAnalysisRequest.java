package com.foxit.crm.modules.campaignanalysis.application.dto;

import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDate;
import java.util.List;

/**
 * 活动分析请求DTO
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Getter
@Builder
public class CampaignAnalysisRequest {

    /**
     * 开始日期
     */
    private final LocalDate startDate;

    /**
     * 结束日期
     */
    private final LocalDate endDate;

    /**
     * 时间粒度
     */
    private final TimeRange.TimeGranularity granularity;

    /**
     * 活动ID列表
     */
    private final List<Long> campaignIds;

    /**
     * 活动类型列表
     */
    private final List<String> campaignTypes;

    /**
     * 活动状态列表
     */
    private final List<String> statuses;

    /**
     * 渠道列表
     */
    private final List<String> channels;

    /**
     * 数据权限范围
     */
    private final String dataScope;

    /**
     * 用户ID
     */
    private final Long userId;

    /**
     * 生成缓存键
     */
    public String cacheKey() {
        return String.format("campaign_analysis_%s_%s_%s_%s_%s_%s_%s_%s",
                startDate, endDate, granularity,
                campaignIds != null ? String.join(",", campaignIds.stream().map(String::valueOf).toArray(String[]::new)) : "all",
                campaignTypes != null ? String.join(",", campaignTypes) : "all",
                statuses != null ? String.join(",", statuses) : "all",
                channels != null ? String.join(",", channels) : "all",
                dataScope);
    }
}
