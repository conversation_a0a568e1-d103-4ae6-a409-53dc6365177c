package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;

import java.util.List;

/**
 * 产品线列表响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
public class ProductLineListResponse {

    /**
     * 产品线列表
     */
    private List<ProductLineDetailResponse> items;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Integer page;

    /**
     * 每页大小
     */
    private Integer size;

    /**
     * 总页数
     */
    private Integer totalPages;

    /**
     * 构造函数
     */
    public ProductLineListResponse(List<ProductLineDetailResponse> items, Long total, Integer page, Integer size) {
        this.items = items;
        this.total = total;
        this.page = page;
        this.size = size;
        this.totalPages = (int) Math.ceil((double) total / size);
    }
}
