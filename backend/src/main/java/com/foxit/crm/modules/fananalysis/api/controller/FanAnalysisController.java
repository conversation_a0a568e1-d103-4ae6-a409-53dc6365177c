package com.foxit.crm.modules.fananalysis.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.common.util.SecurityUtils;
import com.foxit.crm.modules.fananalysis.application.dto.FanAnalysisRequest;
import com.foxit.crm.modules.fananalysis.application.dto.FanAnalysisResponse;
import com.foxit.crm.modules.fananalysis.application.service.FanAnalysisService;
import com.foxit.crm.modules.fananalysis.domain.repository.FanAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 粉丝分析控制器
 * 提供粉丝分析相关的REST API接口
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@RestController
@RequestMapping("/private-domain/fan")
@RequiredArgsConstructor
@Tag(name = "粉丝分析", description = "私域粉丝分析相关接口")
public class FanAnalysisController {

    private final FanAnalysisService fanAnalysisService;

    // ==================== 需要认证的接口 ====================

    /**
     * 获取粉丝总览
     */
    @Operation(summary = "获取粉丝总览", description = "获取指定时间范围内的粉丝总览数据")
    @GetMapping("/overview")
    @PreAuthorize("hasPermission('private-domain:fan', 'READ')")
    public Result<FanAnalysisResponse> getOverview(
            @Parameter(description = "开始日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "平台列表") @RequestParam(required = false) List<String> platforms,
            @Parameter(description = "来源筛选") @RequestParam(required = false) String source,
            @Parameter(description = "粉丝类型筛选") @RequestParam(required = false) String fanType) {

        try {
            FanAnalysisRequest request = FanAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .platforms(platforms)
                    .source(source)
                    .fanType(fanType)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            FanAnalysisResponse response = fanAnalysisService.getFanOverview(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取粉丝总览失败", e);
            return Result.error("获取粉丝总览失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询粉丝列表
     */
    @Operation(summary = "分页查询粉丝列表", description = "分页查询粉丝详细信息")
    @GetMapping("/list")
    @PreAuthorize("hasPermission('private-domain:fan', 'READ')")
    public Result<FanAnalysisResponse> getFanList(
            @Parameter(description = "开始日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "平台列表") @RequestParam(required = false) List<String> platforms,
            @Parameter(description = "来源筛选") @RequestParam(required = false) String source,
            @Parameter(description = "粉丝类型筛选") @RequestParam(required = false) String fanType,
            @Parameter(description = "粉丝价值筛选") @RequestParam(required = false) String fanValue,
            @Parameter(description = "状态筛选") @RequestParam(required = false) String status,
            @Parameter(description = "关键词搜索") @RequestParam(required = false) String keyword,
            @Parameter(description = "页码") @RequestParam(required = false, defaultValue = "1") Integer page,
            @Parameter(description = "页面大小") @RequestParam(required = false, defaultValue = "10") Integer pageSize,
            @Parameter(description = "排序字段") @RequestParam(required = false, defaultValue = "activity_level") String sortField,
            @Parameter(description = "排序方向") @RequestParam(required = false, defaultValue = "desc") String sortOrder) {

        try {
            FanAnalysisRequest request = FanAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .platforms(platforms)
                    .source(source)
                    .fanType(fanType)
                    .fanValue(fanValue)
                    .status(status)
                    .keyword(keyword)
                    .page(page)
                    .pageSize(pageSize)
                    .sortField(sortField)
                    .sortOrder(sortOrder)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            FanAnalysisResponse response = fanAnalysisService.getFansByPage(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("分页查询粉丝列表失败", e);
            return Result.error("分页查询粉丝列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取实时统计
     */
    @Operation(summary = "获取实时粉丝统计", description = "获取实时粉丝统计数据")
    @GetMapping("/realtime-stats")
    @PreAuthorize("hasPermission('private-domain:fan', 'READ')")
    public Result<FanAnalysisRepository.FanRealTimeStats> getRealTimeStats(
            @Parameter(description = "平台列表") @RequestParam(required = false) List<String> platforms) {

        try {
            FanAnalysisRequest request = FanAnalysisRequest.builder()
                    .platforms(platforms)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            FanAnalysisRepository.FanRealTimeStats stats = fanAnalysisService.getRealTimeFanStats(request);
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取实时粉丝统计失败", e);
            return Result.error("获取实时粉丝统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取分析摘要
     */
    @Operation(summary = "获取粉丝分析摘要", description = "获取粉丝分析的摘要信息")
    @GetMapping("/summary")
    @PreAuthorize("hasPermission('private-domain:fan', 'READ')")
    public Result<FanAnalysisRepository.FanAnalysisSummary> getSummary(
            @Parameter(description = "开始日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        try {
            FanAnalysisRequest request = FanAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            FanAnalysisRepository.FanAnalysisSummary summary = fanAnalysisService.getFanAnalysisSummary(request);
            return Result.success(summary);

        } catch (Exception e) {
            log.error("获取粉丝分析摘要失败", e);
            return Result.error("获取粉丝分析摘要失败: " + e.getMessage());
        }
    }

    // ==================== 公共接口（无需认证） ====================

    /**
     * 获取公共粉丝总览 - 无需认证
     */
    @Operation(summary = "获取公共粉丝总览", description = "获取粉丝总览数据（无需认证）")
    @GetMapping("/public/overview")
    public Result<FanAnalysisResponse> getPublicOverview(
            @Parameter(description = "开始日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "平台列表") @RequestParam(required = false) List<String> platforms,
            @Parameter(description = "来源筛选") @RequestParam(required = false) String source,
            @Parameter(description = "粉丝类型筛选") @RequestParam(required = false) String fanType,
            @Parameter(description = "数据权限范围") @RequestParam(required = false, defaultValue = "PUBLIC") String dataScope) {

        try {
            FanAnalysisRequest request = FanAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .platforms(platforms)
                    .source(source)
                    .fanType(fanType)
                    .dataScope(dataScope)
                    .build();

            FanAnalysisResponse response = fanAnalysisService.getFanOverview(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取公共粉丝总览失败", e);
            return Result.error("获取公共粉丝总览失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共粉丝列表 - 无需认证
     */
    @GetMapping("/public/list")
    public Result<FanAnalysisResponse> getPublicFanList(
            @Parameter(description = "平台列表") @RequestParam(required = false) List<String> platforms,
            @Parameter(description = "来源筛选") @RequestParam(required = false) String source,
            @Parameter(description = "粉丝类型筛选") @RequestParam(required = false) String fanType,
            @Parameter(description = "页码") @RequestParam(required = false, defaultValue = "1") Integer page,
            @Parameter(description = "页面大小") @RequestParam(required = false, defaultValue = "10") Integer pageSize,
            @Parameter(description = "数据权限范围") @RequestParam(required = false, defaultValue = "PUBLIC") String dataScope) {

        try {
            FanAnalysisRequest request = FanAnalysisRequest.builder()
                    .platforms(platforms)
                    .source(source)
                    .fanType(fanType)
                    .page(page)
                    .pageSize(pageSize)
                    .sortField("activity_level")
                    .sortOrder("desc")
                    .dataScope(dataScope)
                    .build();

            FanAnalysisResponse response = fanAnalysisService.getFansByPage(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取公共粉丝列表失败", e);
            return Result.error("获取公共粉丝列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共实时统计 - 无需认证
     */
    @Operation(summary = "获取公共实时粉丝统计", description = "获取实时粉丝统计数据（无需认证）")
    @GetMapping("/public/realtime-stats")
    public Result<FanAnalysisRepository.FanRealTimeStats> getPublicRealTimeStats(
            @Parameter(description = "平台列表") @RequestParam(required = false) List<String> platforms,
            @Parameter(description = "数据权限范围") @RequestParam(required = false, defaultValue = "PUBLIC") String dataScope) {

        try {
            FanAnalysisRequest request = FanAnalysisRequest.builder()
                    .platforms(platforms)
                    .dataScope(dataScope)
                    .build();

            FanAnalysisRepository.FanRealTimeStats stats = fanAnalysisService.getRealTimeFanStats(request);
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取公共实时粉丝统计失败", e);
            return Result.error("获取公共实时粉丝统计失败: " + e.getMessage());
        }
    }
}
