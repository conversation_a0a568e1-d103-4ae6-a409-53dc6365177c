package com.foxit.crm.modules.fananalysis.domain.entity;

import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 粉丝分析聚合根
 * 用于表示粉丝分析的数据和统计信息
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
@Builder
public class FanAggregate {

    /**
     * 聚合根ID
     */
    private final String id;

    /**
     * 分析类型
     */
    private final FanAnalysisType analysisType;

    /**
     * 时间范围
     */
    private final TimeRange timeRange;

    /**
     * 平台列表
     */
    private final List<String> platforms;

    /**
     * 核心指标
     */
    private final Map<String, MetricValue> coreMetrics;

    /**
     * 粉丝列表数据
     */
    private final List<FanData> fanList;

    /**
     * 趋势数据
     */
    private final Map<String, TrendData> trendData;

    /**
     * 平台分析数据
     */
    private final Map<String, PlatformData> platformData;

    /**
     * 数据权限范围
     */
    private final String dataScope;

    /**
     * 最后更新时间
     */
    private final LocalDateTime lastUpdateTime;

    /**
     * 粉丝分析类型枚举
     */
    public enum FanAnalysisType {
        OVERVIEW("粉丝总览"),
        GROWTH("粉丝增长"),
        ACTIVITY("活跃度分析"),
        VALUE("价值分析"),
        PLATFORM("平台分析");

        private final String description;

        FanAnalysisType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 粉丝数据
     */
    @Getter
    @Builder
    public static class FanData {
        private final Long fanId;
        private final String fanCode;
        private final String nickname;
        private final String platform;
        private final String platformUserId;
        private final Integer gender;
        private final String ageRange;
        private final String region;
        private final String city;
        private final LocalDateTime followTime;
        private final LocalDateTime unfollowTime;
        private final String status;
        private final String source;
        private final String fanType;
        private final String fanValue;
        private final Integer activityLevel;
        private final Long interactionCount;
        private final LocalDateTime lastInteractionTime;
        private final List<String> tags;
        private final String remark;
    }

    /**
     * 趋势数据
     */
    @Getter
    @Builder
    public static class TrendData {
        private final String name;
        private final List<TrendPoint> points;
        private final String chartType;
    }

    /**
     * 趋势点数据
     */
    @Getter
    @Builder
    public static class TrendPoint {
        private final String date;
        private final Double value;
        private final String label;
    }

    /**
     * 平台数据
     */
    @Getter
    @Builder
    public static class PlatformData {
        private final String platformName;
        private final Long totalFans;
        private final Long newFans;
        private final Long unfollowedFans;
        private final Long activeFans;
        private final Long highValueFans;
        private final Long mediumValueFans;
        private final Long lowValueFans;
        private final Long totalInteractions;
        private final Double avgActivityLevel;
        private final Double retentionRate;
    }

    /**
     * 获取核心指标
     */
    public MetricValue getCoreMetric(String metricName) {
        return coreMetrics != null ? coreMetrics.get(metricName) : null;
    }

    /**
     * 获取趋势数据
     */
    public TrendData getTrendData(String trendName) {
        return trendData != null ? trendData.get(trendName) : null;
    }

    /**
     * 获取平台数据
     */
    public PlatformData getPlatformData(String platformName) {
        return platformData != null ? platformData.get(platformName) : null;
    }

    /**
     * 计算总活跃度
     */
    public Double calculateTotalActivityLevel() {
        if (fanList == null || fanList.isEmpty()) {
            return 0.0;
        }

        return fanList.stream()
                .filter(fan -> fan.getActivityLevel() != null)
                .mapToInt(FanData::getActivityLevel)
                .average()
                .orElse(0.0);
    }

    /**
     * 获取最活跃粉丝
     */
    public FanData getMostActiveFan() {
        if (fanList == null || fanList.isEmpty()) {
            return null;
        }

        return fanList.stream()
                .filter(fan -> fan.getActivityLevel() != null)
                .max((f1, f2) -> Integer.compare(f1.getActivityLevel(), f2.getActivityLevel()))
                .orElse(null);
    }

    /**
     * 获取高价值粉丝数量
     */
    public Long getHighValueFanCount() {
        if (fanList == null || fanList.isEmpty()) {
            return 0L;
        }

        return fanList.stream()
                .filter(fan -> "high".equals(fan.getFanValue()))
                .count();
    }

    /**
     * 获取数据摘要
     */
    public String getDataSummary() {
        int totalFans = fanList != null ? fanList.size() : 0;
        Double avgActivity = calculateTotalActivityLevel();
        Long highValueCount = getHighValueFanCount();
        
        return String.format("分析类型: %s, 时间范围: %s - %s, 粉丝总数: %d, 平均活跃度: %.2f, 高价值粉丝: %d",
                analysisType.getDescription(),
                timeRange != null ? timeRange.getStartDate() : "未知",
                timeRange != null ? timeRange.getEndDate() : "未知",
                totalFans,
                avgActivity,
                highValueCount);
    }

    /**
     * 创建粉丝总览分析
     */
    public static FanAggregate createOverview(TimeRange timeRange, List<String> platforms, String dataScope) {
        return FanAggregate.builder()
                .id(generateId("overview", timeRange))
                .analysisType(FanAnalysisType.OVERVIEW)
                .timeRange(timeRange)
                .platforms(platforms)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 生成聚合根ID
     */
    private static String generateId(String type, TimeRange timeRange) {
        return String.format("fan_%s_%s_%s", 
                type, 
                timeRange != null ? timeRange.getStartDate() : "unknown", 
                timeRange != null ? timeRange.getEndDate() : "unknown");
    }
}
