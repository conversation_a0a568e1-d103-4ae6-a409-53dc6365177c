package com.foxit.crm.modules.system.application.impl;

import com.foxit.crm.common.constant.RedisKeyConstant;
import com.foxit.crm.common.exception.BusinessException;
import com.foxit.crm.common.util.JwtUtil;
import com.foxit.crm.modules.system.api.dto.request.RegisterRequest;
import com.foxit.crm.modules.system.api.dto.response.LoginResponse;
import com.foxit.crm.modules.system.api.dto.response.UserInfoResponse;
import com.foxit.crm.modules.system.application.service.UserAccountService;
import com.foxit.crm.modules.system.domain.model.aggregate.User;
import com.foxit.crm.modules.system.domain.model.valueobject.*;
import com.foxit.crm.modules.system.domain.repository.UserRepository;
import com.foxit.crm.modules.system.infrastructure.persistence.converter.UserConverter;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * 用户账户应用服务实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
@Transactional
@RequiredArgsConstructor
public class UserAccountServiceImpl implements UserAccountService {

    private final UserRepository userRepository;
    private final RedissonClient redissonClient;
    private final UserConverter userConverter;

    @Value("${scrm.jwt.secret}")
    private String jwtSecret;

    @Value("${scrm.jwt.expire}")
    private Long jwtExpire;

    @Override
    public LoginResponse login(String username, String password, String loginIp) {
        // 参数验证
        if (!StringUtils.hasText(username) || !StringUtils.hasText(password)) {
            throw new BusinessException("用户名和密码不能为空");
        }

        // 查询用户
        Username usernameVO = new Username(username);
        Optional<User> userOpt = userRepository.findByUsername(usernameVO);
        if (userOpt.isEmpty()) {
            throw new BusinessException("用户不存在");
        }

        User user = userOpt.get();

        // 验证密码
        if (!user.validatePassword(password)) {
            throw new BusinessException("密码错误");
        }

        // 检查用户状态
        if (!user.isEnabled()) {
            throw new BusinessException("用户已被禁用");
        }

        // 更新最后登录信息
        user.updateLastLogin(loginIp);
        userRepository.save(user);

        // 生成JWT Token
        String token = JwtUtil.generateToken(
                user.getUserId().getValue(),
                user.getUsername().getValue(),
                user.getUserType(), // 添加用户类型
                jwtSecret,
                jwtExpire);

        // 将token存储到Redis
        String tokenKey = RedisKeyConstant.buildUserTokenKey(token);
        RBucket<Object> tokenBucket = redissonClient.getBucket(tokenKey);
        tokenBucket.setAsync(user.getUserId().getValue(), java.time.Duration.ofSeconds(jwtExpire));

        // 转换用户信息（用户信息缓存通过@Cacheable注解在getUserInfoByToken方法中处理）
        UserInfoResponse userInfo = userConverter.toUserInfoResponse(user);

        return new LoginResponse(token, jwtExpire, userInfo);
    }

    @Override
    public void register(RegisterRequest request) {
        // 检查用户名是否已存在
        Username username = new Username(request.getUsername());
        if (userRepository.existsByUsername(username)) {
            throw new BusinessException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (StringUtils.hasText(request.getEmail()) &&
                userRepository.existsByEmail(request.getEmail())) {
            throw new BusinessException("邮箱已被注册");
        }

        // 创建用户
        Password password = Password.fromRaw(request.getPassword());
        Email email = new Email(request.getEmail());

        User user = new User(null, username, password, request.getRealName(), email);
        user.setPhone(request.getPhone());

        userRepository.save(user);
    }

    @Override
    public void logout(String token) {
        if (StringUtils.hasText(token)) {
            String tokenKey = RedisKeyConstant.buildUserTokenKey(token);
            RBucket<Object> tokenBucket = redissonClient.getBucket(tokenKey);
            tokenBucket.deleteAsync();
        }
    }

    @Override
    public UserInfoResponse getUserInfoByToken(String token) {
        if (!StringUtils.hasText(token)) {
            throw new BusinessException("Token不能为空");
        }

        // 验证token
        if (!JwtUtil.validateToken(token)) {
            throw new BusinessException("Token无效");
        }

        // 检查token是否在Redis中存在
        String tokenKey = RedisKeyConstant.buildUserTokenKey(token);
        RBucket<Object> tokenBucket = redissonClient.getBucket(tokenKey);
        if (!tokenBucket.isExists()) {
            throw new BusinessException("Token已过期");
        }

        // 从token中获取用户ID
        Long userId = JwtUtil.getUserIdFromToken(token);

        // 使用Spring Cache获取用户信息（带缓存）
        return getUserInfoById(userId);
    }

    /**
     * 根据用户ID获取用户信息（带缓存）
     */
    @Cacheable(value = "userInfo", key = "#userId")
    private UserInfoResponse getUserInfoById(Long userId) {
        UserId userIdVO = new UserId(userId);
        Optional<User> userOpt = userRepository.findById(userIdVO);
        if (userOpt.isEmpty()) {
            throw new BusinessException("用户不存在");
        }

        User user = userOpt.get();
        if (!user.isEnabled()) {
            throw new BusinessException("用户已被禁用");
        }

        return userConverter.toUserInfoResponse(user);
    }
}
