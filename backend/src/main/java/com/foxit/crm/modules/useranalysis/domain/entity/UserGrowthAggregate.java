package com.foxit.crm.modules.useranalysis.domain.entity;

import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户增长聚合根
 * 封装用户增长分析的核心业务逻辑
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Getter
@Builder
public class UserGrowthAggregate {

    /**
     * 聚合根ID
     */
    private final String id;

    /**
     * 分析类型：NEW_USER, RETENTION, GROWTH_TREND, USER_SOURCE
     */
    private final UserGrowthAnalysisType analysisType;

    /**
     * 时间范围
     */
    private final TimeRange timeRange;

    /**
     * 产品线ID列表
     */
    private final List<Long> productLineIds;

    /**
     * 核心指标
     */
    private final Map<String, MetricValue> coreMetrics;

    /**
     * 趋势数据
     */
    private final Map<String, TrendData> trendData;

    /**
     * 留存数据
     */
    private final Map<String, RetentionData> retentionData;

    /**
     * 用户来源数据
     */
    private final Map<String, UserSourceData> userSourceData;

    /**
     * 数据权限范围
     */
    private final String dataScope;

    /**
     * 最后更新时间
     */
    private final LocalDateTime lastUpdateTime;

    /**
     * 用户增长分析类型枚举
     */
    public enum UserGrowthAnalysisType {
        NEW_USER("新增用户分析"),
        RETENTION("用户留存分析"),
        GROWTH_TREND("增长趋势分析"),
        USER_SOURCE("用户来源分析"),
        COHORT_RETENTION("队列留存分析");

        private final String description;

        UserGrowthAnalysisType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 趋势数据
     */
    @Getter
    @Builder
    public static class TrendData {
        private final String name;
        private final List<String> categories;
        private final List<Number> values;
        private final String unit;
        private final String chartType;
    }

    /**
     * 留存数据
     */
    @Getter
    @Builder
    public static class RetentionData {
        private final String name;
        private final List<RetentionPeriod> periods;
        private final String chartType;
    }

    /**
     * 留存周期数据
     */
    @Getter
    @Builder
    public static class RetentionPeriod {
        private final String period;
        private final Double retentionRate;
        private final Long userCount;
        private final Long baseUserCount;
    }

    /**
     * 用户来源数据
     */
    @Getter
    @Builder
    public static class UserSourceData {
        private final String name;
        private final List<UserSourceItem> sources;
        private final String chartType;
    }

    /**
     * 用户来源项
     */
    @Getter
    @Builder
    public static class UserSourceItem {
        private final String sourceName;
        private final Long userCount;
        private final Double percentage;
        private final String sourceType;
    }

    /**
     * 创建新增用户分析
     */
    public static UserGrowthAggregate createNewUserAnalysis(TimeRange timeRange, String dataScope) {
        return UserGrowthAggregate.builder()
                .id(generateId("newuser", timeRange))
                .analysisType(UserGrowthAnalysisType.NEW_USER)
                .timeRange(timeRange)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建留存分析
     */
    public static UserGrowthAggregate createRetentionAnalysis(TimeRange timeRange, String dataScope) {
        return UserGrowthAggregate.builder()
                .id(generateId("retention", timeRange))
                .analysisType(UserGrowthAnalysisType.RETENTION)
                .timeRange(timeRange)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建增长趋势分析
     */
    public static UserGrowthAggregate createGrowthTrendAnalysis(TimeRange timeRange, List<Long> productLineIds, String dataScope) {
        return UserGrowthAggregate.builder()
                .id(generateId("growth", timeRange))
                .analysisType(UserGrowthAnalysisType.GROWTH_TREND)
                .timeRange(timeRange)
                .productLineIds(productLineIds)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建用户来源分析
     */
    public static UserGrowthAggregate createUserSourceAnalysis(TimeRange timeRange, String dataScope) {
        return UserGrowthAggregate.builder()
                .id(generateId("source", timeRange))
                .analysisType(UserGrowthAnalysisType.USER_SOURCE)
                .timeRange(timeRange)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 获取核心指标
     */
    public MetricValue getCoreMetric(String metricName) {
        return coreMetrics != null ? coreMetrics.get(metricName) : null;
    }

    /**
     * 获取趋势数据
     */
    public TrendData getTrendData(String trendName) {
        return trendData != null ? trendData.get(trendName) : null;
    }

    /**
     * 获取留存数据
     */
    public RetentionData getRetentionData(String retentionName) {
        return retentionData != null ? retentionData.get(retentionName) : null;
    }

    /**
     * 获取用户来源数据
     */
    public UserSourceData getUserSourceData(String sourceName) {
        return userSourceData != null ? userSourceData.get(sourceName) : null;
    }

    /**
     * 生成聚合根ID
     */
    private static String generateId(String prefix, TimeRange timeRange) {
        return String.format("%s_%s_%s", 
                prefix, 
                timeRange.getStartDate().toString(), 
                timeRange.getEndDate().toString());
    }

    /**
     * 计算增长率
     */
    public Double calculateGrowthRate(String currentMetric, String previousMetric) {
        MetricValue current = getCoreMetric(currentMetric);
        MetricValue previous = getCoreMetric(previousMetric);
        
        if (current == null || previous == null || 
            current.getValue() == null || previous.getValue() == null ||
            previous.getValue().doubleValue() == 0) {
            return 0.0;
        }
        
        return ((current.getValue().doubleValue() - previous.getValue().doubleValue()) 
                / previous.getValue().doubleValue()) * 100;
    }

    /**
     * 计算平均留存率
     */
    public Double calculateAverageRetentionRate() {
        if (retentionData == null || retentionData.isEmpty()) {
            return 0.0;
        }
        
        return retentionData.values().stream()
                .flatMap(data -> data.getPeriods().stream())
                .mapToDouble(RetentionPeriod::getRetentionRate)
                .average()
                .orElse(0.0);
    }

    /**
     * 获取主要用户来源
     */
    public String getPrimaryUserSource() {
        if (userSourceData == null || userSourceData.isEmpty()) {
            return "未知";
        }
        
        return userSourceData.values().stream()
                .flatMap(data -> data.getSources().stream())
                .max((s1, s2) -> Long.compare(s1.getUserCount(), s2.getUserCount()))
                .map(UserSourceItem::getSourceName)
                .orElse("未知");
    }

    /**
     * 验证数据完整性
     */
    public boolean isDataComplete() {
        return id != null && 
               analysisType != null && 
               timeRange != null && 
               dataScope != null && 
               lastUpdateTime != null &&
               coreMetrics != null && 
               !coreMetrics.isEmpty();
    }

    /**
     * 获取数据摘要
     */
    public String getDataSummary() {
        if (!isDataComplete()) {
            return "数据不完整";
        }
        
        MetricValue newUsers = getCoreMetric("newUsers");
        MetricValue totalUsers = getCoreMetric("totalUsers");
        
        return String.format("分析类型: %s, 时间范围: %s - %s, 新增用户: %s, 总用户: %s",
                analysisType.getDescription(),
                timeRange.getStartDate(),
                timeRange.getEndDate(),
                newUsers != null ? newUsers.getValue() : "N/A",
                totalUsers != null ? totalUsers.getValue() : "N/A");
    }
}
