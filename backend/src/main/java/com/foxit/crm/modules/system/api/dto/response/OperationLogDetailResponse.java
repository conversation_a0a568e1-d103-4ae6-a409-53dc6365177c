package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 操作日志详情响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
public class OperationLogDetailResponse {

    /**
     * 日志ID
     */
    private Long id;

    /**
     * 操作标题
     */
    private String title;

    /**
     * 操作类型
     */
    private String operation;

    /**
     * 请求方法
     */
    private String method;

    /**
     * 请求方式
     */
    private String requestMethod;

    /**
     * 请求URL
     */
    private String requestUrl;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 响应结果
     */
    private String responseResult;

    /**
     * 操作用户ID
     */
    private Long userId;

    /**
     * 操作用户名
     */
    private String username;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 操作状态：0-失败，1-成功
     */
    private Integer status;

    /**
     * 操作状态描述
     */
    private String statusText;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 执行时长（毫秒）
     */
    private Long executionTime;

    /**
     * 执行时长描述
     */
    private String executionTimeText;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 是否为敏感操作
     */
    private Boolean isSensitive;

    /**
     * 是否为慢操作
     */
    private Boolean isSlow;

    /**
     * 设置操作状态文本
     */
    public void setStatus(Integer status) {
        this.status = status;
        this.statusText = status != null && status == 1 ? "成功" : "失败";
    }

    /**
     * 设置执行时长文本
     */
    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
        if (executionTime != null) {
            if (executionTime < 1000) {
                this.executionTimeText = executionTime + "ms";
            } else if (executionTime < 60000) {
                this.executionTimeText = String.format("%.2fs", executionTime / 1000.0);
            } else {
                long minutes = executionTime / 60000;
                long seconds = (executionTime % 60000) / 1000;
                this.executionTimeText = minutes + "m" + seconds + "s";
            }
            this.isSlow = executionTime > 5000;
        } else {
            this.executionTimeText = "-";
            this.isSlow = false;
        }
    }

    /**
     * 设置是否为敏感操作
     */
    public void setOperation(String operation) {
        this.operation = operation;
        if (operation != null) {
            String upperOperation = operation.toUpperCase();
            this.isSensitive = upperOperation.contains("DELETE") || 
                              upperOperation.contains("CREATE") || 
                              upperOperation.contains("UPDATE") || 
                              upperOperation.contains("LOGIN") || 
                              upperOperation.contains("LOGOUT") || 
                              upperOperation.contains("ASSIGN") || 
                              upperOperation.contains("REMOVE") || 
                              upperOperation.contains("ENABLE") || 
                              upperOperation.contains("DISABLE") || 
                              upperOperation.contains("RESET");
        } else {
            this.isSensitive = false;
        }
    }
}
