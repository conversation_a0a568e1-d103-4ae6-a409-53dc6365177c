package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;

import java.util.List;

/**
 * 操作日志列表响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
public class OperationLogListResponse {

    /**
     * 操作日志列表
     */
    private List<OperationLogDetailResponse> items;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Integer page;

    /**
     * 每页大小
     */
    private Integer size;

    /**
     * 总页数
     */
    private Integer totalPages;

    /**
     * 构造函数
     */
    public OperationLogListResponse(List<OperationLogDetailResponse> items, Long total, Integer page, Integer size) {
        this.items = items;
        this.total = total;
        this.page = page;
        this.size = size;
        this.totalPages = (int) Math.ceil((double) total / size);
    }
}
