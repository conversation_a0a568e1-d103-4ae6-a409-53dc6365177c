package com.foxit.crm.modules.system.domain.model.aggregate;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户角色关联聚合根
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@NoArgsConstructor
public class UserRole {

    /**
     * 关联ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 构造函数
     */
    public UserRole(Long userId, Long roleId) {
        this.userId = userId;
        this.roleId = roleId;
    }

    /**
     * 构造函数
     */
    public UserRole(Long userId, Long roleId, Long createBy) {
        this.userId = userId;
        this.roleId = roleId;
        this.createBy = createBy;
    }

    /**
     * 检查用户ID是否有效
     */
    public boolean isValidUserId() {
        return this.userId != null && this.userId > 0;
    }

    /**
     * 检查角色ID是否有效
     */
    public boolean isValidRoleId() {
        return this.roleId != null && this.roleId > 0;
    }

    /**
     * 检查关联是否有效
     */
    public boolean isValid() {
        return isValidUserId() && isValidRoleId();
    }
}
