package com.foxit.crm.modules.system.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.common.util.IpUtil;
import com.foxit.crm.modules.system.api.dto.request.LoginRequest;
import com.foxit.crm.modules.system.api.dto.response.LoginResponse;
import com.foxit.crm.modules.system.api.dto.response.UserInfoResponse;
import com.foxit.crm.modules.system.application.service.UserAccountService;
import com.foxit.crm.shared.domain.event.OperationLog;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户账户控制器 - 认证相关接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@RestController
@RequestMapping("/auth")
@Validated
@RequiredArgsConstructor
public class UserAccountController {

    private final UserAccountService userAccountService;

    /**
     * 用户登录 - 唯一允许未认证访问的接口
     */
    @PostMapping("/login")
    @OperationLog(value = "用户登录", operation = "LOGIN", saveParams = false)
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request,
            HttpServletRequest httpRequest) {
        String clientIp = IpUtil.getClientIp(httpRequest);
        LoginResponse response = userAccountService.login(request.getUsername(), request.getPassword(), clientIp);
        return Result.success(response);
    }

    /**
     * 用户登出 - 需要认证
     */
    @PostMapping("/logout")
    @PreAuthorize("isAuthenticated()")
    @OperationLog(value = "用户登出", operation = "LOGOUT")
    public Result<Void> logout(HttpServletRequest request) {
        String token = IpUtil.extractToken(request);
        userAccountService.logout(token);
        return Result.success();
    }

    /**
     * 获取当前用户信息 - 需要认证
     */
    @GetMapping("/user/info")
    @PreAuthorize("isAuthenticated()")
    @OperationLog(value = "获取用户信息", operation = "QUERY")
    public Result<UserInfoResponse> getUserInfo(HttpServletRequest request) {
        String token = IpUtil.extractToken(request);
        UserInfoResponse userInfo = userAccountService.getUserInfoByToken(token);
        return Result.success(userInfo);
    }

    /**
     * 健康检查 - 允许未认证访问
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("Authentication service is running");
    }
}
