package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品版本列表响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
public class ProductVersionListResponse {

    /**
     * 版本列表
     */
    private List<ProductVersionItem> items;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Integer page;

    /**
     * 每页大小
     */
    private Integer size;

    /**
     * 总页数
     */
    private Integer totalPages;

    /**
     * 产品版本列表项
     */
    @Data
    public static class ProductVersionItem {

        /**
         * 版本ID
         */
        private Long id;

        /**
         * 产品线ID
         */
        private Long productLineId;

        /**
         * 产品线名称
         */
        private String productLineName;

        /**
         * 版本号
         */
        private String versionNumber;

        /**
         * 版本名称
         */
        private String versionName;

        /**
         * 版本描述
         */
        private String description;

        /**
         * 版本类型：1-主版本，2-次版本，3-修订版，4-预发布版
         */
        private Integer versionType;

        /**
         * 版本类型名称
         */
        private String versionTypeName;

        /**
         * 版本状态：1-开发中，2-测试中，3-预发布，4-已发布，5-已废弃
         */
        private Integer status;

        /**
         * 版本状态名称
         */
        private String statusName;

        /**
         * 是否当前版本
         */
        private Boolean isCurrent;

        /**
         * 发布时间
         */
        private LocalDateTime releaseDate;

        /**
         * 计划发布时间
         */
        private LocalDateTime plannedDate;

        /**
         * 文件大小（字节）
         */
        private Long fileSize;

        /**
         * 格式化的文件大小
         */
        private String formattedFileSize;

        /**
         * 下载次数
         */
        private Integer downloadCount;

        /**
         * 支持平台列表
         */
        private List<String> platforms;

        /**
         * 创建人姓名
         */
        private String createdByName;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;

        /**
         * 更新时间
         */
        private LocalDateTime updateTime;

        /**
         * 是否可以删除
         */
        private Boolean canBeDeleted;

        /**
         * 是否可以发布
         */
        private Boolean canBeReleased;

        /**
         * 是否可以设为当前版本
         */
        private Boolean canBeSetAsCurrent;

        /**
         * 是否可以废弃
         */
        private Boolean canBeDeprecated;
    }
}
