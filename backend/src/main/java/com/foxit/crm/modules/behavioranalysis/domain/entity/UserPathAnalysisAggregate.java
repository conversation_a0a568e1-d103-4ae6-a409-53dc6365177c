package com.foxit.crm.modules.behavioranalysis.domain.entity;

import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 用户路径分析聚合根
 * 封装用户行为路径分析的核心业务逻辑
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Getter
@Builder
public class UserPathAnalysisAggregate {

    /**
     * 聚合根ID
     */
    private final String id;

    /**
     * 分析类型：PATH_FLOW, PATH_STATISTICS, ANOMALY_DETECTION, EFFICIENCY_ANALYSIS, PATH_COMPARISON
     */
    private final PathAnalysisType analysisType;

    /**
     * 时间范围
     */
    private final TimeRange timeRange;

    /**
     * 产品线ID列表
     */
    private final List<Long> productLineIds;

    /**
     * 起始节点列表
     */
    private final List<String> startNodes;

    /**
     * 结束节点列表
     */
    private final List<String> endNodes;

    /**
     * 核心指标
     */
    private final Map<String, MetricValue> coreMetrics;

    /**
     * 路径流向数据
     */
    private final Map<String, PathFlowData> pathFlowData;

    /**
     * 路径统计数据
     */
    private final Map<String, PathStatisticsData> pathStatisticsData;

    /**
     * 异常路径数据
     */
    private final Map<String, AnomalyPathData> anomalyPathData;

    /**
     * 路径效率数据
     */
    private final Map<String, PathEfficiencyData> pathEfficiencyData;

    /**
     * 路径对比数据
     */
    private final Map<String, PathComparisonData> pathComparisonData;

    /**
     * 数据权限范围
     */
    private final String dataScope;

    /**
     * 最后更新时间
     */
    private final LocalDateTime lastUpdateTime;

    /**
     * 路径分析类型枚举
     */
    public enum PathAnalysisType {
        PATH_FLOW("路径流向分析"),
        PATH_STATISTICS("路径统计分析"),
        ANOMALY_DETECTION("异常路径检测"),
        EFFICIENCY_ANALYSIS("路径效率分析"),
        PATH_COMPARISON("路径对比分析");

        private final String description;

        PathAnalysisType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 路径流向数据
     */
    @Getter
    @Builder
    public static class PathFlowData {
        private final String pathName;
        private final List<PathNode> nodes;
        private final List<PathLink> links;
        private final Long totalUsers;
        private final Double conversionRate;
        private final String flowDirection;
        private final Integer maxDepth;
        private final Double avgPathLength;
    }

    /**
     * 路径节点
     */
    @Getter
    @Builder
    public static class PathNode {
        private final String nodeId;
        private final String nodeName;
        private final String nodeType;
        private final Long userCount;
        private final Double percentage;
        private final Integer level;
        private final Double avgStayTime;
        private final Double bounceRate;
        private final String nodeCategory;
    }

    /**
     * 路径链接
     */
    @Getter
    @Builder
    public static class PathLink {
        private final String sourceNodeId;
        private final String targetNodeId;
        private final Long userCount;
        private final Double percentage;
        private final Double avgTransitionTime;
        private final Double conversionRate;
        private final String linkStrength;
        private final Double dropOffRate;
    }

    /**
     * 路径统计数据
     */
    @Getter
    @Builder
    public static class PathStatisticsData {
        private final String pathId;
        private final String pathName;
        private final Long totalPaths;
        private final Long uniqueUsers;
        private final Double avgPathLength;
        private final Double avgPathTime;
        private final Double completionRate;
        private final List<PathStep> commonSteps;
        private final List<String> topPaths;
        private final Map<String, Long> pathDistribution;
    }

    /**
     * 路径步骤
     */
    @Getter
    @Builder
    public static class PathStep {
        private final String stepId;
        private final String stepName;
        private final Integer stepOrder;
        private final Long userCount;
        private final Double percentage;
        private final Double avgTimeSpent;
        private final Double exitRate;
    }

    /**
     * 异常路径数据
     */
    @Getter
    @Builder
    public static class AnomalyPathData {
        private final String anomalyId;
        private final String anomalyType;
        private final String pathPattern;
        private final Long affectedUsers;
        private final Double anomalyScore;
        private final String severity;
        private final String description;
        private final List<String> anomalyNodes;
        private final LocalDateTime detectedTime;
        private final String recommendedAction;
    }

    /**
     * 路径效率数据
     */
    @Getter
    @Builder
    public static class PathEfficiencyData {
        private final String pathId;
        private final String pathName;
        private final Double efficiencyScore;
        private final Integer optimalSteps;
        private final Integer actualSteps;
        private final Double timeEfficiency;
        private final Double conversionEfficiency;
        private final List<EfficiencyMetric> metrics;
        private final List<String> optimizationSuggestions;
        private final String efficiencyLevel;
    }

    /**
     * 效率指标
     */
    @Getter
    @Builder
    public static class EfficiencyMetric {
        private final String metricName;
        private final Double metricValue;
        private final String metricUnit;
        private final Double benchmark;
        private final String performance;
    }

    /**
     * 路径对比数据
     */
    @Getter
    @Builder
    public static class PathComparisonData {
        private final String comparisonId;
        private final String comparisonName;
        private final List<PathComparisonItem> items;
        private final Map<String, Double> comparisonMetrics;
        private final String winnerPath;
        private final String comparisonSummary;
        private final LocalDateTime comparisonTime;
    }

    /**
     * 路径对比项
     */
    @Getter
    @Builder
    public static class PathComparisonItem {
        private final String pathId;
        private final String pathName;
        private final Long userCount;
        private final Double conversionRate;
        private final Double avgPathTime;
        private final Double efficiencyScore;
        private final String performance;
    }

    /**
     * 生成聚合根ID
     */
    private static String generateId(String prefix, TimeRange timeRange) {
        return String.format("%s_%s_%s_%s", 
            prefix, 
            timeRange.getStartDate(), 
            timeRange.getEndDate(),
            UUID.randomUUID().toString().substring(0, 8));
    }

    /**
     * 获取核心指标
     */
    public MetricValue getCoreMetric(String key) {
        return coreMetrics != null ? coreMetrics.get(key) : null;
    }

    /**
     * 创建路径流向分析
     */
    public static UserPathAnalysisAggregate createPathFlowAnalysis(TimeRange timeRange, List<String> startNodes, 
                                                                  List<String> endNodes, String dataScope) {
        return UserPathAnalysisAggregate.builder()
                .id(generateId("path_flow", timeRange))
                .analysisType(PathAnalysisType.PATH_FLOW)
                .timeRange(timeRange)
                .startNodes(startNodes)
                .endNodes(endNodes)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建路径统计分析
     */
    public static UserPathAnalysisAggregate createPathStatisticsAnalysis(TimeRange timeRange, List<String> startNodes, 
                                                                         List<String> endNodes, String dataScope) {
        return UserPathAnalysisAggregate.builder()
                .id(generateId("path_stats", timeRange))
                .analysisType(PathAnalysisType.PATH_STATISTICS)
                .timeRange(timeRange)
                .startNodes(startNodes)
                .endNodes(endNodes)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建异常路径检测
     */
    public static UserPathAnalysisAggregate createAnomalyDetection(TimeRange timeRange, List<String> startNodes, 
                                                                  List<String> endNodes, String dataScope) {
        return UserPathAnalysisAggregate.builder()
                .id(generateId("path_anomaly", timeRange))
                .analysisType(PathAnalysisType.ANOMALY_DETECTION)
                .timeRange(timeRange)
                .startNodes(startNodes)
                .endNodes(endNodes)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建路径效率分析
     */
    public static UserPathAnalysisAggregate createEfficiencyAnalysis(TimeRange timeRange, List<String> startNodes, 
                                                                    List<String> endNodes, String dataScope) {
        return UserPathAnalysisAggregate.builder()
                .id(generateId("path_efficiency", timeRange))
                .analysisType(PathAnalysisType.EFFICIENCY_ANALYSIS)
                .timeRange(timeRange)
                .startNodes(startNodes)
                .endNodes(endNodes)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建路径对比分析
     */
    public static UserPathAnalysisAggregate createPathComparison(TimeRange timeRange, List<String> startNodes, 
                                                                List<String> endNodes, String dataScope) {
        return UserPathAnalysisAggregate.builder()
                .id(generateId("path_comparison", timeRange))
                .analysisType(PathAnalysisType.PATH_COMPARISON)
                .timeRange(timeRange)
                .startNodes(startNodes)
                .endNodes(endNodes)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 验证聚合根是否有效
     */
    public boolean isValid() {
        return timeRange != null && 
               timeRange.isValid() && 
               analysisType != null &&
               dataScope != null && 
               !dataScope.trim().isEmpty() &&
               startNodes != null &&
               !startNodes.isEmpty();
    }

    /**
     * 验证数据完整性
     */
    public boolean isDataComplete() {
        return id != null && 
               analysisType != null && 
               timeRange != null && 
               dataScope != null && 
               lastUpdateTime != null &&
               coreMetrics != null && 
               !coreMetrics.isEmpty();
    }

    /**
     * 获取数据摘要
     */
    public String getDataSummary() {
        if (!isDataComplete()) {
            return "数据不完整";
        }
        
        MetricValue totalPaths = getCoreMetric("totalPaths");
        MetricValue uniqueUsers = getCoreMetric("uniqueUsers");
        
        return String.format("分析类型: %s, 时间范围: %s - %s, 总路径数: %s, 独立用户: %s",
                analysisType.getDescription(),
                timeRange.getStartDate(),
                timeRange.getEndDate(),
                totalPaths != null ? totalPaths.getValue() : "N/A",
                uniqueUsers != null ? uniqueUsers.getValue() : "N/A");
    }
}
