package com.foxit.crm.modules.system.api.dto.converter;

import com.foxit.crm.common.converter.AbstractDtoConverter;
import com.foxit.crm.modules.system.api.dto.response.SystemConfigDetailResponse;
import com.foxit.crm.modules.system.domain.model.aggregate.SystemConfig;
import org.springframework.stereotype.Component;

/**
 * 系统配置转换器
 *
 * 负责SystemConfig实体与SystemConfigDetailResponse DTO之间的转换
 * 继承AbstractDtoConverter以复用通用转换逻辑
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Component
public class SystemConfigConverter extends AbstractDtoConverter<SystemConfig, SystemConfigDetailResponse> {

    @Override
    protected Class<SystemConfigDetailResponse> getDtoClass() {
        return SystemConfigDetailResponse.class;
    }

    @Override
    protected void customizeDto(SystemConfig entity, SystemConfigDetailResponse dto) {
        // 设置基础字段（如果BeanUtils没有正确复制的话）
        dto.setId(entity.getId());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateTime(entity.getUpdateTime());

        // 这里可以添加其他自定义转换逻辑
        // 例如：状态枚举转换、计算字段等
    }
}
