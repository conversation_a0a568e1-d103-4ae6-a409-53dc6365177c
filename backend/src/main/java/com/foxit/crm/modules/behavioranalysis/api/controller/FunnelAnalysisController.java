package com.foxit.crm.modules.behavioranalysis.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.common.util.SecurityUtils;
import com.foxit.crm.modules.behavioranalysis.application.dto.FunnelAnalysisRequest;
import com.foxit.crm.modules.behavioranalysis.application.dto.FunnelAnalysisResponse;
import com.foxit.crm.modules.behavioranalysis.application.service.FunnelAnalysisService;
import com.foxit.crm.modules.behavioranalysis.domain.repository.FunnelAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 漏斗分析控制器
 * 提供漏斗分析相关的REST API接口
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@RestController
@RequestMapping("/behavior/funnel")
@RequiredArgsConstructor
@Tag(name = "漏斗分析", description = "漏斗分析相关接口")
public class FunnelAnalysisController {

    private final FunnelAnalysisService funnelAnalysisService;

    @Operation(summary = "获取漏斗转化分析", description = "获取漏斗转化分析数据，支持可视化漏斗图展示")
    @GetMapping("/conversion-analysis")
    @PreAuthorize("hasPermission('behavior:funnel', 'READ')")
    public Result<FunnelAnalysisResponse> getFunnelConversionAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "漏斗步骤列表") @RequestParam List<String> funnelSteps,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            FunnelAnalysisRequest request = FunnelAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .funnelSteps(funnelSteps)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("FUNNEL_CONVERSION")
                    .includeDetails(true)
                    .build();

            FunnelAnalysisResponse response = funnelAnalysisService.getFunnelConversionAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取漏斗转化分析失败", e);
            return Result.error("获取漏斗转化分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取漏斗对比分析", description = "对比多个漏斗的转化性能")
    @PostMapping("/comparison-analysis")
    @PreAuthorize("hasPermission('behavior:funnel', 'READ')")
    public Result<FunnelAnalysisResponse> getFunnelComparisonAnalysis(
            @RequestBody FunnelAnalysisRequest request) {

        try {
            request.setDataScope(SecurityUtils.getCurrentUserDataScope());
            request.setUserId(SecurityUtils.getCurrentUserId());
            request.setAnalysisType("FUNNEL_COMPARISON");
            request.setIncludeDetails(true);

            FunnelAnalysisResponse response = funnelAnalysisService.getFunnelComparisonAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取漏斗对比分析失败", e);
            return Result.error("获取漏斗对比分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取流失点分析", description = "分析漏斗中的关键流失点，提供优化建议")
    @GetMapping("/dropout-analysis")
    @PreAuthorize("hasPermission('behavior:funnel', 'READ')")
    public Result<FunnelAnalysisResponse> getDropoutAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "漏斗步骤列表") @RequestParam List<String> funnelSteps,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            FunnelAnalysisRequest request = FunnelAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .funnelSteps(funnelSteps)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("DROPOUT_ANALYSIS")
                    .includeDetails(true)
                    .build();

            FunnelAnalysisResponse response = funnelAnalysisService.getDropoutAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取流失点分析失败", e);
            return Result.error("获取流失点分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取队列漏斗分析", description = "按时间队列分析用户转化行为")
    @GetMapping("/cohort-analysis")
    @PreAuthorize("hasPermission('behavior:funnel', 'READ')")
    public Result<FunnelAnalysisResponse> getCohortFunnelAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "漏斗步骤列表") @RequestParam List<String> funnelSteps,
            @Parameter(description = "队列周期") @RequestParam String cohortPeriod,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            FunnelAnalysisRequest request = FunnelAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .funnelSteps(funnelSteps)
                    .cohortPeriod(cohortPeriod)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("COHORT_FUNNEL")
                    .includeDetails(true)
                    .build();

            FunnelAnalysisResponse response = funnelAnalysisService.getCohortFunnelAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取队列漏斗分析失败", e);
            return Result.error("获取队列漏斗分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取漏斗模板列表", description = "获取预定义的漏斗模板")
    @GetMapping("/templates")
    @PreAuthorize("hasPermission('behavior:funnel', 'READ')")
    public Result<List<FunnelAnalysisRepository.FunnelTemplate>> getFunnelTemplates(
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            String dataScope = SecurityUtils.getCurrentUserDataScope();
            List<FunnelAnalysisRepository.FunnelTemplate> templates = funnelAnalysisService.getFunnelTemplates(productLineIds, dataScope);
            return Result.success(templates);

        } catch (Exception e) {
            log.error("获取漏斗模板列表失败", e);
            return Result.error("获取漏斗模板列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取实时漏斗统计", description = "获取实时漏斗转化统计数据")
    @GetMapping("/realtime-stats")
    @PreAuthorize("hasPermission('behavior:funnel', 'READ')")
    public Result<FunnelAnalysisRepository.FunnelRealTimeStats> getRealTimeStats(
            @Parameter(description = "漏斗步骤列表") @RequestParam List<String> funnelSteps,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            String dataScope = SecurityUtils.getCurrentUserDataScope();
            FunnelAnalysisRepository.FunnelRealTimeStats stats = funnelAnalysisService.getRealTimeFunnelStats(funnelSteps, productLineIds, dataScope);
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取实时漏斗统计失败", e);
            return Result.error("获取实时漏斗统计失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取漏斗分析统计摘要", description = "获取漏斗分析的统计摘要数据")
    @GetMapping("/summary")
    @PreAuthorize("hasPermission('behavior:funnel', 'READ')")
    public Result<FunnelAnalysisRepository.FunnelAnalysisSummary> getSummary(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        try {
            FunnelAnalysisRequest request = FunnelAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            FunnelAnalysisRepository.FunnelAnalysisSummary summary = funnelAnalysisService.getFunnelAnalysisSummary(request);
            return Result.success(summary);

        } catch (Exception e) {
            log.error("获取漏斗分析统计摘要失败", e);
            return Result.error("获取漏斗分析统计摘要失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取漏斗优化建议", description = "获取基于数据分析的漏斗优化建议")
    @GetMapping("/optimization-suggestions")
    @PreAuthorize("hasPermission('behavior:funnel', 'READ')")
    public Result<List<FunnelAnalysisRepository.FunnelOptimizationSuggestion>> getOptimizationSuggestions(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "漏斗步骤列表") @RequestParam List<String> funnelSteps,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            FunnelAnalysisRequest request = FunnelAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .funnelSteps(funnelSteps)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            List<FunnelAnalysisRepository.FunnelOptimizationSuggestion> suggestions = funnelAnalysisService.getFunnelOptimizationSuggestions(request);
            return Result.success(suggestions);

        } catch (Exception e) {
            log.error("获取漏斗优化建议失败", e);
            return Result.error("获取漏斗优化建议失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取漏斗转化趋势", description = "获取漏斗转化的时间趋势数据")
    @GetMapping("/conversion-trends")
    @PreAuthorize("hasPermission('behavior:funnel', 'READ')")
    public Result<List<FunnelAnalysisRepository.FunnelConversionTrend>> getConversionTrends(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "漏斗步骤列表") @RequestParam List<String> funnelSteps,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            FunnelAnalysisRequest request = FunnelAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .funnelSteps(funnelSteps)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            List<FunnelAnalysisRepository.FunnelConversionTrend> trends = funnelAnalysisService.getFunnelConversionTrends(request);
            return Result.success(trends);

        } catch (Exception e) {
            log.error("获取漏斗转化趋势失败", e);
            return Result.error("获取漏斗转化趋势失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取漏斗用户细分分析", description = "按维度细分用户群体的漏斗表现")
    @GetMapping("/user-segment-analysis")
    @PreAuthorize("hasPermission('behavior:funnel', 'READ')")
    public Result<List<FunnelAnalysisRepository.FunnelUserSegmentAnalysis>> getUserSegmentAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "漏斗步骤列表") @RequestParam List<String> funnelSteps,
            @Parameter(description = "细分维度列表") @RequestParam List<String> segmentDimensions,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            FunnelAnalysisRequest request = FunnelAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .funnelSteps(funnelSteps)
                    .segmentDimensions(segmentDimensions)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            List<FunnelAnalysisRepository.FunnelUserSegmentAnalysis> analysis = funnelAnalysisService.getFunnelUserSegmentAnalysis(request);
            return Result.success(analysis);

        } catch (Exception e) {
            log.error("获取漏斗用户细分分析失败", e);
            return Result.error("获取漏斗用户细分分析失败: " + e.getMessage());
        }
    }

    // ==================== 公共接口（无需认证） ====================

    /**
     * 获取公共漏斗转化分析 - 无需认证
     */
    @Operation(summary = "获取公共漏斗转化分析", description = "获取漏斗转化分析数据（无需认证）")
    @GetMapping("/public/conversion-analysis")
    public Result<FunnelAnalysisRepository.FunnelAnalysisSummary> getPublicConversionAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "漏斗步骤列表") @RequestParam List<String> funnelSteps,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            FunnelAnalysisRequest request = FunnelAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .funnelSteps(funnelSteps)
                    .productLineIds(productLineIds)
                    .build();

            FunnelAnalysisRepository.FunnelAnalysisSummary analysis = funnelAnalysisService.getFunnelAnalysisSummary(request);
            return Result.success(analysis);

        } catch (Exception e) {
            log.error("获取公共漏斗转化分析失败", e);
            return Result.error("获取公共漏斗转化分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共实时统计 - 无需认证
     */
    @Operation(summary = "获取公共实时统计", description = "获取漏斗实时统计数据（无需认证）")
    @GetMapping("/public/realtime-stats")
    public Result<FunnelAnalysisRepository.FunnelRealTimeStats> getPublicRealTimeStats(
            @Parameter(description = "漏斗步骤列表") @RequestParam List<String> funnelSteps,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            FunnelAnalysisRepository.FunnelRealTimeStats stats = funnelAnalysisService.getRealTimeFunnelStats(funnelSteps, productLineIds, "PUBLIC");
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取公共实时统计失败", e);
            return Result.error("获取公共实时统计失败: " + e.getMessage());
        }
    }
}
