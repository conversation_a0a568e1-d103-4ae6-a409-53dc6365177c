package com.foxit.crm.modules.behavioranalysis.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.common.util.SecurityUtils;
import com.foxit.crm.modules.behavioranalysis.application.dto.UserPathAnalysisRequest;
import com.foxit.crm.modules.behavioranalysis.application.dto.UserPathAnalysisResponse;
import com.foxit.crm.modules.behavioranalysis.application.service.UserPathAnalysisService;
import com.foxit.crm.modules.behavioranalysis.domain.repository.UserPathAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 用户路径分析控制器
 * 提供用户路径分析相关的REST API接口
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@RestController
@RequestMapping("/behavior/path")
@RequiredArgsConstructor
@Tag(name = "用户路径分析", description = "用户路径分析相关接口")
public class UserPathAnalysisController {

    private final UserPathAnalysisService userPathAnalysisService;

    @Operation(summary = "获取路径流向分析", description = "获取用户行为路径的流向分析数据，支持桑基图展示")
    @GetMapping("/flow-analysis")
    @PreAuthorize("hasPermission('behavior:path', 'READ')")
    public Result<UserPathAnalysisResponse> getPathFlowAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "起始节点列表") @RequestParam List<String> startNodes,
            @Parameter(description = "结束节点列表") @RequestParam(required = false) List<String> endNodes,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            UserPathAnalysisRequest request = UserPathAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .startNodes(startNodes)
                    .endNodes(endNodes)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("PATH_FLOW")
                    .includeDetails(true)
                    .build();

            UserPathAnalysisResponse response = userPathAnalysisService.getPathFlowAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取路径流向分析失败", e);
            return Result.error("获取路径流向分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取路径统计分析", description = "获取用户路径的统计分析数据，包括路径长度、完成率等")
    @GetMapping("/statistics-analysis")
    @PreAuthorize("hasPermission('behavior:path', 'READ')")
    public Result<UserPathAnalysisResponse> getPathStatisticsAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "起始节点列表") @RequestParam List<String> startNodes,
            @Parameter(description = "结束节点列表") @RequestParam(required = false) List<String> endNodes,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            UserPathAnalysisRequest request = UserPathAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .startNodes(startNodes)
                    .endNodes(endNodes)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("PATH_STATISTICS")
                    .includeDetails(true)
                    .build();

            UserPathAnalysisResponse response = userPathAnalysisService.getPathStatisticsAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取路径统计分析失败", e);
            return Result.error("获取路径统计分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取异常路径检测", description = "检测用户行为路径中的异常模式，提供预警和建议")
    @GetMapping("/anomaly-detection")
    @PreAuthorize("hasPermission('behavior:path', 'READ')")
    public Result<UserPathAnalysisResponse> getAnomalyPathDetection(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "起始节点列表") @RequestParam List<String> startNodes,
            @Parameter(description = "结束节点列表") @RequestParam(required = false) List<String> endNodes,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            UserPathAnalysisRequest request = UserPathAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .startNodes(startNodes)
                    .endNodes(endNodes)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("ANOMALY_DETECTION")
                    .includeDetails(true)
                    .build();

            UserPathAnalysisResponse response = userPathAnalysisService.getAnomalyPathDetection(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取异常路径检测失败", e);
            return Result.error("获取异常路径检测失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取路径效率分析", description = "分析用户路径的效率，提供优化建议")
    @GetMapping("/efficiency-analysis")
    @PreAuthorize("hasPermission('behavior:path', 'READ')")
    public Result<UserPathAnalysisResponse> getPathEfficiencyAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "起始节点列表") @RequestParam List<String> startNodes,
            @Parameter(description = "结束节点列表") @RequestParam(required = false) List<String> endNodes,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            UserPathAnalysisRequest request = UserPathAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .startNodes(startNodes)
                    .endNodes(endNodes)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("EFFICIENCY_ANALYSIS")
                    .includeDetails(true)
                    .build();

            UserPathAnalysisResponse response = userPathAnalysisService.getPathEfficiencyAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取路径效率分析失败", e);
            return Result.error("获取路径效率分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取路径对比分析", description = "对比多个用户路径的性能表现")
    @PostMapping("/comparison-analysis")
    @PreAuthorize("hasPermission('behavior:path', 'READ')")
    public Result<UserPathAnalysisResponse> getPathComparisonAnalysis(
            @RequestBody UserPathAnalysisRequest request) {

        try {
            request.setDataScope(SecurityUtils.getCurrentUserDataScope());
            request.setUserId(SecurityUtils.getCurrentUserId());
            request.setAnalysisType("PATH_COMPARISON");
            request.setIncludeDetails(true);

            UserPathAnalysisResponse response = userPathAnalysisService.getPathComparisonAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取路径对比分析失败", e);
            return Result.error("获取路径对比分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取路径节点列表", description = "获取可用的路径节点列表")
    @GetMapping("/nodes")
    @PreAuthorize("hasPermission('behavior:path', 'READ')")
    public Result<List<UserPathAnalysisRepository.PathNodeInfo>> getPathNodeList(
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            String dataScope = SecurityUtils.getCurrentUserDataScope();
            List<UserPathAnalysisRepository.PathNodeInfo> nodeList = userPathAnalysisService.getPathNodeList(productLineIds, dataScope);
            return Result.success(nodeList);

        } catch (Exception e) {
            log.error("获取路径节点列表失败", e);
            return Result.error("获取路径节点列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取实时路径统计", description = "获取实时路径统计数据")
    @GetMapping("/realtime-stats")
    @PreAuthorize("hasPermission('behavior:path', 'READ')")
    public Result<UserPathAnalysisRepository.PathRealTimeStats> getRealTimeStats(
            @Parameter(description = "起始节点列表") @RequestParam(required = false) List<String> startNodes,
            @Parameter(description = "结束节点列表") @RequestParam(required = false) List<String> endNodes,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            String dataScope = SecurityUtils.getCurrentUserDataScope();
            UserPathAnalysisRepository.PathRealTimeStats stats = userPathAnalysisService.getRealTimePathStats(startNodes, endNodes, productLineIds, dataScope);
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取实时路径统计失败", e);
            return Result.error("获取实时路径统计失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取用户路径分析统计摘要", description = "获取用户路径分析的统计摘要数据")
    @GetMapping("/summary")
    @PreAuthorize("hasPermission('behavior:path', 'READ')")
    public Result<UserPathAnalysisRepository.UserPathAnalysisSummary> getSummary(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        try {
            UserPathAnalysisRequest request = UserPathAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            UserPathAnalysisRepository.UserPathAnalysisSummary summary = userPathAnalysisService.getUserPathAnalysisSummary(request);
            return Result.success(summary);

        } catch (Exception e) {
            log.error("获取用户路径分析统计摘要失败", e);
            return Result.error("获取用户路径分析统计摘要失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取路径优化建议", description = "获取基于数据分析的路径优化建议")
    @GetMapping("/optimization-suggestions")
    @PreAuthorize("hasPermission('behavior:path', 'READ')")
    public Result<List<UserPathAnalysisRepository.PathOptimizationSuggestion>> getOptimizationSuggestions(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "起始节点列表") @RequestParam List<String> startNodes,
            @Parameter(description = "结束节点列表") @RequestParam(required = false) List<String> endNodes,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            UserPathAnalysisRequest request = UserPathAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .startNodes(startNodes)
                    .endNodes(endNodes)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            List<UserPathAnalysisRepository.PathOptimizationSuggestion> suggestions = userPathAnalysisService.getPathOptimizationSuggestions(request);
            return Result.success(suggestions);

        } catch (Exception e) {
            log.error("获取路径优化建议失败", e);
            return Result.error("获取路径优化建议失败: " + e.getMessage());
        }
    }

    // ==================== 公共接口（无需认证） ====================

    /**
     * 获取公共用户路径流分析 - 无需认证
     */
    @Operation(summary = "获取公共用户路径流分析", description = "获取用户路径流分析数据（无需认证）")
    @GetMapping("/public/flow-analysis")
    public Result<UserPathAnalysisRepository.UserPathAnalysisSummary> getPublicPathFlowAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "起始节点列表") @RequestParam(required = false) List<String> startNodes,
            @Parameter(description = "结束节点列表") @RequestParam(required = false) List<String> endNodes,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            UserPathAnalysisRequest request = UserPathAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .startNodes(startNodes)
                    .endNodes(endNodes)
                    .productLineIds(productLineIds)
                    .build();

            UserPathAnalysisRepository.UserPathAnalysisSummary analysis = userPathAnalysisService.getUserPathAnalysisSummary(request);
            return Result.success(analysis);

        } catch (Exception e) {
            log.error("获取公共用户路径流分析失败", e);
            return Result.error("获取公共用户路径流分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共路径节点信息 - 无需认证
     */
    @Operation(summary = "获取公共路径节点信息", description = "获取可用的路径节点信息（无需认证）")
    @GetMapping("/public/nodes")
    public Result<List<UserPathAnalysisRepository.PathNodeInfo>> getPublicPathNodes(
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {
        try {
            List<UserPathAnalysisRepository.PathNodeInfo> nodes = userPathAnalysisService.getPathNodeList(productLineIds, "PUBLIC");
            return Result.success(nodes);

        } catch (Exception e) {
            log.error("获取公共路径节点信息失败", e);
            return Result.error("获取公共路径节点信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共实时统计 - 无需认证
     */
    @Operation(summary = "获取公共实时统计", description = "获取路径分析实时统计数据（无需认证）")
    @GetMapping("/public/realtime-stats")
    public Result<UserPathAnalysisRepository.PathRealTimeStats> getPublicRealTimeStats(
            @Parameter(description = "起始节点列表") @RequestParam(required = false) List<String> startNodes,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            UserPathAnalysisRepository.PathRealTimeStats stats = userPathAnalysisService.getRealTimePathStats(startNodes, null, productLineIds, "PUBLIC");
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取公共实时统计失败", e);
            return Result.error("获取公共实时统计失败: " + e.getMessage());
        }
    }
}
