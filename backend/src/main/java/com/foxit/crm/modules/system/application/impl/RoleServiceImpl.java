package com.foxit.crm.modules.system.application.impl;

import com.foxit.crm.common.exception.BusinessException;
import com.foxit.crm.modules.system.api.dto.request.RoleCreateRequest;
import com.foxit.crm.modules.system.api.dto.request.RoleUpdateRequest;
import com.foxit.crm.modules.system.api.dto.response.RoleDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.RoleSimpleResponse;
import com.foxit.crm.modules.system.api.dto.response.PageResponse;
import com.foxit.crm.modules.system.application.service.RoleService;
import com.foxit.crm.modules.system.domain.model.aggregate.Role;
import com.foxit.crm.modules.system.domain.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 角色应用服务实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
@RequiredArgsConstructor
public class RoleServiceImpl implements RoleService {

    private final RoleRepository roleRepository;

    @Override
    @Transactional
    public Long createRole(RoleCreateRequest request) {
        // 检查角色编码是否已存在
        if (roleRepository.existsByRoleCode(request.getRoleCode())) {
            throw new BusinessException("角色编码已存在");
        }

        // 创建角色领域对象
        Role role = new Role(
                request.getRoleName(),
                request.getRoleCode(),
                request.getDescription(),
                request.getStatus(),
                request.getSortOrder());

        // 保存角色
        Role savedRole = roleRepository.save(role);
        return savedRole.getId();
    }

    @Override
    @Transactional
    public void updateRole(Long id, RoleUpdateRequest request) {
        // 查找角色
        Role role = roleRepository.findById(id)
                .orElseThrow(() -> new BusinessException("角色不存在"));

        // 检查角色编码是否已被其他角色使用
        if (roleRepository.existsByRoleCodeAndIdNot(request.getRoleCode(), id)) {
            throw new BusinessException("角色编码已存在");
        }

        // 更新角色信息
        role.setRoleName(request.getRoleName());
        role.setRoleCode(request.getRoleCode());
        role.setDescription(request.getDescription());
        role.setStatus(request.getStatus());
        role.setSortOrder(request.getSortOrder());
        role.setVersion(request.getVersion());

        // 保存更新
        roleRepository.save(role);
    }

    @Override
    @Transactional
    public void deleteRole(Long id) {
        // 检查角色是否存在
        Role role = roleRepository.findById(id)
                .orElseThrow(() -> new BusinessException("角色不存在"));

        // TODO: 检查是否有用户关联此角色，如果有则不允许删除

        // 删除角色
        roleRepository.deleteById(id);
    }

    @Override
    public RoleDetailResponse getRoleById(Long id) {
        Role role = roleRepository.findById(id)
                .orElseThrow(() -> new BusinessException("角色不存在"));

        RoleDetailResponse response = new RoleDetailResponse();
        BeanUtils.copyProperties(role, response);
        response.setStatus(role.getStatus()); // 触发状态文本设置

        // TODO: 获取角色权限列表
        // response.setPermissionIds(rolePermissionService.getPermissionIdsByRoleId(id));

        return response;
    }

    @Override
    public PageResponse<RoleDetailResponse> getRoleList(int page, int size, String keyword) {
        List<Role> roles = roleRepository.findByPage(page, size, keyword);
        long total = roleRepository.count(keyword);

        List<RoleDetailResponse> roleResponses = roles.stream()
                .map(this::convertToDetailResponse)
                .collect(Collectors.toList());

        return new PageResponse<>(roleResponses, total, page, size);
    }

    @Override
    public List<RoleSimpleResponse> getAllEnabledRoles() {
        List<Role> roles = roleRepository.findAllEnabled();
        return roles.stream()
                .map(this::convertToSimpleResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<RoleSimpleResponse> getRolesByUserId(Long userId) {
        List<Role> roles = roleRepository.findByUserId(userId);
        return roles.stream()
                .map(this::convertToSimpleResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void enableRole(Long id) {
        Role role = roleRepository.findById(id)
                .orElseThrow(() -> new BusinessException("角色不存在"));

        role.enable();
        roleRepository.save(role);
    }

    @Override
    @Transactional
    public void disableRole(Long id) {
        Role role = roleRepository.findById(id)
                .orElseThrow(() -> new BusinessException("角色不存在"));

        role.disable();
        roleRepository.save(role);
    }

    @Override
    @Transactional
    public void assignPermissions(Long roleId, List<Long> permissionIds) {
        // 检查角色是否存在
        roleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException("角色不存在"));

        // TODO: 实现角色权限分配逻辑
        // rolePermissionService.assignPermissions(roleId, permissionIds);
    }

    @Override
    public List<Long> getRolePermissions(Long roleId) {
        // 检查角色是否存在
        roleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException("角色不存在"));

        // TODO: 实现获取角色权限逻辑
        // return rolePermissionService.getPermissionIdsByRoleId(roleId);
        return List.of();
    }

    @Override
    @Transactional
    public void removePermissions(Long roleId, List<Long> permissionIds) {
        // 检查角色是否存在
        roleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException("角色不存在"));

        // TODO: 实现移除角色权限逻辑
        // rolePermissionService.removePermissions(roleId, permissionIds);
    }

    @Override
    @Transactional
    public Long copyRole(Long roleId, String newName) {
        // 查找原角色
        Role originalRole = roleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException("角色不存在"));

        // 检查新角色名称是否已存在
        if (roleRepository.existsByRoleName(newName)) {
            throw new BusinessException("角色名称已存在");
        }

        // 创建新角色
        Role newRole = new Role(
                newName,
                originalRole.getRoleCode() + "_copy",
                originalRole.getDescription() + " (复制)",
                originalRole.getStatus(),
                originalRole.getSortOrder());

        // 保存新角色
        Role savedRole = roleRepository.save(newRole);

        // TODO: 复制角色权限
        // List<Long> permissionIds = getRolePermissions(roleId);
        // if (!permissionIds.isEmpty()) {
        // assignPermissions(savedRole.getId(), permissionIds);
        // }

        return savedRole.getId();
    }

    @Override
    public List<Map<String, Object>> getRoleUsers(Long roleId) {
        // 检查角色是否存在
        roleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException("角色不存在"));

        // TODO: 实现获取角色用户列表逻辑
        // return userRoleService.getUsersByRoleId(roleId);
        return List.of();
    }

    @Override
    @Transactional
    public void addUsersToRole(Long roleId, List<Long> userIds) {
        // 检查角色是否存在
        roleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException("角色不存在"));

        // TODO: 实现为角色添加用户逻辑
        // userRoleService.addUsersToRole(roleId, userIds);
    }

    @Override
    @Transactional
    public void removeUsersFromRole(Long roleId, List<Long> userIds) {
        // 检查角色是否存在
        roleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException("角色不存在"));

        // TODO: 实现从角色移除用户逻辑
        // userRoleService.removeUsersFromRole(roleId, userIds);
    }

    /**
     * 转换为详情响应对象
     */
    private RoleDetailResponse convertToDetailResponse(Role role) {
        RoleDetailResponse response = new RoleDetailResponse();
        BeanUtils.copyProperties(role, response);
        response.setStatus(role.getStatus()); // 触发状态文本设置
        return response;
    }

    /**
     * 转换为简单响应对象
     */
    private RoleSimpleResponse convertToSimpleResponse(Role role) {
        RoleSimpleResponse response = new RoleSimpleResponse();
        BeanUtils.copyProperties(role, response);
        response.setStatus(role.getStatus()); // 触发状态文本设置
        return response;
    }
}
