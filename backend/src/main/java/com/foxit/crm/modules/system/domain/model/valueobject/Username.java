package com.foxit.crm.modules.system.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.util.regex.Pattern;

/**
 * 用户名值对象
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Getter
@EqualsAndHashCode
@ToString
public class Username {

    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_]{3,50}$");

    private final String value;

    public Username(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("用户名不能为空");
        }

        String trimmedValue = value.trim();
        if (!USERNAME_PATTERN.matcher(trimmedValue).matches()) {
            throw new IllegalArgumentException("用户名只能包含字母、数字和下划线，长度为3-50个字符");
        }

        this.value = trimmedValue;
    }
}
