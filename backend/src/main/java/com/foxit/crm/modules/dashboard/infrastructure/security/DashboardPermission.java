package com.foxit.crm.modules.dashboard.infrastructure.security;

import java.lang.annotation.*;

/**
 * Dashboard权限注解
 * 用于标记需要权限控制的Dashboard方法
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DashboardPermission {

    /**
     * 权限代码
     */
    String value() default "";

    /**
     * 权限名称
     */
    String name() default "";

    /**
     * 是否需要产品线权限
     */
    boolean requireProductLineAccess() default false;

    /**
     * 数据权限范围
     */
    DataScope dataScope() default DataScope.ALL;

    /**
     * 是否记录操作日志
     */
    boolean logAccess() default true;

    /**
     * 访问频率限制（每分钟）
     */
    int rateLimit() default 100;

    /**
     * 数据权限范围枚举
     */
    enum DataScope {
        /**
         * 全部数据权限
         */
        ALL("all", "全部数据"),

        /**
         * 部门数据权限
         */
        DEPT("dept", "部门数据"),

        /**
         * 个人数据权限
         */
        OWN("own", "个人数据"),

        /**
         * 自定义数据权限
         */
        CUSTOM("custom", "自定义");

        private final String code;
        private final String name;

        DataScope(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}
