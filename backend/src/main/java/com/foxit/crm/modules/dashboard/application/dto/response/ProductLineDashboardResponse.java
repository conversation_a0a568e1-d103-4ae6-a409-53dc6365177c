package com.foxit.crm.modules.dashboard.application.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 产品线仪表盘响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductLineDashboardResponse {

    /**
     * 产品线基本信息
     */
    private ProductLineInfo productLineInfo;

    /**
     * 产品线核心指标
     */
    private ProductLineMetrics metrics;

    /**
     * 用户增长图表
     */
    private ChartDataResponse userGrowthChart;

    /**
     * 收入趋势图表
     */
    private ChartDataResponse revenueTrendChart;

    /**
     * 功能使用分析图表
     */
    private ChartDataResponse featureUsageChart;

    /**
     * 用户分布图表
     */
    private ChartDataResponse userDistributionChart;

    /**
     * 对比分析数据
     */
    private List<ProductLineComparison> comparisons;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 产品线基本信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ProductLineInfo {
        /**
         * 产品线ID
         */
        private Long id;

        /**
         * 产品线名称
         */
        private String name;

        /**
         * 产品线类型
         */
        private String type;

        /**
         * 产品线描述
         */
        private String description;

        /**
         * 负责人
         */
        private String owner;

        /**
         * 状态
         */
        private String status;
    }

    /**
     * 产品线指标内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ProductLineMetrics {
        /**
         * 总用户数
         */
        private Long totalUsers;

        /**
         * 活跃用户数
         */
        private Long activeUsers;

        /**
         * 新增用户数
         */
        private Long newUsers;

        /**
         * 总收入
         */
        private BigDecimal totalRevenue;

        /**
         * 下载量
         */
        private Long downloads;

        /**
         * 留存率
         */
        private BigDecimal retentionRate;

        /**
         * 用户增长率
         */
        private BigDecimal userGrowthRate;

        /**
         * 收入增长率
         */
        private BigDecimal revenueGrowthRate;

        /**
         * 市场份额
         */
        private BigDecimal marketShare;
    }

    /**
     * 产品线对比内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ProductLineComparison {
        /**
         * 对比产品线ID
         */
        private Long productLineId;

        /**
         * 对比产品线名称
         */
        private String productLineName;

        /**
         * 对比指标
         */
        private Map<String, BigDecimal> metrics;

        /**
         * 对比结果：better-更好, worse-更差, similar-相似
         */
        private String comparisonResult;
    }
}
