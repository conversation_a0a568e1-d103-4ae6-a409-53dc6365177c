package com.foxit.crm.modules.system.domain.repository;

import com.foxit.crm.modules.system.domain.model.aggregate.Permission;

import java.util.List;
import java.util.Optional;

/**
 * 权限仓储接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface PermissionRepository {

    /**
     * 保存权限
     */
    Permission save(Permission permission);

    /**
     * 根据ID查找权限
     */
    Optional<Permission> findById(Long id);

    /**
     * 根据权限编码查找权限
     */
    Optional<Permission> findByPermissionCode(String permissionCode);

    /**
     * 查找所有启用的权限
     */
    List<Permission> findAllEnabled();

    /**
     * 根据父权限ID查找子权限
     */
    List<Permission> findByParentId(Long parentId);

    /**
     * 查找所有根权限（父ID为0或null）
     */
    List<Permission> findRootPermissions();

    /**
     * 构建权限树
     */
    List<Permission> buildPermissionTree();

    /**
     * 分页查询权限
     */
    List<Permission> findByPage(int page, int size, String keyword, Integer permissionType);

    /**
     * 统计权限总数
     */
    long count(String keyword, Integer permissionType);

    /**
     * 根据角色ID查找权限列表
     */
    List<Permission> findByRoleId(Long roleId);

    /**
     * 根据用户ID查找权限列表
     */
    List<Permission> findByUserId(Long userId);

    /**
     * 根据权限类型查找权限
     */
    List<Permission> findByPermissionType(Integer permissionType);

    /**
     * 删除权限
     */
    void deleteById(Long id);

    /**
     * 检查权限编码是否存在
     */
    boolean existsByPermissionCode(String permissionCode);

    /**
     * 检查权限编码是否存在（排除指定ID）
     */
    boolean existsByPermissionCodeAndIdNot(String permissionCode, Long id);

    /**
     * 检查是否有子权限
     */
    boolean hasChildren(Long parentId);

    /**
     * 根据权限ID列表查找权限
     */
    List<Permission> findByIds(List<Long> ids);
}
