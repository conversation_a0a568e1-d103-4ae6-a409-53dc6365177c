package com.foxit.crm.modules.system.application.service;

import java.util.List;

/**
 * 角色权限关联应用服务接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface RolePermissionService {

    /**
     * 为角色分配权限
     */
    void assignPermissionsToRole(Long roleId, List<Long> permissionIds);

    /**
     * 为角色分配单个权限
     */
    void assignPermissionToRole(Long roleId, Long permissionId);

    /**
     * 移除角色的权限
     */
    void removePermissionFromRole(Long roleId, Long permissionId);

    /**
     * 移除角色的所有权限
     */
    void removeAllPermissionsFromRole(Long roleId);

    /**
     * 为权限分配角色
     */
    void assignRolesToPermission(Long permissionId, List<Long> roleIds);

    /**
     * 移除权限的角色
     */
    void removeRoleFromPermission(Long permissionId, Long roleId);

    /**
     * 移除权限的所有角色
     */
    void removeAllRolesFromPermission(Long permissionId);

    /**
     * 获取角色的权限ID列表
     */
    List<Long> getRolePermissionIds(Long roleId);

    /**
     * 获取权限的角色ID列表
     */
    List<Long> getPermissionRoleIds(Long permissionId);

    /**
     * 检查角色是否拥有指定权限
     */
    boolean hasPermission(Long roleId, Long permissionId);

    /**
     * 检查角色是否拥有指定权限中的任意一个
     */
    boolean hasAnyPermission(Long roleId, List<Long> permissionIds);

    /**
     * 检查角色是否拥有所有指定权限
     */
    boolean hasAllPermissions(Long roleId, List<Long> permissionIds);

    /**
     * 统计权限下的角色数量
     */
    long countRolesByPermission(Long permissionId);

    /**
     * 统计角色的权限数量
     */
    long countPermissionsByRole(Long roleId);

    /**
     * 批量删除角色的权限关联
     */
    void removeRolePermissionsByRoleIds(List<Long> roleIds);

    /**
     * 批量删除权限的角色关联
     */
    void removeRolePermissionsByPermissionIds(List<Long> permissionIds);

    /**
     * 根据角色ID列表获取权限ID列表
     */
    List<Long> getPermissionIdsByRoleIds(List<Long> roleIds);

    /**
     * 根据权限ID列表获取角色ID列表
     */
    List<Long> getRoleIdsByPermissionIds(List<Long> permissionIds);
}
