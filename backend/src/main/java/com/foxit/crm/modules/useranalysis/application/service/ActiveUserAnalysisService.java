package com.foxit.crm.modules.useranalysis.application.service;

import com.foxit.crm.modules.useranalysis.application.dto.ActiveUserAnalysisRequest;
import com.foxit.crm.modules.useranalysis.application.dto.ActiveUserAnalysisResponse;
import com.foxit.crm.modules.useranalysis.domain.entity.ActiveUserAggregate;
import com.foxit.crm.modules.useranalysis.domain.repository.ActiveUserRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 活跃用户分析应用服务
 * 协调活跃用户分析的业务流程
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ActiveUserAnalysisService {

    private final ActiveUserRepository activeUserRepository;

    /**
     * 获取活跃用户总览
     */
    @Cacheable(value = "activeUser:overview", key = "#request.cacheKey()")
    public ActiveUserAnalysisResponse getActiveUserOverview(ActiveUserAnalysisRequest request) {
        log.info("获取活跃用户总览数据: {}", request);

        try {
            // 验证请求参数
            validateRequest(request);

            // 构建时间范围
            TimeRange timeRange = buildTimeRange(request);

            // 获取数据
            Optional<ActiveUserAggregate> aggregateOpt = activeUserRepository
                    .getActiveUserOverview(timeRange, request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                log.warn("未找到活跃用户总览数据: timeRange={}, dataScope={}", timeRange, request.getDataScope());
                return ActiveUserAnalysisResponse.empty("未找到活跃用户数据");
            }

            ActiveUserAggregate aggregate = aggregateOpt.get();

            // 转换为响应对象
            ActiveUserAnalysisResponse response = convertToResponse(aggregate);

            log.info("成功获取活跃用户总览数据: 活跃用户数={}",
                    aggregate.getCoreMetric("activeUsers") != null ? aggregate.getCoreMetric("activeUsers").getValue()
                            : "N/A");

            return response;

        } catch (Exception e) {
            log.error("获取活跃用户总览数据失败: {}", e.getMessage(), e);
            return ActiveUserAnalysisResponse.error("获取活跃用户总览数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取活跃用户趋势
     */
    @Cacheable(value = "activeUser:trends", key = "#request.cacheKey()")
    public ActiveUserAnalysisResponse getActiveUserTrends(ActiveUserAnalysisRequest request) {
        log.info("获取活跃用户趋势数据: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<ActiveUserAggregate> aggregateOpt = activeUserRepository
                    .getActiveUserTrends(timeRange, request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                return ActiveUserAnalysisResponse.empty("未找到活跃用户趋势数据");
            }

            return convertToResponse(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取活跃用户趋势数据失败: {}", e.getMessage(), e);
            return ActiveUserAnalysisResponse.error("获取活跃用户趋势数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取活跃用户分布
     */
    @Cacheable(value = "activeUser:distribution", key = "#request.cacheKey()")
    public ActiveUserAnalysisResponse getActiveUserDistribution(ActiveUserAnalysisRequest request) {
        log.info("获取活跃用户分布数据: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<ActiveUserAggregate> aggregateOpt = activeUserRepository
                    .getActiveUserDistribution(timeRange, request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                return ActiveUserAnalysisResponse.empty("未找到活跃用户分布数据");
            }

            return convertToResponse(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取活跃用户分布数据失败: {}", e.getMessage(), e);
            return ActiveUserAnalysisResponse.error("获取活跃用户分布数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取活跃频次分析
     */
    @Cacheable(value = "activeUser:frequency", key = "#request.cacheKey()")
    public ActiveUserAnalysisResponse getActiveUserFrequency(ActiveUserAnalysisRequest request) {
        log.info("获取活跃频次分析数据: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<ActiveUserAggregate> aggregateOpt = activeUserRepository
                    .getActiveUserFrequency(timeRange, request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                return ActiveUserAnalysisResponse.empty("未找到活跃频次分析数据");
            }

            return convertToResponse(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取活跃频次分析数据失败: {}", e.getMessage(), e);
            return ActiveUserAnalysisResponse.error("获取活跃频次分析数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品线对比
     */
    @Cacheable(value = "activeUser:comparison", key = "#request.cacheKey()")
    public ActiveUserAnalysisResponse getActiveUserComparison(ActiveUserAnalysisRequest request) {
        log.info("获取产品线活跃用户对比数据: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<ActiveUserAggregate> aggregateOpt = activeUserRepository
                    .getActiveUserComparison(timeRange, request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                return ActiveUserAnalysisResponse.empty("未找到产品线对比数据");
            }

            return convertToResponse(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取产品线活跃用户对比数据失败: {}", e.getMessage(), e);
            return ActiveUserAnalysisResponse.error("获取产品线对比数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取活跃用户留存
     */
    @Cacheable(value = "activeUser:retention", key = "#request.cacheKey()")
    public ActiveUserAnalysisResponse getActiveUserRetention(ActiveUserAnalysisRequest request) {
        log.info("获取活跃用户留存数据: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<ActiveUserAggregate> aggregateOpt = activeUserRepository
                    .getActiveUserRetention(timeRange, request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                return ActiveUserAnalysisResponse.empty("未找到活跃用户留存数据");
            }

            return convertToResponse(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取活跃用户留存数据失败: {}", e.getMessage(), e);
            return ActiveUserAnalysisResponse.error("获取活跃用户留存数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取队列分析
     */
    @Cacheable(value = "activeUser:cohort", key = "#request.cacheKey()")
    public ActiveUserAnalysisResponse getActiveUserCohort(ActiveUserAnalysisRequest request) {
        log.info("获取活跃用户队列分析数据: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<ActiveUserAggregate> aggregateOpt = activeUserRepository
                    .getActiveUserCohort(timeRange, request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                return ActiveUserAnalysisResponse.empty("未找到队列分析数据");
            }

            return convertToResponse(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取活跃用户队列分析数据失败: {}", e.getMessage(), e);
            return ActiveUserAnalysisResponse.error("获取队列分析数据失败: " + e.getMessage());
        }
    }

    /**
     * 导出活跃用户数据
     */
    public byte[] exportActiveUserData(ActiveUserAnalysisRequest request) {
        log.info("导出活跃用户数据: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            // 获取所有相关数据
            List<ActiveUserAggregate> aggregates = activeUserRepository
                    .batchGetActiveUserData(List.of(timeRange), request.getProductLineIds(), request.getDataScope());

            // 转换为Excel格式
            return convertToExcel(aggregates);

        } catch (Exception e) {
            log.error("导出活跃用户数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出活跃用户数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(ActiveUserAnalysisRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }

        if (request.getStartDate() == null || request.getEndDate() == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }

        if (request.getStartDate().isAfter(request.getEndDate())) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }

        if (request.getDataScope() == null || request.getDataScope().trim().isEmpty()) {
            throw new IllegalArgumentException("数据权限范围不能为空");
        }
    }

    /**
     * 构建时间范围
     */
    private TimeRange buildTimeRange(ActiveUserAnalysisRequest request) {
        return TimeRange.of(
                request.getStartDate(),
                request.getEndDate(),
                request.getGranularity() != null ? request.getGranularity() : TimeRange.TimeGranularity.DAY);
    }

    /**
     * 转换为响应对象
     */
    private ActiveUserAnalysisResponse convertToResponse(ActiveUserAggregate aggregate) {
        return ActiveUserAnalysisResponse.builder()
                .success(true)
                .data(aggregate)
                .message("获取数据成功")
                .timestamp(java.time.LocalDateTime.now())
                .build();
    }

    /**
     * 转换为Excel格式
     */
    private byte[] convertToExcel(List<ActiveUserAggregate> aggregates) {
        // TODO: 实现Excel导出逻辑
        return new byte[0];
    }
}
