package com.foxit.crm.modules.dashboard.domain.valueobject;

import lombok.Value;
import lombok.Builder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * 指标值对象
 * 封装指标的值、单位、变化率等信息
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Value
@Builder
public class MetricValue {

    /**
     * 指标值
     */
    BigDecimal value;

    /**
     * 单位
     */
    String unit;

    /**
     * 变化率（百分比）
     */
    BigDecimal changeRate;

    /**
     * 对比值
     */
    BigDecimal comparisonValue;

    /**
     * 创建指标值对象
     */
    public static MetricValue of(BigDecimal value, String unit) {
        return MetricValue.builder()
                .value(value)
                .unit(unit)
                .changeRate(BigDecimal.ZERO)
                .comparisonValue(BigDecimal.ZERO)
                .build();
    }

    /**
     * 创建带对比的指标值对象
     */
    public static MetricValue withComparison(BigDecimal value, String unit, BigDecimal comparisonValue) {
        BigDecimal changeRate = calculateChangeRate(value, comparisonValue);
        return MetricValue.builder()
                .value(value)
                .unit(unit)
                .changeRate(changeRate)
                .comparisonValue(comparisonValue)
                .build();
    }

    /**
     * 计算变化率
     */
    private static BigDecimal calculateChangeRate(BigDecimal current, BigDecimal previous) {
        if (previous == null || previous.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        return current.subtract(previous)
                .divide(previous, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 获取趋势
     */
    public String getTrend() {
        if (changeRate.compareTo(BigDecimal.ZERO) > 0) {
            return "up";
        } else if (changeRate.compareTo(BigDecimal.ZERO) < 0) {
            return "down";
        } else {
            return "stable";
        }
    }

    /**
     * 是否异常（变化率超过阈值）
     */
    public boolean isAbnormal(BigDecimal threshold) {
        return changeRate.abs().compareTo(threshold) > 0;
    }

    /**
     * 格式化显示值
     */
    public String getFormattedValue() {
        if (value.compareTo(BigDecimal.valueOf(10000)) >= 0) {
            return value.divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP) + "万" + unit;
        }
        return value.toString() + unit;
    }

    /**
     * 格式化变化率
     */
    public String getFormattedChangeRate() {
        String sign = changeRate.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "";
        return sign + changeRate + "%";
    }

    /**
     * 验证指标值的有效性
     */
    public boolean isValid() {
        return value != null && value.compareTo(BigDecimal.ZERO) >= 0 && 
               unit != null && !unit.trim().isEmpty();
    }
}
