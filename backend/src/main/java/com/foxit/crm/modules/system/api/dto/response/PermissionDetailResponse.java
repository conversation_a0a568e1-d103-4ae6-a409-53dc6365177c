package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 权限详情响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
public class PermissionDetailResponse {

    /**
     * 权限ID
     */
    private Long id;

    /**
     * 父权限ID
     */
    private Long parentId;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 权限编码
     */
    private String permissionCode;

    /**
     * 权限类型：1-菜单，2-按钮，3-接口
     */
    private Integer permissionType;

    /**
     * 权限类型描述
     */
    private String permissionTypeText;

    /**
     * 路由路径
     */
    private String path;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 图标
     */
    private String icon;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusText;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 版本号（乐观锁）
     */
    private Integer version;

    /**
     * 设置权限类型文本
     */
    public void setPermissionType(Integer permissionType) {
        this.permissionType = permissionType;
        if (permissionType != null) {
            switch (permissionType) {
                case 1:
                    this.permissionTypeText = "菜单";
                    break;
                case 2:
                    this.permissionTypeText = "按钮";
                    break;
                case 3:
                    this.permissionTypeText = "接口";
                    break;
                default:
                    this.permissionTypeText = "未知";
                    break;
            }
        }
    }

    /**
     * 设置状态文本
     */
    public void setStatus(Integer status) {
        this.status = status;
        this.statusText = status != null && status == 1 ? "启用" : "禁用";
    }
}
