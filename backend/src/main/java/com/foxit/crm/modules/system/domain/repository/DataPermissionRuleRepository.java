package com.foxit.crm.modules.system.domain.repository;

import com.foxit.crm.modules.system.domain.model.aggregate.DataPermissionRule;

import java.util.List;
import java.util.Optional;

/**
 * 数据权限规则仓储接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface DataPermissionRuleRepository {

    /**
     * 保存数据权限规则
     */
    DataPermissionRule save(DataPermissionRule dataPermissionRule);

    /**
     * 根据ID查找数据权限规则
     */
    Optional<DataPermissionRule> findById(Long id);

    /**
     * 根据规则编码查找数据权限规则
     */
    Optional<DataPermissionRule> findByRuleCode(String ruleCode);

    /**
     * 查找所有启用的数据权限规则
     */
    List<DataPermissionRule> findAllEnabled();

    /**
     * 根据权限类型查找数据权限规则
     */
    List<DataPermissionRule> findByPermissionType(Integer permissionType);

    /**
     * 根据表名查找数据权限规则
     */
    List<DataPermissionRule> findByTableName(String tableName);

    /**
     * 根据用户ID查找数据权限规则
     */
    List<DataPermissionRule> findByUserId(Long userId);

    /**
     * 根据角色ID查找数据权限规则
     */
    List<DataPermissionRule> findByRoleId(Long roleId);

    /**
     * 根据产品线ID查找数据权限规则
     */
    List<DataPermissionRule> findByProductLineId(Long productLineId);

    /**
     * 根据用户ID和表名查找适用的数据权限规则
     */
    List<DataPermissionRule> findApplicableRules(Long userId, String tableName);

    /**
     * 根据用户ID和表名查找适用的数据权限规则（包含角色规则）
     */
    List<DataPermissionRule> findApplicableRulesWithRoles(Long userId, List<Long> roleIds, String tableName);

    /**
     * 分页查询数据权限规则
     */
    List<DataPermissionRule> findByPage(int page, int size, String keyword, Integer permissionType, 
                                       Integer permissionScope, Long roleId, Long userId, Integer status);

    /**
     * 统计数据权限规则总数
     */
    long count(String keyword, Integer permissionType, Integer permissionScope, 
              Long roleId, Long userId, Integer status);

    /**
     * 删除数据权限规则
     */
    void deleteById(Long id);

    /**
     * 检查规则编码是否存在
     */
    boolean existsByRuleCode(String ruleCode);

    /**
     * 检查规则编码是否存在（排除指定ID）
     */
    boolean existsByRuleCodeAndIdNot(String ruleCode, Long id);

    /**
     * 根据状态查找数据权限规则
     */
    List<DataPermissionRule> findByStatus(Integer status);

    /**
     * 批量更新规则状态
     */
    void updateStatusBatch(List<Long> ids, Integer status);

    /**
     * 根据优先级排序查找规则
     */
    List<DataPermissionRule> findByTableNameOrderByPriority(String tableName);

    /**
     * 根据用户ID和权限类型查找规则
     */
    List<DataPermissionRule> findByUserIdAndPermissionType(Long userId, Integer permissionType);

    /**
     * 根据角色ID和权限类型查找规则
     */
    List<DataPermissionRule> findByRoleIdAndPermissionType(Long roleId, Integer permissionType);

    /**
     * 查找行级权限规则
     */
    List<DataPermissionRule> findRowLevelRules(Long userId, List<Long> roleIds, String tableName);

    /**
     * 查找列级权限规则
     */
    List<DataPermissionRule> findColumnLevelRules(Long userId, List<Long> roleIds, String tableName);

    /**
     * 查找查询权限规则
     */
    List<DataPermissionRule> findQueryLevelRules(Long userId, List<Long> roleIds, String tableName);
}
