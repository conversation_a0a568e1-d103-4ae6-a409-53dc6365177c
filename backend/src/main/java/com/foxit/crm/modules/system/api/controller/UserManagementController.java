package com.foxit.crm.modules.system.api.controller;

import com.foxit.crm.common.annotation.DataPermission;
import com.foxit.crm.common.exception.Result;
import com.foxit.crm.common.util.PageResult;
import com.foxit.crm.modules.system.api.dto.request.RegisterRequest;
import com.foxit.crm.modules.system.api.dto.response.UserInfoResponse;
import com.foxit.crm.modules.system.api.dto.response.PageResponse;
import com.foxit.crm.modules.system.application.service.UserAccountService;
import com.foxit.crm.modules.system.application.service.UserManagementService;
import com.foxit.crm.shared.domain.event.OperationLog;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户管理控制器 - 管理员专用
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@RestController
@RequestMapping("/admin/users")
@Validated
@RequiredArgsConstructor
@PreAuthorize("hasAuthority('ADMIN')") // 整个控制器只允许管理员访问
public class UserManagementController {

    private final UserAccountService userAccountService;
    private final UserManagementService userManagementService;

    /**
     * 创建用户（管理员专用）
     * 原来的注册接口移到这里，只有管理员可以创建新用户
     */
    @PostMapping("/create")
    @OperationLog(value = "管理员创建用户", operation = "CREATE_USER", saveParams = false)
    public Result<String> createUser(@Valid @RequestBody RegisterRequest request) {
        userAccountService.register(request);
        return Result.success("用户创建成功");
    }

    /**
     * 更新用户状态（RESTful风格）
     * 支持启用(ENABLED)、禁用(DISABLED)等状态
     */
    @PutMapping("/{userId}/status")
    @OperationLog(value = "更新用户状态", operation = "UPDATE_USER_STATUS")
    public Result<String> updateUserStatus(@PathVariable Long userId, @RequestParam String status) {
        switch (status.toUpperCase()) {
            case "ENABLED":
                userManagementService.enableUser(userId);
                return Result.success("用户已启用");
            case "DISABLED":
                userManagementService.disableUser(userId);
                return Result.success("用户已禁用");
            default:
                return Result.error("不支持的状态: " + status);
        }
    }

    /**
     * 重置用户密码（RESTful风格）
     */
    @PutMapping("/{userId}/password")
    @OperationLog(value = "重置用户密码", operation = "RESET_PASSWORD")
    public Result<String> resetPassword(@PathVariable Long userId) {
        String newPassword = userManagementService.resetPassword(userId);
        return Result.success("密码重置成功，新密码：" + newPassword);
    }

    /**
     * 获取用户详情
     */
    @GetMapping("/{userId}")
    @OperationLog(value = "查看用户详情", operation = "VIEW_USER")
    public Result<UserInfoResponse> getUserById(@PathVariable Long userId) {
        UserInfoResponse userInfo = userManagementService.getUserById(userId);
        return Result.success(userInfo);
    }

    /**
     * 获取用户列表
     */
    @GetMapping
    @OperationLog(value = "查看用户列表", operation = "LIST_USERS")
    @DataPermission(dataScope = DataPermission.DataScope.DEPT_AND_CHILD, deptColumn = "dept_id")
    public Result<PageResponse<UserInfoResponse>> getUserList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword) {

        PageResult<UserInfoResponse> pageResult = userManagementService.getUserList(page, size, keyword);

        // 转换为前端预期的数据结构，补充扩展字段
        List<UserInfoResponse> items = pageResult.getRecords().stream()
                .map(userInfo -> UserInfoResponse.builder()
                        .id(userInfo.getId())
                        .username(userInfo.getUsername())
                        .realName(userInfo.getRealName())
                        .email(userInfo.getEmail())
                        .phone(userInfo.getPhone())
                        .avatar(userInfo.getAvatar())
                        .status(userInfo.getStatus())
                        .userType(userInfo.getUserType())
                        .deptId(userInfo.getDeptId())
                        .lastLoginTime(userInfo.getLastLoginTime())
                        .lastLoginIp(userInfo.getLastLoginIp())
                        .createTime(userInfo.getCreateTime())
                        // 扩展字段
                        .company("") // 暂时为空，后续可扩展
                        .department("") // 暂时为空，后续可扩展
                        .roles(userInfo.getUserType() == 1 ? new String[] { "管理员" } : new String[] { "普通用户" })
                        .permissions(new String[] {}) // 暂时为空，后续可扩展权限功能
                        .online(false) // 暂时为false，后续可扩展在线状态功能
                        .remark("") // 暂时为空，后续可扩展
                        .build())
                .collect(Collectors.toList());

        PageResponse<UserInfoResponse> response = new PageResponse<>(
                items,
                pageResult.getTotal(),
                pageResult.getPageNum(),
                pageResult.getPageSize());

        return Result.success(response);
    }
}
