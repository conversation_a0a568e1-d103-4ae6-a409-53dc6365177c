package com.foxit.crm.modules.system.infrastructure.persistence.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 产品线持久化对象
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product_line")
public class ProductLinePO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 产品线编码
     */
    @TableField("code")
    private String code;

    /**
     * 产品线名称
     */
    @TableField("name")
    private String name;

    /**
     * 产品线描述
     */
    @TableField("description")
    private String description;

    /**
     * 产品线类型：1-阅读器，2-编辑器，3-云服务，4-工具类，5-内容平台
     */
    @TableField("type")
    private Integer type;

    /**
     * 产品线状态：0-禁用，1-启用
     */
    @TableField("status")
    private Integer status;

    /**
     * 排序号
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 产品线图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 产品线颜色
     */
    @TableField("color")
    private String color;

    /**
     * 负责人ID
     */
    @TableField("owner_id")
    private Long ownerId;

    /**
     * 负责人姓名
     */
    @TableField("owner_name")
    private String ownerName;

    /**
     * 数据源配置
     */
    @TableField("data_source_config")
    private String dataSourceConfig;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 更新人ID
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 逻辑删除标志：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 版本号（乐观锁）
     */
    @Version
    @TableField("version")
    private Integer version;
}
