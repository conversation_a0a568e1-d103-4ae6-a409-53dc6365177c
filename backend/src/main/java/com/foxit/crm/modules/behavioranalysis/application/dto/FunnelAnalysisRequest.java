package com.foxit.crm.modules.behavioranalysis.application.dto;

import com.foxit.crm.modules.behavioranalysis.domain.repository.FunnelAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 漏斗分析请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Builder
@Schema(description = "漏斗分析请求")
public class FunnelAnalysisRequest {

    @Schema(description = "开始日期", example = "2025-01-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2025-01-31")
    private LocalDate endDate;

    @Schema(description = "时间粒度", example = "DAY")
    private TimeRange.TimeGranularity granularity;

    @Schema(description = "产品线ID列表")
    private List<Long> productLineIds;

    @Schema(description = "漏斗步骤列表")
    private List<String> funnelSteps;

    @Schema(description = "漏斗组列表（用于对比分析）")
    private List<FunnelAnalysisRepository.FunnelGroup> funnelGroups;

    @Schema(description = "队列周期")
    private String cohortPeriod;

    @Schema(description = "细分维度列表")
    private List<String> segmentDimensions;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "数据权限范围")
    private String dataScope;

    @Schema(description = "是否包含对比数据")
    private Boolean includeComparison;

    @Schema(description = "是否包含详细数据")
    private Boolean includeDetails;

    @Schema(description = "分析类型")
    private String analysisType;

    @Schema(description = "筛选条件")
    private FunnelFilterCondition filterCondition;

    /**
     * 漏斗筛选条件
     */
    @Data
    @Builder
    @Schema(description = "漏斗筛选条件")
    public static class FunnelFilterCondition {

        @Schema(description = "事件类型")
        private List<String> eventTypes;

        @Schema(description = "事件分类")
        private List<String> eventCategories;

        @Schema(description = "用户群体ID")
        private List<String> userSegmentIds;

        @Schema(description = "设备类型")
        private List<String> deviceTypes;

        @Schema(description = "操作系统")
        private List<String> operatingSystems;

        @Schema(description = "应用版本")
        private List<String> appVersions;

        @Schema(description = "地域")
        private List<String> regions;

        @Schema(description = "渠道")
        private List<String> channels;

        @Schema(description = "最小转化率")
        private Double minConversionRate;

        @Schema(description = "最大转化率")
        private Double maxConversionRate;

        @Schema(description = "最小用户数")
        private Integer minUserCount;

        @Schema(description = "最大用户数")
        private Integer maxUserCount;

        @Schema(description = "最小转化时间（分钟）")
        private Integer minConversionTime;

        @Schema(description = "最大转化时间（分钟）")
        private Integer maxConversionTime;

        @Schema(description = "自定义属性筛选")
        private List<PropertyFilter> propertyFilters;
    }

    /**
     * 属性筛选条件
     */
    @Data
    @Builder
    @Schema(description = "属性筛选条件")
    public static class PropertyFilter {

        @Schema(description = "属性名称")
        private String propertyName;

        @Schema(description = "操作符", example = "equals, contains, greater_than, less_than")
        private String operator;

        @Schema(description = "属性值")
        private String propertyValue;

        @Schema(description = "属性值列表（用于in操作）")
        private List<String> propertyValues;
    }

    /**
     * 创建漏斗转化分析请求
     */
    public static FunnelAnalysisRequest createFunnelConversionRequest(LocalDate startDate, LocalDate endDate, 
                                                                     List<String> funnelSteps, String dataScope) {
        return FunnelAnalysisRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .funnelSteps(funnelSteps)
                .dataScope(dataScope)
                .analysisType("FUNNEL_CONVERSION")
                .includeComparison(false)
                .includeDetails(true)
                .build();
    }

    /**
     * 创建漏斗对比分析请求
     */
    public static FunnelAnalysisRequest createFunnelComparisonRequest(LocalDate startDate, LocalDate endDate, 
                                                                     List<FunnelAnalysisRepository.FunnelGroup> funnelGroups, String dataScope) {
        return FunnelAnalysisRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .funnelGroups(funnelGroups)
                .dataScope(dataScope)
                .analysisType("FUNNEL_COMPARISON")
                .includeComparison(true)
                .includeDetails(true)
                .build();
    }

    /**
     * 创建流失点分析请求
     */
    public static FunnelAnalysisRequest createDropoutAnalysisRequest(LocalDate startDate, LocalDate endDate, 
                                                                    List<String> funnelSteps, String dataScope) {
        return FunnelAnalysisRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .funnelSteps(funnelSteps)
                .dataScope(dataScope)
                .analysisType("DROPOUT_ANALYSIS")
                .includeComparison(false)
                .includeDetails(true)
                .build();
    }

    /**
     * 创建队列漏斗分析请求
     */
    public static FunnelAnalysisRequest createCohortFunnelRequest(LocalDate startDate, LocalDate endDate, 
                                                                 List<String> funnelSteps, String cohortPeriod, String dataScope) {
        return FunnelAnalysisRequest.builder()
                .startDate(startDate)
                .endDate(endDate)
                .granularity(TimeRange.TimeGranularity.DAY)
                .funnelSteps(funnelSteps)
                .cohortPeriod(cohortPeriod)
                .dataScope(dataScope)
                .analysisType("COHORT_FUNNEL")
                .includeComparison(false)
                .includeDetails(true)
                .build();
    }

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        if (startDate == null || endDate == null) {
            return false;
        }
        
        if (startDate.isAfter(endDate)) {
            return false;
        }
        
        if (dataScope == null || dataScope.trim().isEmpty()) {
            return false;
        }
        
        // 根据分析类型验证特定参数
        if (analysisType != null) {
            switch (analysisType) {
                case "FUNNEL_CONVERSION":
                case "DROPOUT_ANALYSIS":
                case "COHORT_FUNNEL":
                    return funnelSteps != null && funnelSteps.size() >= 2 && funnelSteps.size() <= 10;
                case "FUNNEL_COMPARISON":
                    return funnelGroups != null && !funnelGroups.isEmpty();
                default:
                    return true;
            }
        }
        
        return true;
    }

    /**
     * 获取请求摘要
     */
    public String getSummary() {
        return String.format("漏斗分析请求: 类型=%s, 时间范围=%s至%s, 漏斗步骤数=%d", 
                analysisType != null ? analysisType : "未指定",
                startDate != null ? startDate.toString() : "未指定",
                endDate != null ? endDate.toString() : "未指定",
                funnelSteps != null ? funnelSteps.size() : 0);
    }

    /**
     * 检查是否为有效漏斗
     */
    public boolean isValidFunnel() {
        return funnelSteps != null && funnelSteps.size() >= 2 && funnelSteps.size() <= 10;
    }

    /**
     * 获取漏斗步骤数量
     */
    public int getStepCount() {
        return funnelSteps != null ? funnelSteps.size() : 0;
    }

    /**
     * 检查是否包含对比分析
     */
    public boolean hasComparison() {
        return Boolean.TRUE.equals(includeComparison) || 
               (funnelGroups != null && funnelGroups.size() > 1);
    }

    /**
     * 检查是否为队列分析
     */
    public boolean isCohortAnalysis() {
        return "COHORT_FUNNEL".equals(analysisType) && 
               cohortPeriod != null && !cohortPeriod.trim().isEmpty();
    }

    /**
     * 检查是否有细分维度
     */
    public boolean hasSegmentation() {
        return segmentDimensions != null && !segmentDimensions.isEmpty();
    }

    /**
     * 检查是否有筛选条件
     */
    public boolean hasFilter() {
        return filterCondition != null;
    }
}
