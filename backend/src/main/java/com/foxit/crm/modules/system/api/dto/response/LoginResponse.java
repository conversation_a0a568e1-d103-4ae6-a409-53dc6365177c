package com.foxit.crm.modules.system.api.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 登录响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = { "accessToken" }) // 排除访问令牌，避免日志泄露
public class LoginResponse {

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";

    /**
     * 过期时间（秒）
     */
    private Long expiresIn;

    /**
     * 用户信息
     */
    private UserInfoResponse userInfo;

    public LoginResponse(String accessToken, Long expiresIn, UserInfoResponse userInfo) {
        this.accessToken = accessToken;
        this.expiresIn = expiresIn;
        this.userInfo = userInfo;
    }
}
