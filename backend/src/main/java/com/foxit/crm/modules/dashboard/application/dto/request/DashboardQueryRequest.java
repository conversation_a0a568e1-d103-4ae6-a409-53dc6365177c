package com.foxit.crm.modules.dashboard.application.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDate;
import java.util.List;

/**
 * Dashboard查询请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DashboardQueryRequest {

    /**
     * 查询类型：overview-总览, product-产品线, realtime-实时
     */
    private String queryType;

    /**
     * 产品线ID列表（产品线仪表盘时使用）
     */
    private List<Long> productLineIds;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 时间粒度：hour-小时, day-日, week-周, month-月
     */
    private String timeGranularity;

    /**
     * 指标类型列表
     */
    private List<String> metricTypes;

    /**
     * 是否包含对比数据
     */
    private Boolean includeComparison;

    /**
     * 对比周期类型：previous-上一周期, same_last_year-去年同期
     */
    private String comparisonType;

    /**
     * 数据权限范围：all-全部, own-本人, dept-部门
     */
    private String dataScope;
}
