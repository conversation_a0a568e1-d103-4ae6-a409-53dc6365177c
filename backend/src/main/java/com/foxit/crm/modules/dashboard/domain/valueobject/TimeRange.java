package com.foxit.crm.modules.dashboard.domain.valueobject;

import lombok.Value;
import lombok.Builder;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

/**
 * 时间范围值对象
 * 封装查询的时间范围和粒度
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Value
@Builder
public class TimeRange {

    /**
     * 开始时间
     */
    LocalDateTime startTime;

    /**
     * 结束时间
     */
    LocalDateTime endTime;

    /**
     * 时间粒度：HOUR, DAY, WEEK, MONTH
     */
    TimeGranularity granularity;

    /**
     * 时间粒度枚举
     */
    public enum TimeGranularity {
        HOUR("hour", "小时"),
        DAY("day", "日"),
        WEEK("week", "周"),
        MONTH("month", "月");

        private final String code;
        private final String name;

        TimeGranularity(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static TimeGranularity fromCode(String code) {
            for (TimeGranularity granularity : values()) {
                if (granularity.code.equals(code)) {
                    return granularity;
                }
            }
            return DAY; // 默认按天
        }
    }

    /**
     * 创建今日时间范围
     */
    public static TimeRange today() {
        LocalDate today = LocalDate.now();
        return TimeRange.builder()
                .startTime(today.atStartOfDay())
                .endTime(today.plusDays(1).atStartOfDay().minusSeconds(1))
                .granularity(TimeGranularity.HOUR)
                .build();
    }

    /**
     * 创建最近N天的时间范围
     */
    public static TimeRange lastDays(int days) {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(days - 1);
        return TimeRange.builder()
                .startTime(startDate.atStartOfDay())
                .endTime(endDate.plusDays(1).atStartOfDay().minusSeconds(1))
                .granularity(TimeGranularity.DAY)
                .build();
    }

    /**
     * 创建本月时间范围
     */
    public static TimeRange thisMonth() {
        LocalDate today = LocalDate.now();
        LocalDate firstDay = today.withDayOfMonth(1);
        LocalDate lastDay = today.withDayOfMonth(today.lengthOfMonth());
        return TimeRange.builder()
                .startTime(firstDay.atStartOfDay())
                .endTime(lastDay.plusDays(1).atStartOfDay().minusSeconds(1))
                .granularity(TimeGranularity.DAY)
                .build();
    }

    /**
     * 创建自定义时间范围
     */
    public static TimeRange custom(LocalDate startDate, LocalDate endDate, String granularityCode) {
        return TimeRange.builder()
                .startTime(startDate.atStartOfDay())
                .endTime(endDate.plusDays(1).atStartOfDay().minusSeconds(1))
                .granularity(TimeGranularity.fromCode(granularityCode))
                .build();
    }

    /**
     * 获取时间跨度（天数）
     */
    public long getDaysBetween() {
        return ChronoUnit.DAYS.between(startTime.toLocalDate(), endTime.toLocalDate()) + 1;
    }

    /**
     * 获取对比时间范围（上一周期）
     */
    public TimeRange getPreviousPeriod() {
        long days = getDaysBetween();
        LocalDateTime newEndTime = startTime.minusSeconds(1);
        LocalDateTime newStartTime = newEndTime.minusDays(days - 1).toLocalDate().atStartOfDay();
        
        return TimeRange.builder()
                .startTime(newStartTime)
                .endTime(newEndTime)
                .granularity(this.granularity)
                .build();
    }

    /**
     * 获取去年同期时间范围
     */
    public TimeRange getSameLastYear() {
        return TimeRange.builder()
                .startTime(startTime.minusYears(1))
                .endTime(endTime.minusYears(1))
                .granularity(this.granularity)
                .build();
    }

    /**
     * 验证时间范围的有效性
     */
    public boolean isValid() {
        return startTime != null && endTime != null && 
               startTime.isBefore(endTime) && granularity != null;
    }

    /**
     * 是否为实时查询（当天）
     */
    public boolean isRealTime() {
        LocalDate today = LocalDate.now();
        return startTime.toLocalDate().equals(today) && 
               endTime.toLocalDate().equals(today);
    }
}
