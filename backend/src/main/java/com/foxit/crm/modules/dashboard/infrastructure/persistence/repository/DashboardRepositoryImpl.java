package com.foxit.crm.modules.dashboard.infrastructure.persistence.repository;

import com.foxit.crm.modules.dashboard.domain.entity.DashboardAggregate;
import com.foxit.crm.modules.dashboard.domain.repository.DashboardRepository;
import com.foxit.crm.modules.dashboard.domain.valueobject.MetricValue;
import com.foxit.crm.modules.dashboard.domain.valueobject.TimeRange;
import com.foxit.crm.modules.dashboard.application.dto.response.OverviewDashboardResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Dashboard仓储实现
 * 提供Dashboard数据访问的具体实现
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
@Repository("dashboardRepositoryMock") // 明确命名为Mock实现
@RequiredArgsConstructor
public class DashboardRepositoryImpl implements DashboardRepository {

        @Override
        public Map<String, MetricValue> getCoreMetrics(TimeRange timeRange, List<Long> productLineIds,
                        String dataScope) {
                log.debug("获取核心指标数据: timeRange={}, productLineIds={}, dataScope={}", timeRange, productLineIds,
                                dataScope);

                // 模拟数据 - 实际实现中应该从数据库查询
                Map<String, MetricValue> metrics = new HashMap<>();

                // 总用户数
                metrics.put("totalUsers", MetricValue.withComparison(
                                new BigDecimal("125680"), "人", new BigDecimal("115890")));

                // 活跃用户数
                metrics.put("activeUsers", MetricValue.withComparison(
                                new BigDecimal("89420"), "人", new BigDecimal("79650")));

                // 总收入
                metrics.put("totalRevenue", MetricValue.withComparison(
                                new BigDecimal("2856789.50"), "元", new BigDecimal("2467890.30")));

                // 转化率
                metrics.put("conversionRate", MetricValue.withComparison(
                                new BigDecimal("3.45"), "%", new BigDecimal("3.52")));

                return metrics;
        }

        @Override
        public DashboardAggregate.ChartData getUserGrowthData(TimeRange timeRange, List<Long> productLineIds,
                        String dataScope) {
                log.debug("获取用户增长数据: timeRange={}, productLineIds={}, dataScope={}", timeRange, productLineIds,
                                dataScope);

                // 模拟数据
                List<String> labels = Arrays.asList("01-01", "01-02", "01-03", "01-04", "01-05", "01-06", "01-07");
                List<BigDecimal> userData = Arrays.asList(
                                new BigDecimal("1200"), new BigDecimal("1350"), new BigDecimal("1180"),
                                new BigDecimal("1420"), new BigDecimal("1680"), new BigDecimal("1520"),
                                new BigDecimal("1750"));

                DashboardAggregate.ChartData.SeriesData series = DashboardAggregate.ChartData.SeriesData.builder()
                                .name("用户增长")
                                .data(userData)
                                .color("#1890ff")
                                .type("line")
                                .build();

                return DashboardAggregate.ChartData.builder()
                                .chartType("line")
                                .title("用户增长趋势")
                                .categories(labels)
                                .series(Arrays.asList(series))
                                .options(new HashMap<>())
                                .build();
        }

        @Override
        public DashboardAggregate.ChartData getRevenueTrendData(TimeRange timeRange, List<Long> productLineIds,
                        String dataScope) {
                log.debug("获取收入趋势数据: timeRange={}, productLineIds={}, dataScope={}", timeRange, productLineIds,
                                dataScope);

                // 模拟数据
                List<String> labels = Arrays.asList("01-01", "01-02", "01-03", "01-04", "01-05", "01-06", "01-07");
                List<BigDecimal> revenueData = Arrays.asList(
                                new BigDecimal("45000"), new BigDecimal("52000"), new BigDecimal("48000"),
                                new BigDecimal("58000"), new BigDecimal("65000"), new BigDecimal("62000"),
                                new BigDecimal("70000"));

                DashboardAggregate.ChartData.SeriesData series = DashboardAggregate.ChartData.SeriesData.builder()
                                .name("收入趋势")
                                .data(revenueData)
                                .color("#52c41a")
                                .type("bar")
                                .build();

                return DashboardAggregate.ChartData.builder()
                                .chartType("bar")
                                .title("收入趋势")
                                .categories(labels)
                                .series(Arrays.asList(series))
                                .options(new HashMap<>())
                                .build();
        }

        @Override
        public DashboardAggregate.ChartData getProductLineDistributionData(TimeRange timeRange, String dataScope) {
                log.debug("获取产品线分布数据: timeRange={}, dataScope={}", timeRange, dataScope);

                // 模拟数据
                List<String> labels = Arrays.asList("PDF Reader", "PDF Editor", "PDF Creator", "PDF Converter", "其他");
                List<BigDecimal> distributionData = Arrays.asList(
                                new BigDecimal("35"), new BigDecimal("25"), new BigDecimal("20"),
                                new BigDecimal("15"), new BigDecimal("5"));

                DashboardAggregate.ChartData.SeriesData series = DashboardAggregate.ChartData.SeriesData.builder()
                                .name("产品线分布")
                                .data(distributionData)
                                .color("#722ed1")
                                .type("pie")
                                .build();

                return DashboardAggregate.ChartData.builder()
                                .chartType("pie")
                                .title("产品线分布")
                                .categories(labels)
                                .series(Arrays.asList(series))
                                .options(new HashMap<>())
                                .build();
        }

        @Override
        public DashboardAggregate.ChartData getUserTypeDistributionData(TimeRange timeRange, List<Long> productLineIds,
                        String dataScope) {
                log.debug("获取用户类型分布数据: timeRange={}, productLineIds={}, dataScope={}", timeRange, productLineIds,
                                dataScope);

                // 模拟数据
                List<String> labels = Arrays.asList("免费用户", "付费用户", "企业用户", "试用用户");
                List<BigDecimal> userTypeData = Arrays.asList(
                                new BigDecimal("45"), new BigDecimal("30"), new BigDecimal("20"), new BigDecimal("5"));

                DashboardAggregate.ChartData.SeriesData series = DashboardAggregate.ChartData.SeriesData.builder()
                                .name("用户类型分布")
                                .data(userTypeData)
                                .color("#fa8c16")
                                .type("doughnut")
                                .build();

                return DashboardAggregate.ChartData.builder()
                                .chartType("doughnut")
                                .title("用户类型分布")
                                .categories(labels)
                                .series(Arrays.asList(series))
                                .options(new HashMap<>())
                                .build();
        }

        @Override
        public DashboardAggregate.ChartData getRealTimeOnlineData(String dataScope) {
                log.debug("获取实时在线用户数据: dataScope={}", dataScope);

                // 模拟实时数据
                List<String> labels = new ArrayList<>();
                List<BigDecimal> onlineUsers = new ArrayList<>();

                LocalDateTime now = LocalDateTime.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");

                // 生成最近30分钟的数据
                for (int i = 29; i >= 0; i--) {
                        LocalDateTime time = now.minusMinutes(i);
                        labels.add(time.format(formatter));

                        // 模拟在线用户数波动
                        int baseUsers = 1500;
                        int variation = (int) (Math.random() * 200 - 100);
                        onlineUsers.add(new BigDecimal(baseUsers + variation));
                }

                DashboardAggregate.ChartData.SeriesData series = DashboardAggregate.ChartData.SeriesData.builder()
                                .name("在线用户")
                                .data(onlineUsers)
                                .color("#13c2c2")
                                .type("line")
                                .build();

                return DashboardAggregate.ChartData.builder()
                                .chartType("line")
                                .title("实时在线用户")
                                .categories(labels)
                                .series(Arrays.asList(series))
                                .options(new HashMap<>())
                                .build();
        }

        @Override
        public DashboardAggregate.RealTimeStats getRealTimeStats(String dataScope) {
                log.debug("获取实时统计数据: dataScope={}", dataScope);

                // 模拟实时统计数据
                return DashboardAggregate.RealTimeStats.builder()
                                .currentOnline(1456L)
                                .peakOnline(1680L)
                                .totalDownloadsToday(234L)
                                .systemLoad(new BigDecimal("98.5"))
                                .updateTime(LocalDateTime.now())
                                .build();
        }

        @Override
        public DashboardAggregate.ChartData getFeatureUsageData(TimeRange timeRange, List<Long> productLineIds,
                        String dataScope) {
                log.debug("获取功能使用分析数据: timeRange={}, productLineIds={}, dataScope={}", timeRange, productLineIds,
                                dataScope);

                // 模拟功能使用数据
                List<String> labels = Arrays.asList("PDF阅读", "PDF编辑", "PDF转换", "PDF创建", "PDF签名", "PDF合并");
                List<BigDecimal> featureData = Arrays.asList(
                                new BigDecimal("85"), new BigDecimal("72"), new BigDecimal("68"),
                                new BigDecimal("45"), new BigDecimal("38"), new BigDecimal("25"));

                DashboardAggregate.ChartData.SeriesData series = DashboardAggregate.ChartData.SeriesData.builder()
                                .name("功能使用率")
                                .data(featureData)
                                .color("#fa541c")
                                .type("bar")
                                .build();

                return DashboardAggregate.ChartData.builder()
                                .chartType("bar")
                                .title("功能使用分析")
                                .categories(labels)
                                .series(Arrays.asList(series))
                                .options(new HashMap<>())
                                .build();
        }

        @Override
        public DashboardAggregate.ChartData getDownloadStatsData(TimeRange timeRange, List<Long> productLineIds,
                        String dataScope) {
                log.debug("获取下载统计数据: timeRange={}, productLineIds={}, dataScope={}", timeRange, productLineIds,
                                dataScope);

                // 模拟下载统计数据
                List<String> labels = Arrays.asList("Windows", "macOS", "Android", "iOS", "Linux");
                List<BigDecimal> downloadData = Arrays.asList(
                                new BigDecimal("45"), new BigDecimal("25"), new BigDecimal("15"),
                                new BigDecimal("10"), new BigDecimal("5"));

                DashboardAggregate.ChartData.SeriesData series = DashboardAggregate.ChartData.SeriesData.builder()
                                .name("下载统计")
                                .data(downloadData)
                                .color("#eb2f96")
                                .type("pie")
                                .build();

                return DashboardAggregate.ChartData.builder()
                                .chartType("pie")
                                .title("下载统计")
                                .categories(labels)
                                .series(Arrays.asList(series))
                                .options(new HashMap<>())
                                .build();
        }

        @Override
        public DashboardAggregate.ChartData getActivityHeatmapData(TimeRange timeRange, List<Long> productLineIds,
                        String dataScope) {
                log.debug("获取用户活跃度热力图数据: timeRange={}, productLineIds={}, dataScope={}", timeRange, productLineIds,
                                dataScope);

                // 模拟热力图数据 - 24小时 x 7天
                List<String> labels = Arrays.asList("周一", "周二", "周三", "周四", "周五", "周六", "周日");
                List<DashboardAggregate.ChartData.SeriesData> seriesList = new ArrayList<>();

                // 生成24小时的数据
                for (int hour = 0; hour < 24; hour++) {
                        List<BigDecimal> hourData = new ArrayList<>();
                        for (int day = 0; day < 7; day++) {
                                // 模拟活跃度数据 (0-100)
                                int activity = (int) (Math.random() * 100);
                                hourData.add(new BigDecimal(activity));
                        }

                        DashboardAggregate.ChartData.SeriesData series = DashboardAggregate.ChartData.SeriesData
                                        .builder()
                                        .name("Hour " + hour)
                                        .data(hourData)
                                        .color("#1890ff")
                                        .type("heatmap")
                                        .build();
                        seriesList.add(series);
                }

                return DashboardAggregate.ChartData.builder()
                                .chartType("heatmap")
                                .title("用户活跃度热力图")
                                .categories(labels)
                                .series(seriesList)
                                .options(new HashMap<>())
                                .build();
        }

        @Override
        public Map<String, BigDecimal> getProductLineComparisonData(List<Long> productLineIds, TimeRange timeRange,
                        String dataScope) {
                log.debug("获取产品线对比数据: productLineIds={}, timeRange={}, dataScope={}", productLineIds, timeRange,
                                dataScope);

                // 模拟产品线对比数据
                Map<String, BigDecimal> comparison = new HashMap<>();
                comparison.put("PDF Reader", new BigDecimal("125680"));
                comparison.put("PDF Editor", new BigDecimal("89420"));
                comparison.put("PDF Creator", new BigDecimal("67890"));
                comparison.put("PDF Converter", new BigDecimal("45670"));

                return comparison;
        }

        @Override
        public List<AnomalyData> getAnomalyData(TimeRange timeRange, String dataScope) {
                log.debug("获取异常检测数据: timeRange={}, dataScope={}", timeRange, dataScope);

                // 模拟异常数据
                List<DashboardRepository.AnomalyData> anomalies = new ArrayList<>();
                anomalies.add(new DashboardRepository.AnomalyData(
                                "用户登录失败率",
                                new BigDecimal("15.8"),
                                new BigDecimal("5.0"),
                                new BigDecimal("10.8"),
                                "高",
                                "登录失败率异常升高，可能存在安全风险"));

                anomalies.add(new DashboardRepository.AnomalyData(
                                "API响应时间",
                                new BigDecimal("2500"),
                                new BigDecimal("800"),
                                new BigDecimal("1700"),
                                "中",
                                "API响应时间超出正常范围"));

                return anomalies;
        }

        @Override
        public List<ProductLineSummary> getAccessibleProductLines(String dataScope, Long userId) {
                log.debug("获取用户权限可访问的产品线列表: dataScope={}, userId={}", dataScope, userId);

                // 模拟产品线数据
                List<DashboardRepository.ProductLineSummary> productLines = new ArrayList<>();
                productLines.add(new DashboardRepository.ProductLineSummary(
                                1L, "PDF Reader", "阅读器", 125680L, new BigDecimal("2856789.50"), new BigDecimal("8.5")));
                productLines.add(new DashboardRepository.ProductLineSummary(
                                2L, "PDF Editor", "编辑器", 89420L, new BigDecimal("1956789.50"), new BigDecimal("12.3")));
                productLines.add(new DashboardRepository.ProductLineSummary(
                                3L, "PDF Creator", "创建器", 67890L, new BigDecimal("1456789.50"), new BigDecimal("5.8")));

                return productLines;
        }
}
