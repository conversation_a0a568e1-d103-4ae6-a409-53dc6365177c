package com.foxit.crm.modules.system.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 密码值对象
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Getter
@EqualsAndHashCode
public class Password {

    private static final PasswordEncoder PASSWORD_ENCODER = new BCryptPasswordEncoder();

    private final String encodedValue;

    /**
     * 从原始密码创建
     */
    public static Password fromRaw(String rawPassword) {
        if (rawPassword == null || rawPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }

        if (rawPassword.length() < 6 || rawPassword.length() > 20) {
            throw new IllegalArgumentException("密码长度必须在6-20个字符之间");
        }

        String encoded = PASSWORD_ENCODER.encode(rawPassword);
        return new Password(encoded);
    }

    /**
     * 从已编码的密码创建
     */
    public static Password fromEncoded(String encodedPassword) {
        if (encodedPassword == null || encodedPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("编码后的密码不能为空");
        }
        return new Password(encodedPassword);
    }

    private Password(String encodedValue) {
        this.encodedValue = encodedValue;
    }

    /**
     * 验证原始密码是否匹配
     */
    public boolean matches(String rawPassword) {
        if (rawPassword == null) {
            return false;
        }
        return PASSWORD_ENCODER.matches(rawPassword, this.encodedValue);
    }

    @Override
    public String toString() {
        return "Password{encodedValue='[PROTECTED]'}";
    }

    public static void main(String[] args) {
        String pwd = PASSWORD_ENCODER.encode("admin123");
        System.out.println(pwd);
    }
}
