package com.foxit.crm.modules.system.application.service;

import java.util.List;

/**
 * 用户角色关联应用服务接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface UserRoleService {

    /**
     * 为用户分配角色
     */
    void assignRolesToUser(Long userId, List<Long> roleIds);

    /**
     * 为用户分配单个角色
     */
    void assignRoleToUser(Long userId, Long roleId);

    /**
     * 移除用户的角色
     */
    void removeRoleFromUser(Long userId, Long roleId);

    /**
     * 移除用户的所有角色
     */
    void removeAllRolesFromUser(Long userId);

    /**
     * 为角色分配用户
     */
    void assignUsersToRole(Long roleId, List<Long> userIds);

    /**
     * 移除角色的用户
     */
    void removeUserFromRole(Long roleId, Long userId);

    /**
     * 移除角色的所有用户
     */
    void removeAllUsersFromRole(Long roleId);

    /**
     * 获取用户的角色ID列表
     */
    List<Long> getUserRoleIds(Long userId);

    /**
     * 获取角色的用户ID列表
     */
    List<Long> getRoleUserIds(Long roleId);

    /**
     * 检查用户是否拥有指定角色
     */
    boolean hasRole(Long userId, Long roleId);

    /**
     * 检查用户是否拥有指定角色中的任意一个
     */
    boolean hasAnyRole(Long userId, List<Long> roleIds);

    /**
     * 检查用户是否拥有所有指定角色
     */
    boolean hasAllRoles(Long userId, List<Long> roleIds);

    /**
     * 统计角色下的用户数量
     */
    long countUsersByRole(Long roleId);

    /**
     * 统计用户的角色数量
     */
    long countRolesByUser(Long userId);

    /**
     * 批量删除用户的角色关联
     */
    void removeUserRolesByUserIds(List<Long> userIds);

    /**
     * 批量删除角色的用户关联
     */
    void removeUserRolesByRoleIds(List<Long> roleIds);
}
