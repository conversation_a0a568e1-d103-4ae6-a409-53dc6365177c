package com.foxit.crm.modules.dashboard.domain.entity;

import com.foxit.crm.modules.dashboard.domain.valueobject.MetricValue;
import com.foxit.crm.modules.dashboard.domain.valueobject.TimeRange;
import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Dashboard聚合根
 * 封装Dashboard的核心业务逻辑
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Getter
@Builder
public class DashboardAggregate {

    /**
     * 聚合根ID
     */
    private final String id;

    /**
     * Dashboard类型：OVERVIEW, PRODUCT_LINE, REAL_TIME
     */
    private final DashboardType type;

    /**
     * 时间范围
     */
    private final TimeRange timeRange;

    /**
     * 产品线ID列表（产品线Dashboard使用）
     */
    private final List<Long> productLineIds;

    /**
     * 核心指标
     */
    private final Map<String, MetricValue> coreMetrics;

    /**
     * 图表数据
     */
    private final Map<String, ChartData> chartData;

    /**
     * 实时统计数据
     */
    private final RealTimeStats realTimeStats;

    /**
     * 数据权限范围
     */
    private final String dataScope;

    /**
     * 最后更新时间
     */
    private final LocalDateTime lastUpdateTime;

    /**
     * Dashboard类型枚举
     */
    public enum DashboardType {
        OVERVIEW("overview", "总览仪表盘"),
        PRODUCT_LINE("product", "产品线仪表盘"),
        REAL_TIME("realtime", "实时监控");

        private final String code;
        private final String name;

        DashboardType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static DashboardType fromCode(String code) {
            for (DashboardType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return OVERVIEW; // 默认总览
        }
    }

    /**
     * 图表数据内部类
     */
    @Getter
    @Builder
    public static class ChartData {
        private final String chartType;
        private final String title;
        private final List<String> categories;
        private final List<SeriesData> series;
        private final Map<String, Object> options;

        @Getter
        @Builder
        public static class SeriesData {
            private final String name;
            private final List<BigDecimal> data;
            private final String color;
            private final String type;
        }
    }

    /**
     * 实时统计内部类
     */
    @Getter
    @Builder
    public static class RealTimeStats {
        private final Long currentOnline;
        private final Long peakOnline;
        private final Long totalDownloadsToday;
        private final BigDecimal systemLoad;
        private final LocalDateTime updateTime;
    }

    /**
     * 创建总览Dashboard
     */
    public static DashboardAggregate createOverview(TimeRange timeRange, String dataScope) {
        return DashboardAggregate.builder()
                .id(generateId("overview"))
                .type(DashboardType.OVERVIEW)
                .timeRange(timeRange)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建产品线Dashboard
     */
    public static DashboardAggregate createProductLine(List<Long> productLineIds, TimeRange timeRange, String dataScope) {
        return DashboardAggregate.builder()
                .id(generateId("product"))
                .type(DashboardType.PRODUCT_LINE)
                .timeRange(timeRange)
                .productLineIds(productLineIds)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建实时监控Dashboard
     */
    public static DashboardAggregate createRealTime(String dataScope) {
        return DashboardAggregate.builder()
                .id(generateId("realtime"))
                .type(DashboardType.REAL_TIME)
                .timeRange(TimeRange.today())
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 生成聚合根ID
     */
    private static String generateId(String prefix) {
        return prefix + "_" + System.currentTimeMillis();
    }

    /**
     * 验证Dashboard配置的有效性
     */
    public boolean isValid() {
        if (type == null || timeRange == null || !timeRange.isValid()) {
            return false;
        }

        // 产品线Dashboard必须有产品线ID
        if (type == DashboardType.PRODUCT_LINE && 
            (productLineIds == null || productLineIds.isEmpty())) {
            return false;
        }

        return true;
    }

    /**
     * 是否需要刷新数据
     */
    public boolean needsRefresh(int refreshIntervalMinutes) {
        if (lastUpdateTime == null) {
            return true;
        }
        
        LocalDateTime threshold = LocalDateTime.now().minusMinutes(refreshIntervalMinutes);
        return lastUpdateTime.isBefore(threshold);
    }

    /**
     * 获取指标值
     */
    public MetricValue getMetric(String metricName) {
        return coreMetrics != null ? coreMetrics.get(metricName) : null;
    }

    /**
     * 获取图表数据
     */
    public ChartData getChart(String chartName) {
        return chartData != null ? chartData.get(chartName) : null;
    }
}
