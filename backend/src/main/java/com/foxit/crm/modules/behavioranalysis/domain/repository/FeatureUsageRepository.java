package com.foxit.crm.modules.behavioranalysis.domain.repository;

import com.foxit.crm.modules.behavioranalysis.domain.entity.FeatureUsageAggregate;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 功能使用分析仓储接口
 * 定义功能使用分析数据访问的领域契约
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface FeatureUsageRepository {

    /**
     * 获取功能使用统计分析数据
     *
     * @param timeRange 时间范围
     * @param featureIds 功能ID列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 功能使用分析聚合根
     */
    Optional<FeatureUsageAggregate> getFeatureUsageStatistics(TimeRange timeRange, List<String> featureIds, 
                                                             List<Long> productLineIds, String dataScope);

    /**
     * 获取功能热度分析数据
     *
     * @param timeRange 时间范围
     * @param featureIds 功能ID列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 功能使用分析聚合根
     */
    Optional<FeatureUsageAggregate> getFeatureHeatAnalysis(TimeRange timeRange, List<String> featureIds, 
                                                          List<Long> productLineIds, String dataScope);

    /**
     * 获取功能路径分析数据
     *
     * @param timeRange 时间范围
     * @param startFeatureIds 起始功能ID列表
     * @param endFeatureIds 结束功能ID列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 功能使用分析聚合根
     */
    Optional<FeatureUsageAggregate> getFeaturePathAnalysis(TimeRange timeRange, List<String> startFeatureIds, 
                                                          List<String> endFeatureIds, List<Long> productLineIds, String dataScope);

    /**
     * 获取功能价值贡献分析数据
     *
     * @param timeRange 时间范围
     * @param featureIds 功能ID列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 功能使用分析聚合根
     */
    Optional<FeatureUsageAggregate> getFeatureValueContribution(TimeRange timeRange, List<String> featureIds, 
                                                               List<Long> productLineIds, String dataScope);

    /**
     * 获取功能满意度评估数据
     *
     * @param timeRange 时间范围
     * @param featureIds 功能ID列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 功能使用分析聚合根
     */
    Optional<FeatureUsageAggregate> getFeatureSatisfactionEvaluation(TimeRange timeRange, List<String> featureIds, 
                                                                    List<Long> productLineIds, String dataScope);

    /**
     * 获取实时功能使用统计
     *
     * @param featureIds 功能ID列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 实时统计数据
     */
    FeatureRealTimeStats getRealTimeFeatureStats(List<String> featureIds, List<Long> productLineIds, String dataScope);

    /**
     * 获取功能列表
     *
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 功能列表
     */
    List<FeatureInfo> getFeatureList(List<Long> productLineIds, String dataScope);

    /**
     * 保存功能使用分析结果
     *
     * @param aggregate 功能使用分析聚合根
     * @return 是否保存成功
     */
    boolean saveFeatureUsageAnalysis(FeatureUsageAggregate aggregate);

    /**
     * 删除功能使用分析数据
     *
     * @param aggregateId 聚合根ID
     * @return 是否删除成功
     */
    boolean deleteFeatureUsageAnalysis(String aggregateId);

    /**
     * 批量获取功能使用分析数据
     *
     * @param timeRanges 时间范围列表
     * @param featureIds 功能ID列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 功能使用分析聚合根列表
     */
    List<FeatureUsageAggregate> batchGetFeatureUsageData(List<TimeRange> timeRanges, List<String> featureIds, 
                                                        List<Long> productLineIds, String dataScope);

    /**
     * 删除过期数据
     *
     * @param beforeTime 过期时间点
     * @return 删除的数据条数
     */
    int deleteExpiredData(LocalDateTime beforeTime);

    /**
     * 功能实时统计数据
     */
    record FeatureRealTimeStats(
            Long totalUsage,
            Long uniqueUsers,
            Double avgUsagePerUser,
            List<FeatureStats> topFeatures,
            LocalDateTime lastUpdateTime
    ) {}

    /**
     * 功能统计数据
     */
    record FeatureStats(
            String featureId,
            String featureName,
            Long usageCount,
            Long userCount,
            Double usageRate,
            String trendDirection
    ) {}

    /**
     * 功能信息
     */
    record FeatureInfo(
            String featureId,
            String featureName,
            String featureCategory,
            String description,
            String featureType,
            Long productLineId,
            String productLineName,
            Boolean isActive,
            Integer priority,
            String version
    ) {}

    /**
     * 获取功能使用分析统计摘要
     *
     * @param timeRange 时间范围
     * @param dataScope 数据权限范围
     * @return 统计摘要数据
     */
    FeatureUsageAnalysisSummary getFeatureUsageAnalysisSummary(TimeRange timeRange, String dataScope);

    /**
     * 功能使用分析统计摘要
     */
    record FeatureUsageAnalysisSummary(
            Long totalFeatures,
            Long activeFeatures,
            Long totalUsage,
            Long uniqueUsers,
            Double avgUsagePerFeature,
            List<String> topFeatureCategories,
            LocalDateTime summaryTime
    ) {}

    /**
     * 获取功能使用趋势对比数据
     *
     * @param currentTimeRange 当前时间范围
     * @param previousTimeRange 对比时间范围
     * @param featureIds 功能ID列表
     * @param productLineIds 产品线ID列表
     * @param dataScope 数据权限范围
     * @return 趋势对比数据
     */
    FeatureUsageTrendComparison getFeatureUsageTrendComparison(TimeRange currentTimeRange, TimeRange previousTimeRange,
                                                              List<String> featureIds, List<Long> productLineIds, String dataScope);

    /**
     * 功能使用趋势对比数据
     */
    record FeatureUsageTrendComparison(
            List<FeatureTrendData> currentPeriodData,
            List<FeatureTrendData> previousPeriodData,
            List<FeatureTrendChange> trendChanges,
            Double overallGrowthRate,
            LocalDateTime comparisonTime
    ) {}

    /**
     * 功能趋势数据
     */
    record FeatureTrendData(
            String featureId,
            String featureName,
            Long usageCount,
            Long userCount,
            Double usageRate,
            String timeLabel
    ) {}

    /**
     * 功能趋势变化
     */
    record FeatureTrendChange(
            String featureId,
            String featureName,
            Double usageGrowthRate,
            Double userGrowthRate,
            String changeDirection,
            String changeLevel
    ) {}
}
