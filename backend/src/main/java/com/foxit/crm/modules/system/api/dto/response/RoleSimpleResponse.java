package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;

/**
 * 角色简单信息响应DTO
 *
 * 优化说明：
 * 1. 移除冗余的statusText字段，前端通过枚举映射获取文本描述
 * 2. 精简字段，只保留选择场景必需的信息
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
public class RoleSimpleResponse {

    /**
     * 角色ID
     */
    private Long id;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 角色状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 排序号
     */
    private Integer sortOrder;
}
