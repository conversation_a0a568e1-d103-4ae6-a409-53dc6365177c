package com.foxit.crm.modules.system.infrastructure.persistence.converter;

import com.foxit.crm.modules.system.api.dto.response.UserInfoResponse;
import com.foxit.crm.modules.system.domain.model.aggregate.User;
import com.foxit.crm.modules.system.domain.model.valueobject.*;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.UserPO;
import org.springframework.stereotype.Component;

/**
 * 用户转换器
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Component
public class UserConverter {

    /**
     * 领域对象转持久化对象
     */
    public UserPO toPO(User user) {
        if (user == null) {
            return null;
        }

        UserPO po = new UserPO();
        if (user.getUserId() != null) {
            po.setId(user.getUserId().getValue());
        }
        if (user.getUsername() != null) {
            po.setUsername(user.getUsername().getValue());
        }
        if (user.getPassword() != null) {
            po.setPassword(user.getPassword().getEncodedValue());
        }
        po.setRealName(user.getRealName());
        if (user.getEmail() != null && !user.getEmail().isEmpty()) {
            po.setEmail(user.getEmail().getValue());
        }
        po.setPhone(user.getPhone());
        po.setAvatar(user.getAvatar());
        po.setStatus(user.getStatus());
        po.setUserType(user.getUserType());
        po.setDeptId(user.getDeptId());
        po.setLastLoginTime(user.getLastLoginTime());
        po.setLastLoginIp(user.getLastLoginIp());
        po.setRemark(user.getRemark());
        po.setCreateTime(user.getCreateTime());
        po.setUpdateTime(user.getUpdateTime());
        po.setCreateBy(user.getCreateBy());
        po.setUpdateBy(user.getUpdateBy());
        po.setDeleted(user.getDeleted());
        po.setVersion(user.getVersion());

        return po;
    }

    /**
     * 持久化对象转领域对象
     */
    public User toDomain(UserPO po) {
        if (po == null) {
            return null;
        }

        User user = new User();
        if (po.getId() != null) {
            user.setUserId(new UserId(po.getId()));
        }
        if (po.getUsername() != null) {
            user.setUsername(new Username(po.getUsername()));
        }
        if (po.getPassword() != null) {
            user.setPassword(Password.fromEncoded(po.getPassword()));
        }
        user.setRealName(po.getRealName());
        if (po.getEmail() != null) {
            user.setEmail(new Email(po.getEmail()));
        }
        user.setPhone(po.getPhone());
        user.setAvatar(po.getAvatar());
        user.setStatus(po.getStatus());
        user.setUserType(po.getUserType());
        user.setDeptId(po.getDeptId());
        user.setLastLoginTime(po.getLastLoginTime());
        user.setLastLoginIp(po.getLastLoginIp());
        user.setRemark(po.getRemark());
        user.setCreateTime(po.getCreateTime());
        user.setUpdateTime(po.getUpdateTime());
        user.setCreateBy(po.getCreateBy());
        user.setUpdateBy(po.getUpdateBy());
        user.setDeleted(po.getDeleted());
        user.setVersion(po.getVersion());

        return user;
    }

    /**
     * 领域对象转响应DTO
     */
    public UserInfoResponse toUserInfoResponse(User user) {
        if (user == null) {
            return null;
        }

        UserInfoResponse response = new UserInfoResponse();
        if (user.getUserId() != null) {
            response.setId(user.getUserId().getValue());
        }
        if (user.getUsername() != null) {
            response.setUsername(user.getUsername().getValue());
        }
        response.setRealName(user.getRealName());
        if (user.getEmail() != null && !user.getEmail().isEmpty()) {
            response.setEmail(user.getEmail().getValue());
        }
        response.setPhone(user.getPhone());
        response.setAvatar(user.getAvatar());
        response.setStatus(user.getStatus());
        response.setUserType(user.getUserType());
        response.setDeptId(user.getDeptId());
        response.setLastLoginTime(user.getLastLoginTime());
        response.setLastLoginIp(user.getLastLoginIp());
        response.setCreateTime(user.getCreateTime());

        return response;
    }
}
