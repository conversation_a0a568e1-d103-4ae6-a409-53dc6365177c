package com.foxit.crm.modules.dashboard.application.service;

import com.foxit.crm.modules.dashboard.application.dto.request.DashboardConfigRequest;
import com.foxit.crm.modules.dashboard.application.dto.request.DashboardQueryRequest;
import com.foxit.crm.modules.dashboard.application.dto.response.*;

/**
 * Dashboard应用服务接口
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
public interface DashboardService {

    /**
     * 获取总览仪表盘数据
     */
    OverviewDashboardResponse getOverviewDashboard(DashboardQueryRequest request);

    /**
     * 获取产品线仪表盘数据
     */
    ProductLineDashboardResponse getProductLineDashboard(DashboardQueryRequest request);

    /**
     * 获取实时统计数据
     */
    RealTimeStatsResponse getRealTimeStats(String dataScope);

    /**
     * 获取核心指标数据
     */
    CoreMetricsResponse getCoreMetrics(DashboardQueryRequest request);

    /**
     * 获取图表数据
     */
    ChartDataResponse getChartData(String chartType, DashboardQueryRequest request);

    /**
     * 刷新Dashboard缓存
     */
    void refreshDashboardCache(String dashboardType, String dataScope);

    /**
     * 获取Dashboard配置信息
     */
    DashboardConfigResponse getDashboardConfig(Long userId);

    /**
     * 更新Dashboard配置
     */
    void updateDashboardConfig(Long userId, DashboardConfigRequest request);
}
