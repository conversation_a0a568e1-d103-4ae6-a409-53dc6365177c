package com.foxit.crm.modules.behavioranalysis.application.dto;

import com.foxit.crm.modules.behavioranalysis.domain.entity.FeatureUsageAggregate;
import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 功能使用分析响应DTO
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Builder
@Schema(description = "功能使用分析响应")
public class FeatureUsageResponse {

    @Schema(description = "分析ID")
    private String analysisId;

    @Schema(description = "分析类型")
    private String analysisType;

    @Schema(description = "时间范围")
    private TimeRange timeRange;

    @Schema(description = "核心指标")
    private Map<String, MetricValue> coreMetrics;

    @Schema(description = "功能使用统计数据")
    private Map<String, FeatureUsageAggregate.FeatureUsageStats> usageStats;

    @Schema(description = "功能热度数据")
    private Map<String, FeatureUsageAggregate.FeatureHeatData> heatData;

    @Schema(description = "功能路径数据")
    private Map<String, FeatureUsageAggregate.FeaturePathData> pathData;

    @Schema(description = "功能价值数据")
    private Map<String, FeatureUsageAggregate.FeatureValueData> valueData;

    @Schema(description = "功能满意度数据")
    private Map<String, FeatureUsageAggregate.FeatureSatisfactionData> satisfactionData;

    @Schema(description = "最后更新时间")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "数据状态")
    private String dataStatus;

    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 功能使用统计数据DTO
     */
    @Data
    @Builder
    @Schema(description = "功能使用统计数据")
    public static class FeatureUsageStatsData {

        @Schema(description = "功能ID")
        private String featureId;

        @Schema(description = "功能名称")
        private String featureName;

        @Schema(description = "功能分类")
        private String featureCategory;

        @Schema(description = "总使用次数")
        private Long totalUsageCount;

        @Schema(description = "独立用户数")
        private Long uniqueUsers;

        @Schema(description = "平均使用次数/用户")
        private Double avgUsagePerUser;

        @Schema(description = "使用率")
        private Double usageRate;

        @Schema(description = "时间分类")
        private List<String> categories;

        @Schema(description = "使用次数")
        private List<Number> usageCounts;

        @Schema(description = "用户数")
        private List<Number> userCounts;

        @Schema(description = "趋势方向")
        private String trendDirection;

        @Schema(description = "增长率")
        private Double growthRate;
    }

    /**
     * 功能热度数据DTO
     */
    @Data
    @Builder
    @Schema(description = "功能热度数据")
    public static class FeatureHeatData {

        @Schema(description = "功能ID")
        private String featureId;

        @Schema(description = "功能名称")
        private String featureName;

        @Schema(description = "热度分数")
        private Double heatScore;

        @Schema(description = "热度排名")
        private Integer heatRank;

        @Schema(description = "受欢迎指数")
        private Double popularityIndex;

        @Schema(description = "参与度分数")
        private Double engagementScore;

        @Schema(description = "留存率")
        private Double retentionRate;

        @Schema(description = "热力图点数据")
        private List<HeatMapPointData> heatMapPoints;

        @Schema(description = "热度等级")
        private String heatLevel;

        @Schema(description = "变化率")
        private Double changeRate;
    }

    /**
     * 热力图点数据DTO
     */
    @Data
    @Builder
    @Schema(description = "热力图点数据")
    public static class HeatMapPointData {

        @Schema(description = "维度")
        private String dimension;

        @Schema(description = "分类")
        private String category;

        @Schema(description = "数值")
        private Double value;

        @Schema(description = "颜色")
        private String color;

        @Schema(description = "强度")
        private Integer intensity;
    }

    /**
     * 功能路径数据DTO
     */
    @Data
    @Builder
    @Schema(description = "功能路径数据")
    public static class FeaturePathData {

        @Schema(description = "路径名称")
        private String pathName;

        @Schema(description = "路径节点")
        private List<FeaturePathNodeData> nodes;

        @Schema(description = "路径链接")
        private List<FeaturePathLinkData> links;

        @Schema(description = "总用户数")
        private Long totalUsers;

        @Schema(description = "平均路径长度")
        private Double avgPathLength;

        @Schema(description = "最常见路径")
        private String mostCommonPath;

        @Schema(description = "热门路径")
        private List<String> topPaths;
    }

    /**
     * 功能路径节点数据DTO
     */
    @Data
    @Builder
    @Schema(description = "功能路径节点数据")
    public static class FeaturePathNodeData {

        @Schema(description = "功能ID")
        private String featureId;

        @Schema(description = "功能名称")
        private String featureName;

        @Schema(description = "用户数")
        private Long userCount;

        @Schema(description = "百分比")
        private Double percentage;

        @Schema(description = "层级")
        private Integer level;

        @Schema(description = "平均停留时间")
        private Double avgTimeSpent;

        @Schema(description = "节点类型")
        private String nodeType;
    }

    /**
     * 功能路径链接数据DTO
     */
    @Data
    @Builder
    @Schema(description = "功能路径链接数据")
    public static class FeaturePathLinkData {

        @Schema(description = "源功能ID")
        private String sourceFeatureId;

        @Schema(description = "目标功能ID")
        private String targetFeatureId;

        @Schema(description = "用户数")
        private Long userCount;

        @Schema(description = "百分比")
        private Double percentage;

        @Schema(description = "平均转换时间")
        private Double avgTransitionTime;

        @Schema(description = "转化率")
        private Double conversionRate;

        @Schema(description = "链接强度")
        private String linkStrength;
    }

    /**
     * 功能价值数据DTO
     */
    @Data
    @Builder
    @Schema(description = "功能价值数据")
    public static class FeatureValueData {

        @Schema(description = "功能ID")
        private String featureId;

        @Schema(description = "功能名称")
        private String featureName;

        @Schema(description = "价值分数")
        private Double valueScore;

        @Schema(description = "价值排名")
        private Integer valueRank;

        @Schema(description = "业务影响")
        private Double businessImpact;

        @Schema(description = "用户满意度影响")
        private Double userSatisfactionImpact;

        @Schema(description = "留存影响")
        private Double retentionImpact;

        @Schema(description = "收入贡献")
        private Double revenueContribution;

        @Schema(description = "价值指标")
        private List<ValueMetricData> valueMetrics;

        @Schema(description = "价值等级")
        private String valueLevel;
    }

    /**
     * 价值指标数据DTO
     */
    @Data
    @Builder
    @Schema(description = "价值指标数据")
    public static class ValueMetricData {

        @Schema(description = "指标名称")
        private String metricName;

        @Schema(description = "指标值")
        private Double metricValue;

        @Schema(description = "指标单位")
        private String metricUnit;

        @Schema(description = "权重")
        private Double weight;

        @Schema(description = "描述")
        private String description;
    }

    /**
     * 功能满意度数据DTO
     */
    @Data
    @Builder
    @Schema(description = "功能满意度数据")
    public static class FeatureSatisfactionData {

        @Schema(description = "功能ID")
        private String featureId;

        @Schema(description = "功能名称")
        private String featureName;

        @Schema(description = "满意度分数")
        private Double satisfactionScore;

        @Schema(description = "满意度排名")
        private Integer satisfactionRank;

        @Schema(description = "易用性分数")
        private Double usabilityScore;

        @Schema(description = "实用性分数")
        private Double utilityScore;

        @Schema(description = "可靠性分数")
        private Double reliabilityScore;

        @Schema(description = "满意度因子")
        private List<SatisfactionFactorData> factors;

        @Schema(description = "正面评价")
        private List<String> positiveComments;

        @Schema(description = "负面评价")
        private List<String> negativeComments;

        @Schema(description = "满意度等级")
        private String satisfactionLevel;
    }

    /**
     * 满意度因子数据DTO
     */
    @Data
    @Builder
    @Schema(description = "满意度因子数据")
    public static class SatisfactionFactorData {

        @Schema(description = "因子名称")
        private String factorName;

        @Schema(description = "因子分数")
        private Double factorScore;

        @Schema(description = "权重")
        private Double weight;

        @Schema(description = "影响")
        private String impact;

        @Schema(description = "描述")
        private String description;
    }

    /**
     * 创建空响应
     */
    public static FeatureUsageResponse empty() {
        return FeatureUsageResponse.builder()
                .dataStatus("EMPTY")
                .errorMessage("未找到数据")
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建错误响应
     */
    public static FeatureUsageResponse error(String errorMessage) {
        return FeatureUsageResponse.builder()
                .dataStatus("ERROR")
                .errorMessage(errorMessage)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功响应
     */
    public static FeatureUsageResponse success(String analysisId, String analysisType) {
        return FeatureUsageResponse.builder()
                .analysisId(analysisId)
                .analysisType(analysisType)
                .dataStatus("SUCCESS")
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 检查是否有数据
     */
    public boolean hasData() {
        return "SUCCESS".equals(dataStatus) && 
               (coreMetrics != null && !coreMetrics.isEmpty() ||
                usageStats != null && !usageStats.isEmpty() ||
                heatData != null && !heatData.isEmpty() ||
                pathData != null && !pathData.isEmpty() ||
                valueData != null && !valueData.isEmpty() ||
                satisfactionData != null && !satisfactionData.isEmpty());
    }

    /**
     * 检查是否有错误
     */
    public boolean hasError() {
        return "ERROR".equals(dataStatus) || errorMessage != null;
    }

    /**
     * 获取数据摘要
     */
    public String getDataSummary() {
        if (hasError()) {
            return "错误: " + errorMessage;
        }
        
        if (!hasData()) {
            return "无数据";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("分析类型: ").append(analysisType);
        
        if (coreMetrics != null && !coreMetrics.isEmpty()) {
            summary.append(", 核心指标数: ").append(coreMetrics.size());
        }
        
        if (usageStats != null && !usageStats.isEmpty()) {
            summary.append(", 使用统计数: ").append(usageStats.size());
        }
        
        if (heatData != null && !heatData.isEmpty()) {
            summary.append(", 热度数据数: ").append(heatData.size());
        }
        
        return summary.toString();
    }
}
