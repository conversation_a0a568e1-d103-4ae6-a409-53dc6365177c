package com.foxit.crm.modules.dashboard.application.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 图表数据响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChartDataResponse {

    /**
     * 图表类型：line-折线图, bar-柱状图, pie-饼图, area-面积图
     */
    private String chartType;

    /**
     * 图表标题
     */
    private String title;

    /**
     * 图表描述
     */
    private String description;

    /**
     * X轴数据（时间或分类）
     */
    private List<String> categories;

    /**
     * 数据系列
     */
    private List<SeriesData> series;

    /**
     * 图表配置选项
     */
    private ChartOptions options;

    /**
     * 数据系列内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class SeriesData {
        /**
         * 系列名称
         */
        private String name;

        /**
         * 数据值
         */
        private List<BigDecimal> data;

        /**
         * 系列颜色
         */
        private String color;

        /**
         * 系列类型（用于混合图表）
         */
        private String type;

        /**
         * 是否显示
         */
        private Boolean visible;

        /**
         * 额外属性
         */
        private Map<String, Object> extra;
    }

    /**
     * 图表配置选项内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ChartOptions {
        /**
         * 是否显示图例
         */
        private Boolean showLegend;

        /**
         * 图例位置：top, bottom, left, right
         */
        private String legendPosition;

        /**
         * 是否显示数据标签
         */
        private Boolean showDataLabels;

        /**
         * 是否平滑曲线
         */
        private Boolean smooth;

        /**
         * 是否堆叠
         */
        private Boolean stacked;

        /**
         * Y轴单位
         */
        private String yAxisUnit;

        /**
         * 是否显示缩放工具
         */
        private Boolean showDataZoom;

        /**
         * 高度
         */
        private Integer height;

        /**
         * 额外配置
         */
        private Map<String, Object> extra;
    }
}
