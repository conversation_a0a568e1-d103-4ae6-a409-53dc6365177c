package com.foxit.crm.modules.dashboard.application.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 总览仪表盘响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OverviewDashboardResponse {

    /**
     * 核心指标数据
     */
    private CoreMetricsResponse coreMetrics;

    /**
     * 用户增长趋势图表
     */
    private ChartDataResponse userGrowthChart;

    /**
     * 收入统计图表
     */
    private ChartDataResponse revenueChart;

    /**
     * 产品线分布图表
     */
    private ChartDataResponse productLineChart;

    /**
     * 用户类型分布图表
     */
    private ChartDataResponse userTypeChart;

    /**
     * 实时在线用户图表
     */
    private ChartDataResponse realTimeChart;

    /**
     * 实时统计数据
     */
    private RealTimeStatsResponse realTimeStats;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 数据权限范围
     */
    private String dataScope;

    /**
     * 可访问的产品线列表
     */
    private List<ProductLineSummary> accessibleProductLines;

    /**
     * 产品线摘要内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ProductLineSummary {
        /**
         * 产品线ID
         */
        private Long id;

        /**
         * 产品线名称
         */
        private String name;

        /**
         * 产品线类型
         */
        private String type;

        /**
         * 用户数
         */
        private Long userCount;

        /**
         * 收入
         */
        private java.math.BigDecimal revenue;

        /**
         * 增长率
         */
        private java.math.BigDecimal growthRate;
    }
}
