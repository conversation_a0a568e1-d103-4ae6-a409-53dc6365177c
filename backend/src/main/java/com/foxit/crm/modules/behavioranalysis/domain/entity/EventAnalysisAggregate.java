package com.foxit.crm.modules.behavioranalysis.domain.entity;

import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 事件分析聚合根
 * 封装事件分析的核心业务逻辑
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Getter
@Builder
public class EventAnalysisAggregate {

    /**
     * 聚合根ID
     */
    private final String id;

    /**
     * 分析类型：EVENT_TREND, EVENT_FUNNEL, EVENT_PATH, EVENT_COMPARISON
     */
    private final EventAnalysisType analysisType;

    /**
     * 时间范围
     */
    private final TimeRange timeRange;

    /**
     * 产品线ID列表
     */
    private final List<Long> productLineIds;

    /**
     * 事件ID列表
     */
    private final List<String> eventIds;

    /**
     * 核心指标
     */
    private final Map<String, MetricValue> coreMetrics;

    /**
     * 趋势数据
     */
    private final Map<String, TrendData> trendData;

    /**
     * 漏斗数据
     */
    private final Map<String, FunnelData> funnelData;

    /**
     * 路径数据
     */
    private final Map<String, PathData> pathData;

    /**
     * 事件属性数据
     */
    private final Map<String, EventPropertyData> eventPropertyData;

    /**
     * 数据权限范围
     */
    private final String dataScope;

    /**
     * 最后更新时间
     */
    private final LocalDateTime lastUpdateTime;

    /**
     * 事件分析类型枚举
     */
    public enum EventAnalysisType {
        EVENT_TREND("事件趋势分析"),
        EVENT_FUNNEL("事件漏斗分析"),
        EVENT_PATH("事件路径分析"),
        EVENT_COMPARISON("事件对比分析"),
        EVENT_PROPERTY("事件属性分析");

        private final String description;

        EventAnalysisType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 趋势数据
     */
    @Getter
    @Builder
    public static class TrendData {
        private final String eventId;
        private final String eventName;
        private final List<String> categories;
        private final List<Number> triggerCounts;
        private final List<Number> userCounts;
        private final String unit;
        private final String chartType;
    }

    /**
     * 漏斗数据
     */
    @Getter
    @Builder
    public static class FunnelData {
        private final String funnelName;
        private final List<FunnelStep> steps;
        private final Double totalConversionRate;
        private final Long totalUsers;
        private final String timeRange;
    }

    /**
     * 漏斗步骤
     */
    @Getter
    @Builder
    public static class FunnelStep {
        private final String eventId;
        private final String eventName;
        private final Long userCount;
        private final Double conversionRate;
        private final Double dropOffRate;
        private final Integer stepOrder;
    }

    /**
     * 路径数据
     */
    @Getter
    @Builder
    public static class PathData {
        private final String pathName;
        private final List<PathNode> nodes;
        private final List<PathLink> links;
        private final Long totalUsers;
        private final String pathType;
    }

    /**
     * 路径节点
     */
    @Getter
    @Builder
    public static class PathNode {
        private final String eventId;
        private final String eventName;
        private final Long userCount;
        private final Double percentage;
        private final Integer level;
    }

    /**
     * 路径链接
     */
    @Getter
    @Builder
    public static class PathLink {
        private final String sourceEventId;
        private final String targetEventId;
        private final Long userCount;
        private final Double percentage;
        private final Double avgTimeSpent;
    }

    /**
     * 事件属性数据
     */
    @Getter
    @Builder
    public static class EventPropertyData {
        private final String eventId;
        private final String propertyName;
        private final Map<String, Long> propertyValues;
        private final String dataType;
        private final Long totalCount;
    }

    /**
     * 生成聚合根ID
     */
    private static String generateId(String prefix, TimeRange timeRange) {
        return String.format("%s_%s_%s_%s", 
            prefix, 
            timeRange.getStartDate(), 
            timeRange.getEndDate(),
            UUID.randomUUID().toString().substring(0, 8));
    }

    /**
     * 获取核心指标
     */
    public MetricValue getCoreMetric(String key) {
        return coreMetrics != null ? coreMetrics.get(key) : null;
    }

    /**
     * 创建事件趋势分析
     */
    public static EventAnalysisAggregate createEventTrendAnalysis(TimeRange timeRange, List<String> eventIds, String dataScope) {
        return EventAnalysisAggregate.builder()
                .id(generateId("event_trend", timeRange))
                .analysisType(EventAnalysisType.EVENT_TREND)
                .timeRange(timeRange)
                .eventIds(eventIds)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建事件漏斗分析
     */
    public static EventAnalysisAggregate createEventFunnelAnalysis(TimeRange timeRange, List<String> eventIds, String dataScope) {
        return EventAnalysisAggregate.builder()
                .id(generateId("event_funnel", timeRange))
                .analysisType(EventAnalysisType.EVENT_FUNNEL)
                .timeRange(timeRange)
                .eventIds(eventIds)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建事件路径分析
     */
    public static EventAnalysisAggregate createEventPathAnalysis(TimeRange timeRange, List<String> eventIds, String dataScope) {
        return EventAnalysisAggregate.builder()
                .id(generateId("event_path", timeRange))
                .analysisType(EventAnalysisType.EVENT_PATH)
                .timeRange(timeRange)
                .eventIds(eventIds)
                .dataScope(dataScope)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 验证聚合根是否有效
     */
    public boolean isValid() {
        return timeRange != null && 
               timeRange.isValid() && 
               analysisType != null &&
               dataScope != null && 
               !dataScope.trim().isEmpty() &&
               eventIds != null &&
               !eventIds.isEmpty();
    }

    /**
     * 验证数据完整性
     */
    public boolean isDataComplete() {
        return id != null && 
               analysisType != null && 
               timeRange != null && 
               dataScope != null && 
               lastUpdateTime != null &&
               coreMetrics != null && 
               !coreMetrics.isEmpty();
    }

    /**
     * 获取数据摘要
     */
    public String getDataSummary() {
        if (!isDataComplete()) {
            return "数据不完整";
        }
        
        MetricValue totalEvents = getCoreMetric("totalEvents");
        MetricValue uniqueUsers = getCoreMetric("uniqueUsers");
        
        return String.format("分析类型: %s, 时间范围: %s - %s, 事件总数: %s, 独立用户: %s",
                analysisType.getDescription(),
                timeRange.getStartDate(),
                timeRange.getEndDate(),
                totalEvents != null ? totalEvents.getValue() : "N/A",
                uniqueUsers != null ? uniqueUsers.getValue() : "N/A");
    }
}
