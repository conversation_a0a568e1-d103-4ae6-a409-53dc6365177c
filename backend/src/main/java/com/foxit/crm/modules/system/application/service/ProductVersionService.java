package com.foxit.crm.modules.system.application.service;

import com.foxit.crm.modules.system.api.dto.request.ProductVersionCreateRequest;
import com.foxit.crm.modules.system.api.dto.request.ProductVersionUpdateRequest;
import com.foxit.crm.modules.system.api.dto.response.ProductVersionDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.ProductVersionListResponse;
import com.foxit.crm.modules.system.api.dto.response.ProductVersionSimpleResponse;
import com.foxit.crm.modules.system.api.dto.response.VersionReleaseHistoryResponse;

import java.util.List;

/**
 * 产品版本应用服务接口
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface ProductVersionService {

    /**
     * 创建产品版本
     */
    Long createProductVersion(ProductVersionCreateRequest request);

    /**
     * 更新产品版本
     */
    void updateProductVersion(Long id, ProductVersionUpdateRequest request);

    /**
     * 删除产品版本
     */
    void deleteProductVersion(Long id);

    /**
     * 批量删除产品版本
     */
    void deleteProductVersionsBatch(List<Long> ids);

    /**
     * 根据ID获取产品版本详情
     */
    ProductVersionDetailResponse getProductVersionById(Long id);

    /**
     * 分页查询产品版本列表
     */
    ProductVersionListResponse getProductVersionList(int page, int size, Long productLineId, 
                                                   String keyword, String status, String versionType);

    /**
     * 根据产品线ID获取版本列表
     */
    List<ProductVersionSimpleResponse> getVersionsByProductLineId(Long productLineId);

    /**
     * 根据产品线ID获取当前版本
     */
    ProductVersionDetailResponse getCurrentVersionByProductLineId(Long productLineId);

    /**
     * 发布版本
     */
    void releaseVersion(Long id, String reason);

    /**
     * 设置当前版本
     */
    void setCurrentVersion(Long id);

    /**
     * 废弃版本
     */
    void deprecateVersion(Long id, String reason);

    /**
     * 更新版本状态
     */
    void updateVersionStatus(Long id, String status, String reason);

    /**
     * 下载版本
     */
    void downloadVersion(Long id, String platform, String region);

    /**
     * 获取版本发布历史
     */
    List<VersionReleaseHistoryResponse> getVersionReleaseHistory(Long versionId);

    /**
     * 获取版本统计信息
     */
    Object getVersionStatsByProductLineId(Long productLineId);

    /**
     * 获取热门版本排行
     */
    List<Object> getTopDownloadVersions(Integer days, Integer limit);

    /**
     * 检查版本号是否可用
     */
    boolean isVersionNumberAvailable(Long productLineId, String versionNumber);

    /**
     * 检查版本号是否可用（排除指定ID）
     */
    boolean isVersionNumberAvailable(Long productLineId, String versionNumber, Long excludeId);

    /**
     * 生成下一个版本号建议
     */
    String suggestNextVersionNumber(Long productLineId, String versionType);

    /**
     * 批量更新版本状态
     */
    void updateVersionStatusBatch(List<Long> ids, String status, String reason);

    /**
     * 获取版本下载统计
     */
    Object getVersionDownloadStats(Long versionId, Integer days);

    /**
     * 导出版本列表
     */
    byte[] exportVersionList(Long productLineId, String format);

    /**
     * 同步版本信息
     */
    void syncVersionInfo(Long productLineId);
}
