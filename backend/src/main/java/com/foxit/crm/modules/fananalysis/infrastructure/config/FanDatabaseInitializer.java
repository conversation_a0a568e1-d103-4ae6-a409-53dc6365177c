package com.foxit.crm.modules.fananalysis.infrastructure.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

/**
 * 粉丝分析数据库初始化器
 * 在应用启动时创建粉丝分析相关的表和数据
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Component
@Order(3) // 在活动分析初始化器之后执行
@RequiredArgsConstructor
public class FanDatabaseInitializer implements CommandLineRunner {

    private final JdbcTemplate mysqlJdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始初始化粉丝分析数据库表...");
        
        try {
            // 创建粉丝基础信息表
            createFansTable();
            
            // 创建粉丝互动记录表
            createFanInteractionsTable();
            
            // 创建粉丝统计表
            createFanStatisticsTable();
            
            // 插入模拟数据
            insertMockData();
            
            log.info("粉丝分析数据库表初始化完成");
            
        } catch (Exception e) {
            log.error("粉丝分析数据库表初始化失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建粉丝基础信息表
     */
    private void createFansTable() {
        String sql = """
                CREATE TABLE IF NOT EXISTS fans (
                    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '粉丝ID',
                    fan_id VARCHAR(100) NOT NULL UNIQUE COMMENT '粉丝唯一标识',
                    nickname VARCHAR(255) NOT NULL COMMENT '昵称',
                    avatar_url VARCHAR(500) COMMENT '头像URL',
                    platform VARCHAR(50) NOT NULL COMMENT '平台',
                    platform_user_id VARCHAR(255) COMMENT '平台用户ID',
                    gender TINYINT COMMENT '性别：0-未知, 1-男, 2-女',
                    age_range VARCHAR(20) COMMENT '年龄段',
                    region VARCHAR(100) COMMENT '地区',
                    city VARCHAR(100) COMMENT '城市',
                    follow_time DATETIME NOT NULL COMMENT '关注时间',
                    unfollow_time DATETIME COMMENT '取消关注时间',
                    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态',
                    source VARCHAR(50) COMMENT '来源',
                    fan_type VARCHAR(50) COMMENT '粉丝类型',
                    fan_value VARCHAR(20) COMMENT '粉丝价值',
                    activity_level INT DEFAULT 0 COMMENT '活跃度评分(0-100)',
                    interaction_count BIGINT DEFAULT 0 COMMENT '互动次数',
                    last_interaction_time DATETIME COMMENT '最后互动时间',
                    tags JSON COMMENT '标签列表',
                    remark TEXT COMMENT '备注',
                    created_by BIGINT COMMENT '创建人ID',
                    updated_by BIGINT COMMENT '更新人ID',
                    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    deleted TINYINT DEFAULT 0 COMMENT '是否删除：0-否，1-是',
                    version INT DEFAULT 0 COMMENT '版本号',
                    
                    INDEX idx_fan_id (fan_id),
                    INDEX idx_platform (platform),
                    INDEX idx_status (status),
                    INDEX idx_follow_time (follow_time),
                    INDEX idx_source (source),
                    INDEX idx_fan_type (fan_type),
                    INDEX idx_fan_value (fan_value),
                    INDEX idx_activity_level (activity_level),
                    INDEX idx_region (region),
                    INDEX idx_create_time (create_time)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='粉丝基础信息表'
                """;
        
        mysqlJdbcTemplate.execute(sql);
        log.info("粉丝基础信息表创建成功");
    }

    /**
     * 创建粉丝互动记录表
     */
    private void createFanInteractionsTable() {
        String sql = """
                CREATE TABLE IF NOT EXISTS fan_interactions (
                    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '互动ID',
                    fan_id BIGINT NOT NULL COMMENT '粉丝ID',
                    interaction_type VARCHAR(50) NOT NULL COMMENT '互动类型',
                    interaction_date DATE NOT NULL COMMENT '互动日期',
                    interaction_time DATETIME NOT NULL COMMENT '互动时间',
                    content_id VARCHAR(255) COMMENT '内容ID',
                    content_type VARCHAR(50) COMMENT '内容类型',
                    interaction_value INT DEFAULT 1 COMMENT '互动价值权重',
                    platform VARCHAR(50) NOT NULL COMMENT '平台',
                    device_type VARCHAR(50) COMMENT '设备类型',
                    ip_address VARCHAR(45) COMMENT 'IP地址',
                    user_agent TEXT COMMENT '用户代理',
                    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    
                    INDEX idx_fan_id (fan_id),
                    INDEX idx_interaction_type (interaction_type),
                    INDEX idx_interaction_date (interaction_date),
                    INDEX idx_interaction_time (interaction_time),
                    INDEX idx_platform (platform),
                    INDEX idx_content_type (content_type)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='粉丝互动记录表'
                """;
        
        mysqlJdbcTemplate.execute(sql);
        log.info("粉丝互动记录表创建成功");
    }

    /**
     * 创建粉丝统计表
     */
    private void createFanStatisticsTable() {
        String sql = """
                CREATE TABLE IF NOT EXISTS fan_statistics (
                    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计ID',
                    stat_date DATE NOT NULL COMMENT '统计日期',
                    platform VARCHAR(50) NOT NULL COMMENT '平台',
                    total_fans BIGINT DEFAULT 0 COMMENT '总粉丝数',
                    new_fans BIGINT DEFAULT 0 COMMENT '新增粉丝数',
                    unfollowed_fans BIGINT DEFAULT 0 COMMENT '取关粉丝数',
                    active_fans BIGINT DEFAULT 0 COMMENT '活跃粉丝数',
                    high_value_fans BIGINT DEFAULT 0 COMMENT '高价值粉丝数',
                    medium_value_fans BIGINT DEFAULT 0 COMMENT '中等价值粉丝数',
                    low_value_fans BIGINT DEFAULT 0 COMMENT '低价值粉丝数',
                    total_interactions BIGINT DEFAULT 0 COMMENT '总互动数',
                    avg_activity_level DECIMAL(5,2) DEFAULT 0 COMMENT '平均活跃度',
                    retention_rate DECIMAL(5,2) DEFAULT 0 COMMENT '留存率(%)',
                    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    
                    UNIQUE KEY uk_date_platform (stat_date, platform),
                    INDEX idx_stat_date (stat_date),
                    INDEX idx_platform (platform)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='粉丝统计表'
                """;
        
        mysqlJdbcTemplate.execute(sql);
        log.info("粉丝统计表创建成功");
    }

    /**
     * 插入模拟数据
     */
    private void insertMockData() {
        // 检查是否已有数据
        Integer count = mysqlJdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM fans", Integer.class);
        
        if (count != null && count > 0) {
            log.info("粉丝数据已存在，跳过数据插入");
            return;
        }

        // 插入粉丝基础信息数据
        String fansSql = """
                INSERT INTO fans (id, fan_id, nickname, platform, platform_user_id, gender, age_range, region, city, follow_time, status, source, fan_type, fan_value, activity_level, interaction_count, last_interaction_time, tags, created_by, create_time) VALUES
                (1, 'FAN001', '张小明', 'wechat', 'wx_001', 1, '26-35', '北京市', '北京', '2025-06-15 10:30:00', 'active', 'search', 'loyal', 'high', 85, 156, '2025-08-04 15:30:00', '["科技爱好者", "高消费"]', 1, '2025-06-15 10:30:00'),
                (2, 'FAN002', '李小红', 'weibo', 'wb_002', 2, '18-25', '上海市', '上海', '2025-06-20 14:20:00', 'active', 'recommend', 'potential', 'medium', 45, 89, '2025-08-03 12:15:00', '["时尚", "美妆"]', 1, '2025-06-20 14:20:00'),
                (3, 'FAN003', '王大力', 'wechat', 'wx_003', 1, '36-45', '广东省', '广州', '2025-06-25 09:15:00', 'active', 'share', 'loyal', 'high', 72, 134, '2025-08-04 09:45:00', '["商务", "投资"]', 1, '2025-06-25 09:15:00'),
                (4, 'FAN004', '陈小美', 'douyin', 'dy_004', 2, '18-25', '浙江省', '杭州', '2025-07-01 16:45:00', 'active', 'ads', 'ordinary', 'medium', 58, 67, '2025-08-02 20:30:00', '["娱乐", "音乐"]', 1, '2025-07-01 16:45:00'),
                (5, 'FAN005', '刘小强', 'xiaohongshu', 'xhs_005', 1, '26-35', '四川省', '成都', '2025-07-05 11:20:00', 'active', 'direct', 'vip', 'high', 91, 203, '2025-08-04 18:20:00', '["美食", "旅游", "VIP客户"]', 1, '2025-07-05 11:20:00'),
                (6, 'FAN006', '赵小芳', 'official', 'of_006', 2, '26-35', '江苏省', '南京', '2025-07-08 13:10:00', 'active', 'search', 'loyal', 'medium', 63, 98, '2025-08-03 16:45:00', '["教育", "亲子"]', 1, '2025-07-08 13:10:00'),
                (7, 'FAN007', '孙小龙', 'wechat', 'wx_007', 1, '46-55', '山东省', '青岛', '2025-07-10 08:30:00', 'inactive', 'recommend', 'ordinary', 'low', 23, 34, '2025-07-25 10:15:00', '["传统文化"]', 1, '2025-07-10 08:30:00'),
                (8, 'FAN008', '周小花', 'weibo', 'wb_008', 2, '18-25', '湖北省', '武汉', '2025-07-12 19:25:00', 'active', 'share', 'potential', 'medium', 67, 87, '2025-08-04 11:30:00', '["健身", "运动"]', 1, '2025-07-12 19:25:00'),
                (9, 'FAN009', '吴小军', 'douyin', 'dy_009', 1, '36-45', '河南省', '郑州', '2025-07-15 14:50:00', 'active', 'ads', 'loyal', 'high', 78, 145, '2025-08-04 14:20:00', '["汽车", "科技"]', 1, '2025-07-15 14:50:00'),
                (10, 'FAN010', '郑小玉', 'xiaohongshu', 'xhs_010', 2, '26-35', '福建省', '厦门', '2025-07-18 10:15:00', 'active', 'direct', 'vip', 'high', 88, 178, '2025-08-04 17:10:00', '["奢侈品", "时尚", "VIP客户"]', 1, '2025-07-18 10:15:00')
                """;
        
        mysqlJdbcTemplate.execute(fansSql);
        log.info("粉丝基础信息数据插入成功");

        // 插入粉丝互动记录数据
        String interactionsSql = """
                INSERT INTO fan_interactions (fan_id, interaction_type, interaction_date, interaction_time, content_id, content_type, interaction_value, platform, device_type) VALUES
                (1, 'like', '2025-08-04', '2025-08-04 15:30:00', 'post_001', 'post', 1, 'wechat', 'mobile'),
                (1, 'comment', '2025-08-04', '2025-08-04 15:32:00', 'post_001', 'post', 3, 'wechat', 'mobile'),
                (1, 'share', '2025-08-03', '2025-08-03 10:15:00', 'article_001', 'article', 5, 'wechat', 'mobile'),
                (2, 'like', '2025-08-03', '2025-08-03 12:15:00', 'video_001', 'video', 1, 'weibo', 'mobile'),
                (2, 'view', '2025-08-03', '2025-08-03 12:10:00', 'video_001', 'video', 1, 'weibo', 'mobile'),
                (3, 'like', '2025-08-04', '2025-08-04 09:45:00', 'product_001', 'product', 2, 'wechat', 'desktop'),
                (3, 'comment', '2025-08-04', '2025-08-04 09:50:00', 'product_001', 'product', 3, 'wechat', 'desktop'),
                (4, 'like', '2025-08-02', '2025-08-02 20:30:00', 'video_002', 'video', 1, 'douyin', 'mobile'),
                (4, 'share', '2025-08-02', '2025-08-02 20:35:00', 'video_002', 'video', 5, 'douyin', 'mobile'),
                (5, 'like', '2025-08-04', '2025-08-04 18:20:00', 'post_002', 'post', 1, 'xiaohongshu', 'mobile'),
                (5, 'comment', '2025-08-04', '2025-08-04 18:25:00', 'post_002', 'post', 3, 'xiaohongshu', 'mobile'),
                (5, 'message', '2025-08-04', '2025-08-04 18:30:00', 'msg_001', 'message', 10, 'xiaohongshu', 'mobile')
                """;
        
        mysqlJdbcTemplate.execute(interactionsSql);
        log.info("粉丝互动记录数据插入成功");

        // 插入粉丝统计数据
        String statsSql = """
                INSERT INTO fan_statistics (stat_date, platform, total_fans, new_fans, unfollowed_fans, active_fans, high_value_fans, medium_value_fans, low_value_fans, total_interactions, avg_activity_level, retention_rate) VALUES
                ('2025-08-01', 'wechat', 3, 1, 0, 2, 2, 0, 1, 45, 60.00, 95.50),
                ('2025-08-01', 'weibo', 2, 1, 0, 2, 0, 2, 0, 23, 56.00, 92.30),
                ('2025-08-01', 'douyin', 2, 1, 0, 2, 1, 1, 0, 34, 62.50, 94.20),
                ('2025-08-01', 'xiaohongshu', 2, 1, 0, 2, 2, 0, 0, 67, 89.50, 98.10),
                ('2025-08-01', 'official', 1, 1, 0, 1, 0, 1, 0, 12, 63.00, 96.00),
                ('2025-08-04', 'wechat', 3, 0, 0, 3, 2, 0, 1, 65, 62.67, 96.40),
                ('2025-08-04', 'weibo', 2, 0, 0, 2, 0, 2, 0, 35, 58.50, 93.20),
                ('2025-08-04', 'douyin', 2, 0, 0, 2, 1, 1, 0, 48, 64.00, 95.00),
                ('2025-08-04', 'xiaohongshu', 2, 0, 0, 2, 2, 0, 0, 82, 91.00, 98.70),
                ('2025-08-04', 'official', 1, 0, 0, 1, 0, 1, 0, 20, 66.00, 96.80)
                """;
        
        mysqlJdbcTemplate.execute(statsSql);
        log.info("粉丝统计数据插入成功");
    }
}
