package com.foxit.crm.modules.system.domain.repository;

import com.foxit.crm.modules.system.domain.model.aggregate.OperationLog;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 系统操作日志仓储接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface OperationLogRepository {

    /**
     * 保存操作日志
     */
    OperationLog save(OperationLog operationLog);

    /**
     * 根据ID查找操作日志
     */
    Optional<OperationLog> findById(Long id);

    /**
     * 分页查询操作日志
     */
    List<OperationLog> findByPage(int page, int size, String keyword, Integer status, 
                                 LocalDateTime startTime, LocalDateTime endTime, Long userId);

    /**
     * 统计操作日志总数
     */
    long count(String keyword, Integer status, LocalDateTime startTime, LocalDateTime endTime, Long userId);

    /**
     * 根据用户ID查找操作日志
     */
    List<OperationLog> findByUserId(Long userId, int page, int size);

    /**
     * 根据操作类型查找操作日志
     */
    List<OperationLog> findByOperation(String operation, int page, int size);

    /**
     * 根据时间范围查找操作日志
     */
    List<OperationLog> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime, int page, int size);

    /**
     * 查找失败的操作日志
     */
    List<OperationLog> findFailedOperations(int page, int size);

    /**
     * 查找慢操作日志（执行时间超过指定毫秒数）
     */
    List<OperationLog> findSlowOperations(Long minExecutionTime, int page, int size);

    /**
     * 查找敏感操作日志
     */
    List<OperationLog> findSensitiveOperations(int page, int size);

    /**
     * 根据IP地址查找操作日志
     */
    List<OperationLog> findByClientIp(String clientIp, int page, int size);

    /**
     * 删除指定时间之前的操作日志
     */
    void deleteByCreateTimeBefore(LocalDateTime beforeTime);

    /**
     * 统计用户操作次数
     */
    long countByUserId(Long userId);

    /**
     * 统计操作类型次数
     */
    long countByOperation(String operation);

    /**
     * 统计指定时间范围内的操作次数
     */
    long countByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计失败操作次数
     */
    long countFailedOperations();

    /**
     * 统计成功操作次数
     */
    long countSuccessOperations();

    /**
     * 获取最近的操作日志
     */
    List<OperationLog> findRecentOperations(int limit);

    /**
     * 获取用户最近的操作日志
     */
    List<OperationLog> findRecentOperationsByUserId(Long userId, int limit);

    /**
     * 获取操作统计信息（按操作类型分组）
     */
    List<OperationLog> getOperationStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取用户操作统计信息（按用户分组）
     */
    List<OperationLog> getUserOperationStatistics(LocalDateTime startTime, LocalDateTime endTime);
}
