package com.foxit.crm.modules.system.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.modules.system.api.dto.request.SystemConfigCreateRequest;
import com.foxit.crm.modules.system.api.dto.request.SystemConfigUpdateRequest;
import com.foxit.crm.modules.system.api.dto.response.SystemConfigDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.PageResponse;
import com.foxit.crm.modules.system.application.service.SystemConfigService;
import com.foxit.crm.shared.domain.event.OperationLog;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 系统配置管理控制器
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@RestController
@RequestMapping("/admin/system-configs")
@Validated
@RequiredArgsConstructor
@PreAuthorize("hasAuthority('ADMIN')") // 整个控制器只允许管理员访问
public class SystemConfigController {

    private final SystemConfigService systemConfigService;

    /**
     * 创建系统配置
     */
    @PostMapping
    @OperationLog(value = "创建系统配置", operation = "CREATE_SYSTEM_CONFIG", saveParams = true)
    public Result<Long> createSystemConfig(@Valid @RequestBody SystemConfigCreateRequest request) {
        Long systemConfigId = systemConfigService.createSystemConfig(request);
        return Result.success(systemConfigId, "系统配置创建成功");
    }

    /**
     * 更新系统配置
     */
    @PutMapping("/{id}")
    @OperationLog(value = "更新系统配置", operation = "UPDATE_SYSTEM_CONFIG", saveParams = true)
    public Result<String> updateSystemConfig(@PathVariable Long id,
            @Valid @RequestBody SystemConfigUpdateRequest request) {
        systemConfigService.updateSystemConfig(id, request);
        return Result.success("系统配置更新成功");
    }

    /**
     * 删除系统配置
     */
    @DeleteMapping("/{id}")
    @OperationLog(value = "删除系统配置", operation = "DELETE_SYSTEM_CONFIG")
    public Result<String> deleteSystemConfig(@PathVariable Long id) {
        systemConfigService.deleteSystemConfig(id);
        return Result.success("系统配置删除成功");
    }

    /**
     * 获取系统配置详情
     */
    @GetMapping("/{id}")
    @OperationLog(value = "查看系统配置详情", operation = "VIEW_SYSTEM_CONFIG")
    public Result<SystemConfigDetailResponse> getSystemConfigById(@PathVariable Long id) {
        SystemConfigDetailResponse response = systemConfigService.getSystemConfigById(id);
        return Result.success(response);
    }

    /**
     * 根据配置键获取系统配置详情
     */
    @GetMapping("/key/{configKey}")
    @OperationLog(value = "按配置键查看系统配置", operation = "VIEW_SYSTEM_CONFIG_BY_KEY")
    public Result<SystemConfigDetailResponse> getSystemConfigByKey(@PathVariable String configKey) {
        SystemConfigDetailResponse response = systemConfigService.getSystemConfigByKey(configKey);
        return Result.success(response);
    }

    /**
     * 分页查询系统配置列表
     */
    @GetMapping
    @OperationLog(value = "查看系统配置列表", operation = "LIST_SYSTEM_CONFIGS")
    public Result<PageResponse<SystemConfigDetailResponse>> getSystemConfigList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String configGroup,
            @RequestParam(required = false) Integer status) {
        PageResponse<SystemConfigDetailResponse> response = systemConfigService.getSystemConfigList(page, size, keyword,
                configGroup, status);
        return Result.success(response);
    }

    /**
     * 根据配置分组获取系统配置列表
     */
    @GetMapping("/group/{configGroup}")
    @OperationLog(value = "按分组获取系统配置", operation = "GET_SYSTEM_CONFIGS_BY_GROUP")
    public Result<List<SystemConfigDetailResponse>> getSystemConfigsByGroup(@PathVariable String configGroup) {
        List<SystemConfigDetailResponse> response = systemConfigService.getSystemConfigsByGroup(configGroup);
        return Result.success(response);
    }

    /**
     * 获取所有启用的系统配置
     */
    @GetMapping("/enabled")
    @OperationLog(value = "获取启用系统配置", operation = "GET_ENABLED_SYSTEM_CONFIGS")
    public Result<List<SystemConfigDetailResponse>> getAllEnabledSystemConfigs() {
        List<SystemConfigDetailResponse> response = systemConfigService.getAllEnabledSystemConfigs();
        return Result.success(response);
    }

    /**
     * 获取系统内置配置
     */
    @GetMapping("/system")
    @OperationLog(value = "获取系统内置配置", operation = "GET_SYSTEM_CONFIGS")
    public Result<List<SystemConfigDetailResponse>> getSystemConfigs() {
        List<SystemConfigDetailResponse> response = systemConfigService.getSystemConfigs();
        return Result.success(response);
    }

    /**
     * 获取用户自定义配置
     */
    @GetMapping("/user")
    @OperationLog(value = "获取用户自定义配置", operation = "GET_USER_CONFIGS")
    public Result<List<SystemConfigDetailResponse>> getUserConfigs() {
        List<SystemConfigDetailResponse> response = systemConfigService.getUserConfigs();
        return Result.success(response);
    }

    /**
     * 启用系统配置
     */
    @PutMapping("/{id}/enable")
    @OperationLog(value = "启用系统配置", operation = "ENABLE_SYSTEM_CONFIG")
    public Result<String> enableSystemConfig(@PathVariable Long id) {
        systemConfigService.enableSystemConfig(id);
        return Result.success("系统配置启用成功");
    }

    /**
     * 禁用系统配置
     */
    @PutMapping("/{id}/disable")
    @OperationLog(value = "禁用系统配置", operation = "DISABLE_SYSTEM_CONFIG")
    public Result<String> disableSystemConfig(@PathVariable Long id) {
        systemConfigService.disableSystemConfig(id);
        return Result.success("系统配置禁用成功");
    }

    /**
     * 批量启用系统配置
     */
    @PutMapping("/batch/enable")
    @OperationLog(value = "批量启用系统配置", operation = "BATCH_ENABLE_SYSTEM_CONFIGS", saveParams = true)
    public Result<String> enableSystemConfigsBatch(@RequestBody List<Long> ids) {
        systemConfigService.enableSystemConfigsBatch(ids);
        return Result.success("系统配置批量启用成功");
    }

    /**
     * 批量禁用系统配置
     */
    @PutMapping("/batch/disable")
    @OperationLog(value = "批量禁用系统配置", operation = "BATCH_DISABLE_SYSTEM_CONFIGS", saveParams = true)
    public Result<String> disableSystemConfigsBatch(@RequestBody List<Long> ids) {
        systemConfigService.disableSystemConfigsBatch(ids);
        return Result.success("系统配置批量禁用成功");
    }

    /**
     * 获取所有配置分组
     */
    @GetMapping("/groups")
    @OperationLog(value = "获取配置分组列表", operation = "GET_CONFIG_GROUPS")
    public Result<List<String>> getAllConfigGroups() {
        List<String> response = systemConfigService.getAllConfigGroups();
        return Result.success(response);
    }

    /**
     * 检查配置键是否可用
     */
    @GetMapping("/check-key")
    @OperationLog(value = "检查配置键可用性", operation = "CHECK_CONFIG_KEY")
    public Result<Boolean> checkConfigKeyAvailability(@RequestParam String configKey,
            @RequestParam(required = false) Long excludeId) {
        boolean available = excludeId != null ? systemConfigService.isConfigKeyAvailable(configKey, excludeId)
                : systemConfigService.isConfigKeyAvailable(configKey);
        return Result.success(available);
    }

    /**
     * 根据配置键获取配置值
     */
    @GetMapping("/value/{configKey}")
    @OperationLog(value = "获取配置值", operation = "GET_CONFIG_VALUE")
    public Result<String> getConfigValue(@PathVariable String configKey) {
        String value = systemConfigService.getConfigValue(configKey);
        return Result.success(value);
    }

    /**
     * 根据配置键设置配置值
     */
    @PutMapping("/value/{configKey}")
    @OperationLog(value = "设置配置值", operation = "SET_CONFIG_VALUE", saveParams = true)
    public Result<String> setConfigValue(@PathVariable String configKey, @RequestBody String configValue) {
        systemConfigService.setConfigValue(configKey, configValue);
        return Result.success("配置值设置成功");
    }

    /**
     * 批量获取配置值
     */
    @PostMapping("/values/batch")
    @OperationLog(value = "批量获取配置值", operation = "GET_CONFIG_VALUES_BATCH", saveParams = true)
    public Result<Map<String, String>> getConfigValues(@RequestBody List<String> configKeys) {
        Map<String, String> values = systemConfigService.getConfigValues(configKeys);
        return Result.success(values);
    }

    /**
     * 批量设置配置值
     */
    @PutMapping("/values/batch")
    @OperationLog(value = "批量设置配置值", operation = "SET_CONFIG_VALUES_BATCH", saveParams = true)
    public Result<String> setConfigValues(@RequestBody Map<String, String> configValues) {
        systemConfigService.setConfigValues(configValues);
        return Result.success("配置值批量设置成功");
    }

    /**
     * 刷新配置缓存
     */
    @PostMapping("/refresh-cache")
    @OperationLog(value = "刷新配置缓存", operation = "REFRESH_CONFIG_CACHE")
    public Result<String> refreshConfigCache() {
        systemConfigService.refreshConfigCache();
        return Result.success("配置缓存刷新成功");
    }
}
