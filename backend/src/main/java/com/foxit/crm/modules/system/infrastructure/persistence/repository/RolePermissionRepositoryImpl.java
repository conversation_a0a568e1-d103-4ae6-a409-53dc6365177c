package com.foxit.crm.modules.system.infrastructure.persistence.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.foxit.crm.modules.system.domain.model.aggregate.RolePermission;
import com.foxit.crm.modules.system.domain.repository.RolePermissionRepository;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.RolePermissionPO;
import com.foxit.crm.modules.system.infrastructure.persistence.mapper.RolePermissionMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 角色权限关联仓储实现
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Repository
@RequiredArgsConstructor
public class RolePermissionRepositoryImpl implements RolePermissionRepository {

    private final RolePermissionMapper rolePermissionMapper;

    @Override
    public RolePermission save(RolePermission rolePermission) {
        RolePermissionPO rolePermissionPO = new RolePermissionPO();
        BeanUtils.copyProperties(rolePermission, rolePermissionPO);
        
        if (rolePermission.getId() == null) {
            rolePermissionMapper.insert(rolePermissionPO);
            rolePermission.setId(rolePermissionPO.getId());
        } else {
            rolePermissionMapper.updateById(rolePermissionPO);
        }
        
        return rolePermission;
    }

    @Override
    public Optional<RolePermission> findById(Long id) {
        RolePermissionPO rolePermissionPO = rolePermissionMapper.selectById(id);
        if (rolePermissionPO == null) {
            return Optional.empty();
        }
        
        RolePermission rolePermission = new RolePermission();
        BeanUtils.copyProperties(rolePermissionPO, rolePermission);
        return Optional.of(rolePermission);
    }

    @Override
    public List<RolePermission> findByRoleId(Long roleId) {
        QueryWrapper<RolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        
        List<RolePermissionPO> rolePermissionPOs = rolePermissionMapper.selectList(queryWrapper);
        return rolePermissionPOs.stream()
                               .map(this::convertToRolePermission)
                               .collect(Collectors.toList());
    }

    @Override
    public List<RolePermission> findByPermissionId(Long permissionId) {
        QueryWrapper<RolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_id", permissionId);
        
        List<RolePermissionPO> rolePermissionPOs = rolePermissionMapper.selectList(queryWrapper);
        return rolePermissionPOs.stream()
                               .map(this::convertToRolePermission)
                               .collect(Collectors.toList());
    }

    @Override
    public Optional<RolePermission> findByRoleIdAndPermissionId(Long roleId, Long permissionId) {
        QueryWrapper<RolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId)
                   .eq("permission_id", permissionId);
        
        RolePermissionPO rolePermissionPO = rolePermissionMapper.selectOne(queryWrapper);
        if (rolePermissionPO == null) {
            return Optional.empty();
        }
        
        RolePermission rolePermission = new RolePermission();
        BeanUtils.copyProperties(rolePermissionPO, rolePermission);
        return Optional.of(rolePermission);
    }

    @Override
    public void saveBatch(List<RolePermission> rolePermissions) {
        List<RolePermissionPO> rolePermissionPOs = rolePermissions.stream()
            .map(rolePermission -> {
                RolePermissionPO rolePermissionPO = new RolePermissionPO();
                BeanUtils.copyProperties(rolePermission, rolePermissionPO);
                return rolePermissionPO;
            })
            .collect(Collectors.toList());
        
        // 使用MyBatis-Plus的批量插入
        rolePermissionPOs.forEach(rolePermissionMapper::insert);
    }

    @Override
    public void deleteByRoleId(Long roleId) {
        QueryWrapper<RolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        rolePermissionMapper.delete(queryWrapper);
    }

    @Override
    public void deleteByPermissionId(Long permissionId) {
        QueryWrapper<RolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_id", permissionId);
        rolePermissionMapper.delete(queryWrapper);
    }

    @Override
    public void deleteByRoleIdAndPermissionId(Long roleId, Long permissionId) {
        QueryWrapper<RolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId)
                   .eq("permission_id", permissionId);
        rolePermissionMapper.delete(queryWrapper);
    }

    @Override
    public void deleteByRoleIds(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return;
        }
        
        QueryWrapper<RolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("role_id", roleIds);
        rolePermissionMapper.delete(queryWrapper);
    }

    @Override
    public void deleteByPermissionIds(List<Long> permissionIds) {
        if (permissionIds == null || permissionIds.isEmpty()) {
            return;
        }
        
        QueryWrapper<RolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("permission_id", permissionIds);
        rolePermissionMapper.delete(queryWrapper);
    }

    @Override
    public boolean existsByRoleIdAndPermissionId(Long roleId, Long permissionId) {
        QueryWrapper<RolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId)
                   .eq("permission_id", permissionId);
        return rolePermissionMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public long countByPermissionId(Long permissionId) {
        QueryWrapper<RolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_id", permissionId);
        return rolePermissionMapper.selectCount(queryWrapper);
    }

    @Override
    public long countByRoleId(Long roleId) {
        QueryWrapper<RolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        return rolePermissionMapper.selectCount(queryWrapper);
    }

    @Override
    public List<Long> findPermissionIdsByRoleId(Long roleId) {
        QueryWrapper<RolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId)
                   .select("permission_id");
        
        List<RolePermissionPO> rolePermissionPOs = rolePermissionMapper.selectList(queryWrapper);
        return rolePermissionPOs.stream()
                               .map(RolePermissionPO::getPermissionId)
                               .collect(Collectors.toList());
    }

    @Override
    public List<Long> findRoleIdsByPermissionId(Long permissionId) {
        QueryWrapper<RolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_id", permissionId)
                   .select("role_id");
        
        List<RolePermissionPO> rolePermissionPOs = rolePermissionMapper.selectList(queryWrapper);
        return rolePermissionPOs.stream()
                               .map(RolePermissionPO::getRoleId)
                               .collect(Collectors.toList());
    }

    @Override
    public List<Long> findPermissionIdsByRoleIds(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return List.of();
        }
        
        QueryWrapper<RolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("role_id", roleIds)
                   .select("permission_id");
        
        List<RolePermissionPO> rolePermissionPOs = rolePermissionMapper.selectList(queryWrapper);
        return rolePermissionPOs.stream()
                               .map(RolePermissionPO::getPermissionId)
                               .distinct()
                               .collect(Collectors.toList());
    }

    @Override
    public List<Long> findRoleIdsByPermissionIds(List<Long> permissionIds) {
        if (permissionIds == null || permissionIds.isEmpty()) {
            return List.of();
        }
        
        QueryWrapper<RolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("permission_id", permissionIds)
                   .select("role_id");
        
        List<RolePermissionPO> rolePermissionPOs = rolePermissionMapper.selectList(queryWrapper);
        return rolePermissionPOs.stream()
                               .map(RolePermissionPO::getRoleId)
                               .distinct()
                               .collect(Collectors.toList());
    }

    /**
     * 转换PO为领域对象
     */
    private RolePermission convertToRolePermission(RolePermissionPO rolePermissionPO) {
        RolePermission rolePermission = new RolePermission();
        BeanUtils.copyProperties(rolePermissionPO, rolePermission);
        return rolePermission;
    }
}
