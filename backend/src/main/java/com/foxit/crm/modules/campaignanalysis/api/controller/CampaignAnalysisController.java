package com.foxit.crm.modules.campaignanalysis.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.common.util.SecurityUtils;
import com.foxit.crm.modules.campaignanalysis.application.dto.CampaignAnalysisRequest;
import com.foxit.crm.modules.campaignanalysis.application.dto.CampaignAnalysisResponse;
import com.foxit.crm.modules.campaignanalysis.application.service.CampaignAnalysisService;
import com.foxit.crm.modules.campaignanalysis.domain.repository.CampaignAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 活动分析控制器
 * 提供活动分析相关的REST API接口
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@RestController
@RequestMapping("/activity/campaign")
@RequiredArgsConstructor
@Tag(name = "活动分析", description = "营销活动分析相关接口")
public class CampaignAnalysisController {

    private final CampaignAnalysisService campaignAnalysisService;

    // ==================== 需要认证的接口 ====================

    /**
     * 获取活动总览
     */
    @Operation(summary = "获取活动总览", description = "获取指定时间范围内的活动总览数据")
    @GetMapping("/overview")
    @PreAuthorize("hasPermission('activity:campaign', 'READ')")
    public Result<CampaignAnalysisResponse> getOverview(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "活动ID列表") @RequestParam(required = false) List<Long> campaignIds,
            @Parameter(description = "活动类型列表") @RequestParam(required = false) List<String> campaignTypes,
            @Parameter(description = "活动状态列表") @RequestParam(required = false) List<String> statuses,
            @Parameter(description = "渠道列表") @RequestParam(required = false) List<String> channels) {

        try {
            CampaignAnalysisRequest request = CampaignAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .campaignIds(campaignIds)
                    .campaignTypes(campaignTypes)
                    .statuses(statuses)
                    .channels(channels)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            CampaignAnalysisResponse response = campaignAnalysisService.getCampaignOverview(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取活动总览失败", e);
            return Result.error("获取活动总览失败: " + e.getMessage());
        }
    }

    /**
     * 获取活动效果分析
     */
    @Operation(summary = "获取活动效果分析", description = "获取活动的效果分析数据")
    @GetMapping("/performance")
    @PreAuthorize("hasPermission('activity:campaign', 'READ')")
    public Result<CampaignAnalysisResponse> getPerformance(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "活动ID列表") @RequestParam(required = false) List<Long> campaignIds) {

        try {
            CampaignAnalysisRequest request = CampaignAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .campaignIds(campaignIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            CampaignAnalysisResponse response = campaignAnalysisService.getCampaignPerformance(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取活动效果分析失败", e);
            return Result.error("获取活动效果分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取实时统计
     */
    @Operation(summary = "获取实时活动统计", description = "获取实时活动统计数据")
    @GetMapping("/realtime-stats")
    @PreAuthorize("hasPermission('activity:campaign', 'READ')")
    public Result<CampaignAnalysisRepository.CampaignRealTimeStats> getRealTimeStats(
            @Parameter(description = "活动ID列表") @RequestParam(required = false) List<Long> campaignIds) {

        try {
            CampaignAnalysisRequest request = CampaignAnalysisRequest.builder()
                    .campaignIds(campaignIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            CampaignAnalysisRepository.CampaignRealTimeStats stats = campaignAnalysisService.getRealTimeStats(request);
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取实时活动统计失败", e);
            return Result.error("获取实时活动统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取分析摘要
     */
    @Operation(summary = "获取活动分析摘要", description = "获取活动分析的摘要信息")
    @GetMapping("/summary")
    @PreAuthorize("hasPermission('activity:campaign', 'READ')")
    public Result<CampaignAnalysisRepository.CampaignAnalysisSummary> getSummary(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        try {
            CampaignAnalysisRequest request = CampaignAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            CampaignAnalysisRepository.CampaignAnalysisSummary summary = campaignAnalysisService.getAnalysisSummary(request);
            return Result.success(summary);

        } catch (Exception e) {
            log.error("获取活动分析摘要失败", e);
            return Result.error("获取活动分析摘要失败: " + e.getMessage());
        }
    }

    // ==================== 公共接口（无需认证） ====================

    /**
     * 获取公共活动总览 - 无需认证
     */
    @Operation(summary = "获取公共活动总览", description = "获取活动总览数据（无需认证）")
    @GetMapping("/public/overview")
    public Result<CampaignAnalysisResponse> getPublicOverview(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "活动ID列表") @RequestParam(required = false) List<Long> campaignIds,
            @Parameter(description = "数据权限范围") @RequestParam(required = false, defaultValue = "PUBLIC") String dataScope) {

        try {
            CampaignAnalysisRequest request = CampaignAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .campaignIds(campaignIds)
                    .dataScope(dataScope)
                    .build();

            CampaignAnalysisResponse response = campaignAnalysisService.getCampaignOverview(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取公共活动总览失败", e);
            return Result.error("获取公共活动总览失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共实时统计 - 无需认证
     */
    @Operation(summary = "获取公共实时活动统计", description = "获取实时活动统计数据（无需认证）")
    @GetMapping("/public/realtime-stats")
    public Result<CampaignAnalysisRepository.CampaignRealTimeStats> getPublicRealTimeStats(
            @Parameter(description = "活动ID列表") @RequestParam(required = false) List<Long> campaignIds,
            @Parameter(description = "数据权限范围") @RequestParam(required = false, defaultValue = "PUBLIC") String dataScope) {

        try {
            CampaignAnalysisRequest request = CampaignAnalysisRequest.builder()
                    .campaignIds(campaignIds)
                    .dataScope(dataScope)
                    .build();

            CampaignAnalysisRepository.CampaignRealTimeStats stats = campaignAnalysisService.getRealTimeStats(request);
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取公共实时活动统计失败", e);
            return Result.error("获取公共实时活动统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共分析摘要 - 无需认证
     */
    @Operation(summary = "获取公共活动分析摘要", description = "获取活动分析摘要（无需认证）")
    @GetMapping("/public/summary")
    public Result<CampaignAnalysisRepository.CampaignAnalysisSummary> getPublicSummary(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "数据权限范围") @RequestParam(required = false, defaultValue = "PUBLIC") String dataScope) {

        try {
            CampaignAnalysisRequest request = CampaignAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .dataScope(dataScope)
                    .build();

            CampaignAnalysisRepository.CampaignAnalysisSummary summary = campaignAnalysisService.getAnalysisSummary(request);
            return Result.success(summary);

        } catch (Exception e) {
            log.error("获取公共活动分析摘要失败", e);
            return Result.error("获取公共活动分析摘要失败: " + e.getMessage());
        }
    }
}
