package com.foxit.crm.modules.behavioranalysis.application.dto;

import com.foxit.crm.modules.behavioranalysis.domain.entity.EventAnalysisAggregate;
import com.foxit.crm.modules.useranalysis.domain.valueobject.MetricValue;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 事件分析响应DTO
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Builder
@Schema(description = "事件分析响应")
public class EventAnalysisResponse {

    @Schema(description = "分析ID")
    private String analysisId;

    @Schema(description = "分析类型")
    private String analysisType;

    @Schema(description = "时间范围")
    private TimeRange timeRange;

    @Schema(description = "核心指标")
    private Map<String, MetricValue> coreMetrics;

    @Schema(description = "趋势数据")
    private Map<String, EventAnalysisAggregate.TrendData> trendData;

    @Schema(description = "漏斗数据")
    private Map<String, EventAnalysisAggregate.FunnelData> funnelData;

    @Schema(description = "路径数据")
    private Map<String, EventAnalysisAggregate.PathData> pathData;

    @Schema(description = "事件属性数据")
    private Map<String, EventAnalysisAggregate.EventPropertyData> eventPropertyData;

    @Schema(description = "最后更新时间")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "数据状态")
    private String dataStatus;

    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 事件趋势数据DTO
     */
    @Data
    @Builder
    @Schema(description = "事件趋势数据")
    public static class EventTrendData {

        @Schema(description = "事件ID")
        private String eventId;

        @Schema(description = "事件名称")
        private String eventName;

        @Schema(description = "时间分类")
        private List<String> categories;

        @Schema(description = "触发次数")
        private List<Number> triggerCounts;

        @Schema(description = "用户数")
        private List<Number> userCounts;

        @Schema(description = "单位")
        private String unit;

        @Schema(description = "图表类型")
        private String chartType;
    }

    /**
     * 事件漏斗数据DTO
     */
    @Data
    @Builder
    @Schema(description = "事件漏斗数据")
    public static class EventFunnelData {

        @Schema(description = "漏斗名称")
        private String funnelName;

        @Schema(description = "漏斗步骤")
        private List<FunnelStepData> steps;

        @Schema(description = "总转化率")
        private Double totalConversionRate;

        @Schema(description = "总用户数")
        private Long totalUsers;

        @Schema(description = "时间范围")
        private String timeRange;
    }

    /**
     * 漏斗步骤数据DTO
     */
    @Data
    @Builder
    @Schema(description = "漏斗步骤数据")
    public static class FunnelStepData {

        @Schema(description = "事件ID")
        private String eventId;

        @Schema(description = "事件名称")
        private String eventName;

        @Schema(description = "用户数")
        private Long userCount;

        @Schema(description = "转化率")
        private Double conversionRate;

        @Schema(description = "流失率")
        private Double dropOffRate;

        @Schema(description = "步骤顺序")
        private Integer stepOrder;
    }

    /**
     * 事件路径数据DTO
     */
    @Data
    @Builder
    @Schema(description = "事件路径数据")
    public static class EventPathData {

        @Schema(description = "路径名称")
        private String pathName;

        @Schema(description = "路径节点")
        private List<PathNodeData> nodes;

        @Schema(description = "路径链接")
        private List<PathLinkData> links;

        @Schema(description = "总用户数")
        private Long totalUsers;

        @Schema(description = "路径类型")
        private String pathType;
    }

    /**
     * 路径节点数据DTO
     */
    @Data
    @Builder
    @Schema(description = "路径节点数据")
    public static class PathNodeData {

        @Schema(description = "事件ID")
        private String eventId;

        @Schema(description = "事件名称")
        private String eventName;

        @Schema(description = "用户数")
        private Long userCount;

        @Schema(description = "百分比")
        private Double percentage;

        @Schema(description = "层级")
        private Integer level;
    }

    /**
     * 路径链接数据DTO
     */
    @Data
    @Builder
    @Schema(description = "路径链接数据")
    public static class PathLinkData {

        @Schema(description = "源事件ID")
        private String sourceEventId;

        @Schema(description = "目标事件ID")
        private String targetEventId;

        @Schema(description = "用户数")
        private Long userCount;

        @Schema(description = "百分比")
        private Double percentage;

        @Schema(description = "平均耗时")
        private Double avgTimeSpent;
    }

    /**
     * 事件属性数据DTO
     */
    @Data
    @Builder
    @Schema(description = "事件属性数据")
    public static class EventPropertyData {

        @Schema(description = "事件ID")
        private String eventId;

        @Schema(description = "属性名称")
        private String propertyName;

        @Schema(description = "属性值分布")
        private Map<String, Long> propertyValues;

        @Schema(description = "数据类型")
        private String dataType;

        @Schema(description = "总数")
        private Long totalCount;
    }

    /**
     * 创建空响应
     */
    public static EventAnalysisResponse empty() {
        return EventAnalysisResponse.builder()
                .dataStatus("EMPTY")
                .errorMessage("未找到数据")
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建错误响应
     */
    public static EventAnalysisResponse error(String errorMessage) {
        return EventAnalysisResponse.builder()
                .dataStatus("ERROR")
                .errorMessage(errorMessage)
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功响应
     */
    public static EventAnalysisResponse success(String analysisId, String analysisType) {
        return EventAnalysisResponse.builder()
                .analysisId(analysisId)
                .analysisType(analysisType)
                .dataStatus("SUCCESS")
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 检查是否有数据
     */
    public boolean hasData() {
        return "SUCCESS".equals(dataStatus) && 
               (coreMetrics != null && !coreMetrics.isEmpty() ||
                trendData != null && !trendData.isEmpty() ||
                funnelData != null && !funnelData.isEmpty() ||
                pathData != null && !pathData.isEmpty() ||
                eventPropertyData != null && !eventPropertyData.isEmpty());
    }

    /**
     * 检查是否有错误
     */
    public boolean hasError() {
        return "ERROR".equals(dataStatus) || errorMessage != null;
    }

    /**
     * 获取数据摘要
     */
    public String getDataSummary() {
        if (hasError()) {
            return "错误: " + errorMessage;
        }
        
        if (!hasData()) {
            return "无数据";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("分析类型: ").append(analysisType);
        
        if (coreMetrics != null && !coreMetrics.isEmpty()) {
            summary.append(", 核心指标数: ").append(coreMetrics.size());
        }
        
        if (trendData != null && !trendData.isEmpty()) {
            summary.append(", 趋势数据数: ").append(trendData.size());
        }
        
        if (funnelData != null && !funnelData.isEmpty()) {
            summary.append(", 漏斗数据数: ").append(funnelData.size());
        }
        
        if (pathData != null && !pathData.isEmpty()) {
            summary.append(", 路径数据数: ").append(pathData.size());
        }
        
        return summary.toString();
    }
}
