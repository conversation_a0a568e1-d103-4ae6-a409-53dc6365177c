package com.foxit.crm.modules.behavioranalysis.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.common.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.*;

/**
 * 搜索分析控制器
 * 提供搜索行为分析相关的REST API接口
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Slf4j
@RestController
@RequestMapping("/behavior/search")
@RequiredArgsConstructor
@Tag(name = "搜索分析", description = "搜索行为分析相关接口")
public class SearchAnalysisController {

    @Operation(summary = "获取搜索数据统计", description = "获取搜索关键词和搜索行为的统计数据")
    @GetMapping("/statistics")
    @PreAuthorize("hasPermission('behavior:search', 'READ')")
    public Result<Map<String, Object>> getSearchStatistics(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds,
            @Parameter(description = "搜索类型") @RequestParam(required = false) String searchType,
            @Parameter(description = "结果状态") @RequestParam(required = false) String resultStatus) {

        try {
            // 模拟搜索统计数据
            Map<String, Object> response = new HashMap<>();
            
            // 核心指标
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("totalSearches", 12580);
            metrics.put("successRate", 87.5);
            metrics.put("avgSearchTime", 1.2);
            metrics.put("hotKeywords", 156);
            response.put("metrics", metrics);
            
            // 搜索趋势数据
            List<Map<String, Object>> trendData = new ArrayList<>();
            for (int i = 0; i < 7; i++) {
                Map<String, Object> trend = new HashMap<>();
                trend.put("date", startDate.plusDays(i).toString());
                trend.put("searches", 1800 + (int)(Math.random() * 400));
                trend.put("successRate", 85 + Math.random() * 10);
                trendData.add(trend);
            }
            response.put("trendData", trendData);
            
            // 热门关键词
            List<Map<String, Object>> hotKeywords = new ArrayList<>();
            String[] keywords = {"PDF编辑", "文档转换", "电子签名", "OCR识别", "批注工具", "页面管理", "安全设置", "打印设置"};
            for (int i = 0; i < keywords.length; i++) {
                Map<String, Object> keyword = new HashMap<>();
                keyword.put("keyword", keywords[i]);
                keyword.put("searchCount", 500 - i * 50);
                keyword.put("successRate", 90 - i * 2);
                keyword.put("avgTime", 1.0 + i * 0.1);
                hotKeywords.add(keyword);
            }
            response.put("hotKeywords", hotKeywords);
            
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取搜索数据统计失败", e);
            return Result.error("获取搜索数据统计失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取搜索关键词列表", description = "获取搜索关键词的详细列表数据")
    @GetMapping("/keywords")
    @PreAuthorize("hasPermission('behavior:search', 'READ')")
    public Result<Map<String, Object>> getSearchKeywords(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds,
            @Parameter(description = "页码") @RequestParam(required = false, defaultValue = "1") Integer page,
            @Parameter(description = "页大小") @RequestParam(required = false, defaultValue = "20") Integer pageSize) {

        try {
            // 模拟关键词列表数据
            List<Map<String, Object>> keywordList = new ArrayList<>();
            String[] keywords = {"PDF编辑", "文档转换", "电子签名", "OCR识别", "批注工具", "页面管理", "安全设置", "打印设置", 
                               "文件合并", "页面提取", "书签管理", "表单填写", "数字证书", "水印添加", "文档压缩", "格式转换"};
            
            for (int i = 0; i < Math.min(pageSize, keywords.length); i++) {
                Map<String, Object> keyword = new HashMap<>();
                keyword.put("id", i + 1);
                keyword.put("keyword", keywords[i]);
                keyword.put("searchCount", 500 - i * 20);
                keyword.put("successRate", 90.0 - i * 1.5);
                keyword.put("avgTime", 1.0 + i * 0.05);
                keyword.put("trend", i % 3 == 0 ? "up" : (i % 3 == 1 ? "down" : "stable"));
                keyword.put("category", i < 4 ? "核心功能" : (i < 8 ? "高级功能" : "辅助功能"));
                keywordList.add(keyword);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("list", keywordList);
            response.put("total", keywords.length);
            response.put("page", page);
            response.put("pageSize", pageSize);
            
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取搜索关键词列表失败", e);
            return Result.error("获取搜索关键词列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取搜索热力图数据", description = "获取搜索行为的热力图分析数据")
    @GetMapping("/heatmap")
    @PreAuthorize("hasPermission('behavior:search', 'READ')")
    public Result<Map<String, Object>> getSearchHeatmap(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            // 模拟热力图数据
            List<Map<String, Object>> heatmapData = new ArrayList<>();
            
            // 生成24小时 x 7天的热力图数据
            String[] days = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
            for (int day = 0; day < 7; day++) {
                for (int hour = 0; hour < 24; hour++) {
                    Map<String, Object> point = new HashMap<>();
                    point.put("day", days[day]);
                    point.put("hour", hour);
                    // 工作时间搜索量更高
                    int baseValue = (hour >= 9 && hour <= 18 && day < 5) ? 50 : 10;
                    point.put("value", baseValue + (int)(Math.random() * 30));
                    heatmapData.add(point);
                }
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("heatmapData", heatmapData);
            
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取搜索热力图数据失败", e);
            return Result.error("获取搜索热力图数据失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取搜索失败分析", description = "分析搜索失败的原因和模式")
    @GetMapping("/failure-analysis")
    @PreAuthorize("hasPermission('behavior:search', 'READ')")
    public Result<Map<String, Object>> getSearchFailureAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            // 模拟搜索失败分析数据
            Map<String, Object> response = new HashMap<>();
            
            // 失败原因分布
            List<Map<String, Object>> failureReasons = new ArrayList<>();
            String[] reasons = {"关键词不匹配", "功能不存在", "权限不足", "系统错误", "网络超时"};
            int[] counts = {45, 25, 15, 10, 5};
            
            for (int i = 0; i < reasons.length; i++) {
                Map<String, Object> reason = new HashMap<>();
                reason.put("reason", reasons[i]);
                reason.put("count", counts[i]);
                reason.put("percentage", counts[i]);
                failureReasons.add(reason);
            }
            response.put("failureReasons", failureReasons);
            
            // 失败关键词Top10
            List<Map<String, Object>> failedKeywords = new ArrayList<>();
            String[] keywords = {"高级编辑", "批量处理", "云同步", "API接口", "插件开发"};
            for (int i = 0; i < keywords.length; i++) {
                Map<String, Object> keyword = new HashMap<>();
                keyword.put("keyword", keywords[i]);
                keyword.put("failCount", 50 - i * 8);
                keyword.put("totalCount", 100 - i * 10);
                keyword.put("failRate", (50.0 - i * 8) / (100 - i * 10) * 100);
                failedKeywords.add(keyword);
            }
            response.put("failedKeywords", failedKeywords);
            
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取搜索失败分析失败", e);
            return Result.error("获取搜索失败分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取搜索优化建议", description = "基于搜索数据分析提供优化建议")
    @GetMapping("/optimization-suggestions")
    @PreAuthorize("hasPermission('behavior:search', 'READ')")
    public Result<List<Map<String, Object>>> getOptimizationSuggestions(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            // 模拟优化建议
            List<Map<String, Object>> suggestions = new ArrayList<>();
            
            Map<String, Object> suggestion1 = new HashMap<>();
            suggestion1.put("type", "关键词优化");
            suggestion1.put("title", "增加同义词支持");
            suggestion1.put("description", "为高频搜索关键词添加同义词，提高搜索成功率");
            suggestion1.put("priority", "高");
            suggestion1.put("impact", "预计提升成功率5-8%");
            suggestions.add(suggestion1);
            
            Map<String, Object> suggestion2 = new HashMap<>();
            suggestion2.put("type", "搜索体验");
            suggestion2.put("title", "优化搜索建议");
            suggestion2.put("description", "在用户输入时提供智能搜索建议");
            suggestion2.put("priority", "中");
            suggestion2.put("impact", "预计减少搜索时间20%");
            suggestions.add(suggestion2);
            
            Map<String, Object> suggestion3 = new HashMap<>();
            suggestion3.put("type", "内容完善");
            suggestion3.put("title", "补充帮助文档");
            suggestion3.put("description", "针对高失败率关键词补充相关帮助文档");
            suggestion3.put("priority", "中");
            suggestion3.put("impact", "预计减少失败率10%");
            suggestions.add(suggestion3);
            
            return Result.success(suggestions);

        } catch (Exception e) {
            log.error("获取搜索优化建议失败", e);
            return Result.error("获取搜索优化建议失败: " + e.getMessage());
        }
    }
}
