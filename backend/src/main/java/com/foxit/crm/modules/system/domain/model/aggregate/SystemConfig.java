package com.foxit.crm.modules.system.domain.model.aggregate;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 系统配置聚合根
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@NoArgsConstructor
public class SystemConfig {

    /**
     * 配置ID
     */
    private Long id;

    /**
     * 配置键
     */
    private String configKey;

    /**
     * 配置值
     */
    private String configValue;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置描述
     */
    private String description;

    /**
     * 配置类型：1-字符串，2-数字，3-布尔值，4-JSON
     */
    private Integer configType;

    /**
     * 配置分组
     */
    private String configGroup;

    /**
     * 是否系统内置：0-否，1-是
     */
    private Integer isSystem;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 版本号（乐观锁）
     */
    private Integer version;

    /**
     * 配置类型枚举
     */
    public enum ConfigType {
        STRING(1, "字符串"),
        NUMBER(2, "数字"),
        BOOLEAN(3, "布尔值"),
        JSON(4, "JSON");

        private final Integer code;
        private final String name;

        ConfigType(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static ConfigType fromCode(Integer code) {
            for (ConfigType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 构造函数
     */
    public SystemConfig(String configKey, String configValue, String configName, String description,
                       Integer configType, String configGroup, Integer isSystem, Integer status,
                       Integer sortOrder, String remark) {
        this.configKey = configKey;
        this.configValue = configValue;
        this.configName = configName;
        this.description = description;
        this.configType = configType;
        this.configGroup = configGroup;
        this.isSystem = isSystem;
        this.status = status;
        this.sortOrder = sortOrder;
        this.remark = remark;
    }

    /**
     * 启用配置
     */
    public void enable() {
        this.status = 1;
    }

    /**
     * 禁用配置
     */
    public void disable() {
        this.status = 0;
    }

    /**
     * 检查配置是否启用
     */
    public boolean isEnabled() {
        return this.status != null && this.status == 1;
    }

    /**
     * 检查是否为系统内置配置
     */
    public boolean isSystemConfig() {
        return this.isSystem != null && this.isSystem == 1;
    }

    /**
     * 检查配置键是否有效
     */
    public boolean isValidConfigKey() {
        return this.configKey != null && !this.configKey.trim().isEmpty();
    }

    /**
     * 检查配置名称是否有效
     */
    public boolean isValidConfigName() {
        return this.configName != null && !this.configName.trim().isEmpty();
    }

    /**
     * 获取配置类型名称
     */
    public String getConfigTypeName() {
        ConfigType configType = ConfigType.fromCode(this.configType);
        return configType != null ? configType.getName() : "未知";
    }

    /**
     * 获取字符串值
     */
    public String getStringValue() {
        return this.configValue;
    }

    /**
     * 获取整数值
     */
    public Integer getIntValue() {
        try {
            return this.configValue != null ? Integer.parseInt(this.configValue) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 获取长整数值
     */
    public Long getLongValue() {
        try {
            return this.configValue != null ? Long.parseLong(this.configValue) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 获取双精度值
     */
    public Double getDoubleValue() {
        try {
            return this.configValue != null ? Double.parseDouble(this.configValue) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 获取布尔值
     */
    public Boolean getBooleanValue() {
        if (this.configValue == null) {
            return null;
        }
        return "true".equalsIgnoreCase(this.configValue) || "1".equals(this.configValue);
    }

    /**
     * 设置字符串值
     */
    public void setStringValue(String value) {
        this.configValue = value;
    }

    /**
     * 设置整数值
     */
    public void setIntValue(Integer value) {
        this.configValue = value != null ? value.toString() : null;
    }

    /**
     * 设置长整数值
     */
    public void setLongValue(Long value) {
        this.configValue = value != null ? value.toString() : null;
    }

    /**
     * 设置双精度值
     */
    public void setDoubleValue(Double value) {
        this.configValue = value != null ? value.toString() : null;
    }

    /**
     * 设置布尔值
     */
    public void setBooleanValue(Boolean value) {
        this.configValue = value != null ? value.toString() : null;
    }
}
