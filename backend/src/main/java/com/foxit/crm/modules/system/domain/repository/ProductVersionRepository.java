package com.foxit.crm.modules.system.domain.repository;

import com.foxit.crm.modules.system.domain.model.aggregate.ProductVersion;
import com.foxit.crm.modules.system.domain.model.valueobject.VersionReleaseHistory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 产品版本仓储接口
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface ProductVersionRepository {

    /**
     * 保存产品版本
     */
    ProductVersion save(ProductVersion productVersion);

    /**
     * 根据ID查找产品版本
     */
    Optional<ProductVersion> findById(Long id);

    /**
     * 根据产品线ID和版本号查找产品版本
     */
    Optional<ProductVersion> findByProductLineIdAndVersionNumber(Long productLineId, String versionNumber);

    /**
     * 根据产品线ID获取当前版本
     */
    Optional<ProductVersion> getCurrentVersionByProductLineId(Long productLineId);

    /**
     * 根据产品线ID获取版本列表
     */
    List<ProductVersion> findByProductLineId(Long productLineId);

    /**
     * 分页查询产品版本
     */
    List<ProductVersion> findByPage(int page, int size, Long productLineId, String keyword, 
                                   ProductVersion.VersionStatus status, ProductVersion.VersionType versionType);

    /**
     * 统计产品版本数量
     */
    long count(Long productLineId, String keyword, ProductVersion.VersionStatus status, 
               ProductVersion.VersionType versionType);

    /**
     * 根据状态查找版本列表
     */
    List<ProductVersion> findByStatus(ProductVersion.VersionStatus status);

    /**
     * 根据产品线ID和状态查找版本列表
     */
    List<ProductVersion> findByProductLineIdAndStatus(Long productLineId, ProductVersion.VersionStatus status);

    /**
     * 删除产品版本
     */
    void deleteById(Long id);

    /**
     * 批量删除产品版本
     */
    void deleteByIds(List<Long> ids);

    /**
     * 检查版本号是否存在
     */
    boolean existsByProductLineIdAndVersionNumber(Long productLineId, String versionNumber);

    /**
     * 检查版本号是否存在（排除指定ID）
     */
    boolean existsByProductLineIdAndVersionNumberAndIdNot(Long productLineId, String versionNumber, Long excludeId);

    /**
     * 清除产品线的当前版本标记
     */
    void clearCurrentVersionByProductLineId(Long productLineId);

    /**
     * 设置当前版本
     */
    void setCurrentVersion(Long versionId);

    /**
     * 增加下载次数
     */
    void increaseDownloadCount(Long versionId, Integer count);

    /**
     * 获取版本统计信息
     */
    List<Object> getVersionStatsByProductLineId(Long productLineId);

    /**
     * 获取最新版本号
     */
    String getLatestVersionNumber(Long productLineId);

    /**
     * 保存版本发布历史
     */
    void saveReleaseHistory(VersionReleaseHistory history);

    /**
     * 根据版本ID获取发布历史
     */
    List<VersionReleaseHistory> findReleaseHistoryByVersionId(Long versionId);

    /**
     * 根据操作人获取发布历史
     */
    List<VersionReleaseHistory> findReleaseHistoryByActionBy(Long actionBy, Integer limit);

    /**
     * 根据时间范围获取发布历史
     */
    List<VersionReleaseHistory> findReleaseHistoryByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取最近的发布历史
     */
    List<VersionReleaseHistory> findRecentReleaseHistory(Integer limit);

    /**
     * 根据版本ID和日期获取下载统计
     */
    Long getTotalDownloadsByVersionId(Long versionId);

    /**
     * 根据版本ID获取独立下载次数
     */
    Long getTotalUniqueDownloadsByVersionId(Long versionId);

    /**
     * 获取热门版本下载排行
     */
    List<Object> getTopDownloadVersions(Integer days, Integer limit);

    /**
     * 记录下载统计
     */
    void recordDownloadStats(Long versionId, String platform, String region);
}
