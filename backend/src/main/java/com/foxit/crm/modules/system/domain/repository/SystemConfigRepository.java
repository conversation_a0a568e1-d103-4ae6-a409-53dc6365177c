package com.foxit.crm.modules.system.domain.repository;

import com.foxit.crm.modules.system.domain.model.aggregate.SystemConfig;

import java.util.List;
import java.util.Optional;

/**
 * 系统配置仓储接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface SystemConfigRepository {

    /**
     * 保存系统配置
     */
    SystemConfig save(SystemConfig systemConfig);

    /**
     * 根据ID查找系统配置
     */
    Optional<SystemConfig> findById(Long id);

    /**
     * 根据配置键查找系统配置
     */
    Optional<SystemConfig> findByConfigKey(String configKey);

    /**
     * 查找所有启用的系统配置
     */
    List<SystemConfig> findAllEnabled();

    /**
     * 根据配置分组查找系统配置
     */
    List<SystemConfig> findByConfigGroup(String configGroup);

    /**
     * 分页查询系统配置
     */
    List<SystemConfig> findByPage(int page, int size, String keyword, String configGroup, Integer status);

    /**
     * 统计系统配置总数
     */
    long count(String keyword, String configGroup, Integer status);

    /**
     * 删除系统配置
     */
    void deleteById(Long id);

    /**
     * 检查配置键是否存在
     */
    boolean existsByConfigKey(String configKey);

    /**
     * 检查配置键是否存在（排除指定ID）
     */
    boolean existsByConfigKeyAndIdNot(String configKey, Long id);

    /**
     * 根据状态查找系统配置
     */
    List<SystemConfig> findByStatus(Integer status);

    /**
     * 查找系统内置配置
     */
    List<SystemConfig> findSystemConfigs();

    /**
     * 查找用户自定义配置
     */
    List<SystemConfig> findUserConfigs();

    /**
     * 根据配置类型查找系统配置
     */
    List<SystemConfig> findByConfigType(Integer configType);

    /**
     * 批量更新配置状态
     */
    void updateStatusBatch(List<Long> ids, Integer status);

    /**
     * 获取所有配置分组
     */
    List<String> findAllConfigGroups();

    /**
     * 根据配置键列表查找系统配置
     */
    List<SystemConfig> findByConfigKeys(List<String> configKeys);
}
