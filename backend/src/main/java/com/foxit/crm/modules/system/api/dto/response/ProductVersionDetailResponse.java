package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品版本详情响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
public class ProductVersionDetailResponse {

    /**
     * 版本ID
     */
    private Long id;

    /**
     * 产品线ID
     */
    private Long productLineId;

    /**
     * 产品线名称
     */
    private String productLineName;

    /**
     * 版本号
     */
    private String versionNumber;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 版本描述
     */
    private String description;

    /**
     * 发布说明
     */
    private String releaseNotes;

    /**
     * 版本类型：1-主版本，2-次版本，3-修订版，4-预发布版
     */
    private Integer versionType;

    /**
     * 版本类型名称
     */
    private String versionTypeName;

    /**
     * 版本状态：1-开发中，2-测试中，3-预发布，4-已发布，5-已废弃
     */
    private Integer status;

    /**
     * 版本状态名称
     */
    private String statusName;

    /**
     * 是否当前版本
     */
    private Boolean isCurrent;

    /**
     * 发布时间
     */
    private LocalDateTime releaseDate;

    /**
     * 计划发布时间
     */
    private LocalDateTime plannedDate;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 格式化的文件大小
     */
    private String formattedFileSize;

    /**
     * 下载地址
     */
    private String downloadUrl;

    /**
     * 下载次数
     */
    private Integer downloadCount;

    /**
     * 支持平台列表
     */
    private List<String> platforms;

    /**
     * 新功能列表
     */
    private List<String> features;

    /**
     * 修复问题列表
     */
    private List<String> bugFixes;

    /**
     * 破坏性变更列表
     */
    private List<String> breakingChanges;

    /**
     * 依赖信息
     */
    private List<String> dependencies;

    /**
     * 系统要求
     */
    private List<String> systemRequirements;

    /**
     * MD5校验值
     */
    private String checksumMd5;

    /**
     * SHA256校验值
     */
    private String checksumSha256;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人姓名
     */
    private String createdByName;

    /**
     * 审批人ID
     */
    private Long approvedBy;

    /**
     * 审批人姓名
     */
    private String approvedByName;

    /**
     * 审批时间
     */
    private LocalDateTime approvedAt;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 版本号（乐观锁）
     */
    private Integer version;

    /**
     * 是否可以删除
     */
    private Boolean canBeDeleted;

    /**
     * 是否可以发布
     */
    private Boolean canBeReleased;

    /**
     * 是否可以设为当前版本
     */
    private Boolean canBeSetAsCurrent;

    /**
     * 是否可以废弃
     */
    private Boolean canBeDeprecated;
}
