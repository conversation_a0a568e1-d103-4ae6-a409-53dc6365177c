package com.foxit.crm.modules.behavioranalysis.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.common.util.SecurityUtils;
import com.foxit.crm.modules.behavioranalysis.application.dto.EventAnalysisRequest;
import com.foxit.crm.modules.behavioranalysis.application.dto.EventAnalysisResponse;
import com.foxit.crm.modules.behavioranalysis.application.service.EventAnalysisService;
import com.foxit.crm.modules.behavioranalysis.domain.repository.EventAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 事件分析控制器
 * 提供事件分析相关的REST API接口
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@RestController
@RequestMapping("/behavior/event")
@RequiredArgsConstructor
@Tag(name = "事件分析", description = "事件分析相关接口")
public class EventAnalysisController {

    private final EventAnalysisService eventAnalysisService;

    @Operation(summary = "获取事件趋势分析", description = "获取指定时间范围内的事件趋势分析数据")
    @GetMapping("/trends")
    @PreAuthorize("hasPermission('behavior:event', 'READ')")
    public Result<EventAnalysisResponse> getEventTrends(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "事件ID列表") @RequestParam List<String> eventIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds,
            @Parameter(description = "是否包含对比") @RequestParam(required = false, defaultValue = "false") Boolean includeComparison) {

        try {
            EventAnalysisRequest request = EventAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .eventIds(eventIds)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .includeComparison(includeComparison)
                    .analysisType("EVENT_TREND")
                    .build();

            EventAnalysisResponse response = eventAnalysisService.getEventTrendAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取事件趋势分析失败", e);
            return Result.error("获取事件趋势分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取事件漏斗分析", description = "获取指定事件序列的漏斗转化分析数据")
    @GetMapping("/funnel")
    @PreAuthorize("hasPermission('behavior:event', 'READ')")
    public Result<EventAnalysisResponse> getEventFunnel(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "事件ID列表（按漏斗顺序）") @RequestParam List<String> eventIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            EventAnalysisRequest request = EventAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .eventIds(eventIds)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("EVENT_FUNNEL")
                    .includeDetails(true)
                    .build();

            EventAnalysisResponse response = eventAnalysisService.getEventFunnelAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取事件漏斗分析失败", e);
            return Result.error("获取事件漏斗分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取事件路径分析", description = "获取用户在事件间的行为路径分析数据")
    @GetMapping("/path")
    @PreAuthorize("hasPermission('behavior:event', 'READ')")
    public Result<EventAnalysisResponse> getEventPath(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "起始事件ID列表") @RequestParam List<String> startEventIds,
            @Parameter(description = "结束事件ID列表") @RequestParam(required = false) List<String> endEventIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            EventAnalysisRequest request = EventAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .startEventIds(startEventIds)
                    .endEventIds(endEventIds)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("EVENT_PATH")
                    .includeDetails(true)
                    .build();

            EventAnalysisResponse response = eventAnalysisService.getEventPathAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取事件路径分析失败", e);
            return Result.error("获取事件路径分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取事件对比分析", description = "获取多个事件的对比分析数据")
    @GetMapping("/comparison")
    @PreAuthorize("hasPermission('behavior:event', 'READ')")
    public Result<EventAnalysisResponse> getEventComparison(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "事件ID列表") @RequestParam List<String> eventIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            EventAnalysisRequest request = EventAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .eventIds(eventIds)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("EVENT_COMPARISON")
                    .includeDetails(true)
                    .build();

            EventAnalysisResponse response = eventAnalysisService.getEventComparisonAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取事件对比分析失败", e);
            return Result.error("获取事件对比分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取事件属性分析", description = "获取指定事件的属性分布分析数据")
    @GetMapping("/property")
    @PreAuthorize("hasPermission('behavior:event', 'READ')")
    public Result<EventAnalysisResponse> getEventProperty(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "事件ID") @RequestParam String eventId,
            @Parameter(description = "属性名称列表") @RequestParam List<String> propertyNames,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            EventAnalysisRequest request = EventAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .eventId(eventId)
                    .propertyNames(propertyNames)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("EVENT_PROPERTY")
                    .includeDetails(true)
                    .build();

            EventAnalysisResponse response = eventAnalysisService.getEventPropertyAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取事件属性分析失败", e);
            return Result.error("获取事件属性分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取事件列表", description = "获取可用的事件列表")
    @GetMapping("/list")
    @PreAuthorize("hasPermission('behavior:event', 'READ')")
    public Result<List<EventAnalysisRepository.EventInfo>> getEventList(
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            String dataScope = SecurityUtils.getCurrentUserDataScope();
            List<EventAnalysisRepository.EventInfo> eventList = eventAnalysisService.getEventList(productLineIds, dataScope);
            return Result.success(eventList);

        } catch (Exception e) {
            log.error("获取事件列表失败", e);
            return Result.error("获取事件列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取实时事件统计", description = "获取实时事件统计数据")
    @GetMapping("/realtime-stats")
    @PreAuthorize("hasPermission('behavior:event', 'READ')")
    public Result<EventAnalysisRepository.EventRealTimeStats> getRealTimeStats(
            @Parameter(description = "事件ID列表") @RequestParam(required = false) List<String> eventIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            String dataScope = SecurityUtils.getCurrentUserDataScope();
            EventAnalysisRepository.EventRealTimeStats stats = eventAnalysisService.getRealTimeEventStats(eventIds, productLineIds, dataScope);
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取实时事件统计失败", e);
            return Result.error("获取实时事件统计失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取事件分析统计摘要", description = "获取事件分析的统计摘要数据")
    @GetMapping("/summary")
    @PreAuthorize("hasPermission('behavior:event', 'READ')")
    public Result<EventAnalysisRepository.EventAnalysisSummary> getSummary(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        try {
            EventAnalysisRequest request = EventAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            EventAnalysisRepository.EventAnalysisSummary summary = eventAnalysisService.getEventAnalysisSummary(request);
            return Result.success(summary);

        } catch (Exception e) {
            log.error("获取事件分析统计摘要失败", e);
            return Result.error("获取事件分析统计摘要失败: " + e.getMessage());
        }
    }

    // ==================== 公共接口（无需认证） ====================

    /**
     * 获取公共事件趋势分析 - 无需认证
     */
    @Operation(summary = "获取公共事件趋势分析", description = "获取指定时间范围内的事件趋势分析数据（无需认证）")
    @GetMapping("/public/trends")
    public Result<EventAnalysisResponse> getPublicEventTrends(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "事件ID列表") @RequestParam List<String> eventIds) {

        try {
            EventAnalysisRequest request = EventAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .eventIds(eventIds)
                    .analysisType("EVENT_TREND")
                    .includeComparison(false)
                    .build();

            EventAnalysisResponse response = eventAnalysisService.getEventTrendAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取公共事件趋势分析失败", e);
            return Result.error("获取公共事件趋势分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共事件列表 - 无需认证
     */
    @Operation(summary = "获取公共事件列表", description = "获取可用的事件列表（无需认证）")
    @GetMapping("/public/list")
    public Result<List<EventAnalysisRepository.EventInfo>> getPublicEventList() {
        try {
            List<EventAnalysisRepository.EventInfo> eventList = eventAnalysisService.getEventList(null, "PUBLIC");
            return Result.success(eventList);

        } catch (Exception e) {
            log.error("获取公共事件列表失败", e);
            return Result.error("获取公共事件列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共事件漏斗分析 - 无需认证
     */
    @Operation(summary = "获取公共事件漏斗分析", description = "获取事件漏斗分析数据（无需认证）")
    @GetMapping("/public/funnel")
    public Result<EventAnalysisResponse> getPublicEventFunnel(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "事件ID列表") @RequestParam List<String> eventIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            EventAnalysisRequest request = EventAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .eventIds(eventIds)
                    .productLineIds(productLineIds)
                    .analysisType("EVENT_FUNNEL")
                    .includeComparison(false)
                    .build();

            EventAnalysisResponse response = eventAnalysisService.getEventFunnelAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取公共事件漏斗分析失败", e);
            return Result.error("获取公共事件漏斗分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共事件路径分析 - 无需认证
     */
    @Operation(summary = "获取公共事件路径分析", description = "获取事件路径分析数据（无需认证）")
    @GetMapping("/public/path")
    public Result<EventAnalysisResponse> getPublicEventPath(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "起始事件ID列表") @RequestParam List<String> startEventIds,
            @Parameter(description = "结束事件ID列表") @RequestParam(required = false) List<String> endEventIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            EventAnalysisRequest request = EventAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .eventIds(startEventIds)
                    .productLineIds(productLineIds)
                    .analysisType("EVENT_PATH")
                    .includeComparison(false)
                    .build();

            EventAnalysisResponse response = eventAnalysisService.getEventPathAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取公共事件路径分析失败", e);
            return Result.error("获取公共事件路径分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共事件对比分析 - 无需认证
     */
    @Operation(summary = "获取公共事件对比分析", description = "获取事件对比分析数据（无需认证）")
    @GetMapping("/public/comparison")
    public Result<EventAnalysisResponse> getPublicEventComparison(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "事件ID列表") @RequestParam List<String> eventIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            EventAnalysisRequest request = EventAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .eventIds(eventIds)
                    .productLineIds(productLineIds)
                    .analysisType("EVENT_COMPARISON")
                    .includeComparison(true)
                    .build();

            EventAnalysisResponse response = eventAnalysisService.getEventComparisonAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取公共事件对比分析失败", e);
            return Result.error("获取公共事件对比分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共事件属性分析 - 无需认证
     */
    @Operation(summary = "获取公共事件属性分析", description = "获取事件属性分析数据（无需认证）")
    @GetMapping("/public/property")
    public Result<EventAnalysisResponse> getPublicEventProperty(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "事件ID") @RequestParam String eventId,
            @Parameter(description = "属性名称列表") @RequestParam List<String> propertyNames,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            EventAnalysisRequest request = EventAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .eventIds(List.of(eventId))
                    .productLineIds(productLineIds)
                    .analysisType("EVENT_PROPERTY")
                    .includeComparison(false)
                    .build();

            EventAnalysisResponse response = eventAnalysisService.getEventPropertyAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取公共事件属性分析失败", e);
            return Result.error("获取公共事件属性分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共实时事件统计 - 无需认证
     */
    @Operation(summary = "获取公共实时事件统计", description = "获取实时事件统计数据（无需认证）")
    @GetMapping("/public/realtime-stats")
    public Result<EventAnalysisRepository.EventRealTimeStats> getPublicRealTimeStats(
            @Parameter(description = "事件ID列表") @RequestParam(required = false) List<String> eventIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            log.info("调用公共实时事件统计接口: eventIds={}, productLineIds={}", eventIds, productLineIds);
            EventAnalysisRepository.EventRealTimeStats stats = eventAnalysisService.getRealTimeEventStats(eventIds, productLineIds, "PUBLIC");
            log.info("返回实时统计结果: totalEvents={}, uniqueUsers={}", stats.totalEvents(), stats.uniqueUsers());
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取公共实时事件统计失败", e);
            return Result.error("获取公共实时事件统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共事件分析统计摘要 - 无需认证
     */
    @Operation(summary = "获取公共事件分析统计摘要", description = "获取事件分析统计摘要（无需认证）")
    @GetMapping("/public/summary")
    public Result<EventAnalysisRepository.EventAnalysisSummary> getPublicEventSummary(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        try {
            EventAnalysisRequest request = EventAnalysisRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .dataScope("PUBLIC")
                    .build();

            EventAnalysisRepository.EventAnalysisSummary summary = eventAnalysisService.getEventAnalysisSummary(request);
            return Result.success(summary);

        } catch (Exception e) {
            log.error("获取公共事件分析统计摘要失败", e);
            return Result.error("获取公共事件分析统计摘要失败: " + e.getMessage());
        }
    }
}
