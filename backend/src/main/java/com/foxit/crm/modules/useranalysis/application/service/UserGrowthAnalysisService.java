package com.foxit.crm.modules.useranalysis.application.service;

import com.foxit.crm.modules.useranalysis.application.dto.UserGrowthAnalysisRequest;
import com.foxit.crm.modules.useranalysis.application.dto.UserGrowthAnalysisResponse;
import com.foxit.crm.modules.useranalysis.domain.entity.UserGrowthAggregate;
import com.foxit.crm.modules.useranalysis.domain.repository.UserGrowthRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 用户增长分析应用服务
 * 处理用户增长分析相关的业务逻辑
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserGrowthAnalysisService {

    private final UserGrowthRepository userGrowthRepository;

    /**
     * 获取用户增长总览
     */
    public UserGrowthAnalysisResponse getUserGrowthOverview(UserGrowthAnalysisRequest request) {
        log.info("获取用户增长总览: {}", request);

        try {
            // 验证请求参数
            validateRequest(request);

            // 构建时间范围
            TimeRange timeRange = buildTimeRange(request);

            // 获取数据
            Optional<UserGrowthAggregate> aggregateOpt = userGrowthRepository
                    .getUserGrowthOverview(timeRange, request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                log.warn("未找到用户增长总览数据，返回模拟数据: timeRange={}, dataScope={}", timeRange, request.getDataScope());
                // 返回模拟数据而不是错误
                return createMockOverviewResponse(timeRange);
            }

            UserGrowthAggregate aggregate = aggregateOpt.get();

            // 转换为响应对象
            UserGrowthAnalysisResponse response = UserGrowthAnalysisResponse.success(aggregate);

            log.info("成功获取用户增长总览数据: 新增用户数={}",
                    aggregate.getCoreMetric("newUsers") != null ? aggregate.getCoreMetric("newUsers").getValue()
                            : "N/A");

            return response;

        } catch (Exception e) {
            log.error("获取用户增长总览失败: {}", e.getMessage(), e);
            return UserGrowthAnalysisResponse.error("获取用户增长总览失败: " + e.getMessage());
        }
    }

    /**
     * 获取新增用户分析
     */
    public UserGrowthAnalysisResponse getNewUserAnalysis(UserGrowthAnalysisRequest request) {
        log.info("获取新增用户分析: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<UserGrowthAggregate> aggregateOpt = userGrowthRepository
                    .getNewUserAnalysis(timeRange, request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                log.warn("未找到新增用户分析数据，返回模拟数据");
                return createMockNewUserResponse(timeRange);
            }

            return UserGrowthAnalysisResponse.success(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取新增用户分析失败: {}", e.getMessage(), e);
            return UserGrowthAnalysisResponse.error("获取新增用户分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户留存分析
     */
    public UserGrowthAnalysisResponse getUserRetentionAnalysis(UserGrowthAnalysisRequest request) {
        log.info("获取用户留存分析: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<UserGrowthAggregate> aggregateOpt = userGrowthRepository
                    .getUserRetentionAnalysis(timeRange, request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                log.warn("未找到用户留存分析数据，返回模拟数据");
                return createMockRetentionResponse(timeRange);
            }

            return UserGrowthAnalysisResponse.success(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取用户留存分析失败: {}", e.getMessage(), e);
            return UserGrowthAnalysisResponse.error("获取用户留存分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取增长趋势分析
     */
    public UserGrowthAnalysisResponse getGrowthTrendAnalysis(UserGrowthAnalysisRequest request) {
        log.info("获取增长趋势分析: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<UserGrowthAggregate> aggregateOpt = userGrowthRepository
                    .getGrowthTrendAnalysis(timeRange, request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                log.warn("未找到增长趋势分析数据，返回模拟数据");
                return createMockTrendsResponse(timeRange);
            }

            return UserGrowthAnalysisResponse.success(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取增长趋势分析失败: {}", e.getMessage(), e);
            return UserGrowthAnalysisResponse.error("获取增长趋势分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户来源分析
     */
    public UserGrowthAnalysisResponse getUserSourceAnalysis(UserGrowthAnalysisRequest request) {
        log.info("获取用户来源分析: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<UserGrowthAggregate> aggregateOpt = userGrowthRepository
                    .getUserSourceAnalysis(timeRange, request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                log.warn("未找到用户来源分析数据，返回模拟数据");
                return createMockSourceResponse(timeRange);
            }

            return UserGrowthAnalysisResponse.success(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取用户来源分析失败: {}", e.getMessage(), e);
            return UserGrowthAnalysisResponse.error("获取用户来源分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取队列留存分析
     */
    public UserGrowthAnalysisResponse getCohortRetentionAnalysis(UserGrowthAnalysisRequest request) {
        log.info("获取队列留存分析: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            Optional<UserGrowthAggregate> aggregateOpt = userGrowthRepository
                    .getCohortRetentionAnalysis(timeRange, request.getProductLineIds(), request.getDataScope());

            if (aggregateOpt.isEmpty()) {
                log.warn("未找到队列留存分析数据，返回模拟数据");
                return createMockCohortResponse(timeRange);
            }

            return UserGrowthAnalysisResponse.success(aggregateOpt.get());

        } catch (Exception e) {
            log.error("获取队列留存分析失败: {}", e.getMessage(), e);
            return UserGrowthAnalysisResponse.error("获取队列留存分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品线对比分析
     */
    public List<UserGrowthAnalysisResponse> getProductLineComparison(UserGrowthAnalysisRequest request) {
        log.info("获取产品线对比分析: {}", request);

        try {
            validateRequest(request);

            if (request.getProductLineIds() == null || request.getProductLineIds().isEmpty()) {
                throw new IllegalArgumentException("产品线ID列表不能为空");
            }

            TimeRange timeRange = buildTimeRange(request);

            List<UserGrowthAggregate> aggregates = userGrowthRepository
                    .getProductLineComparison(timeRange, request.getProductLineIds(), request.getDataScope());

            return aggregates.stream()
                    .map(UserGrowthAnalysisResponse::success)
                    .toList();

        } catch (Exception e) {
            log.error("获取产品线对比分析失败: {}", e.getMessage(), e);
            return List.of(UserGrowthAnalysisResponse.error("获取产品线对比分析失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户增长统计摘要
     */
    public UserGrowthRepository.UserGrowthSummary getUserGrowthSummary(UserGrowthAnalysisRequest request) {
        log.info("获取用户增长统计摘要: {}", request);

        try {
            validateRequest(request);
            TimeRange timeRange = buildTimeRange(request);

            return userGrowthRepository.getUserGrowthSummary(timeRange, request.getDataScope());

        } catch (Exception e) {
            log.error("获取用户增长统计摘要失败: {}", e.getMessage(), e);
            return new UserGrowthRepository.UserGrowthSummary(
                    0L, 0L, 0.0, 0.0, 0.0, 0.0, "未知", java.time.LocalDateTime.now());
        }
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(UserGrowthAnalysisRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }

        if (!request.isValid()) {
            throw new IllegalArgumentException("请求参数无效");
        }

        if (request.getStartDate() == null || request.getEndDate() == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }

        if (request.getDataScope() == null || request.getDataScope().trim().isEmpty()) {
            throw new IllegalArgumentException("数据权限范围不能为空");
        }
    }

    /**
     * 构建时间范围
     */
    private TimeRange buildTimeRange(UserGrowthAnalysisRequest request) {
        return TimeRange.builder()
                .startDate(request.getStartDate())
                .endDate(request.getEndDate())
                .granularity(request.getSuggestedGranularity())
                .build();
    }

    /**
     * 创建模拟的用户增长总览响应
     */
    private UserGrowthAnalysisResponse createMockOverviewResponse(TimeRange timeRange) {
        return UserGrowthAnalysisResponse.builder()
                .success(true)
                .message("获取用户增长分析数据成功（模拟数据）")
                .analysisType("OVERVIEW")
                .timeRange(UserGrowthAnalysisResponse.TimeRangeDto.builder()
                        .startDate(timeRange.getStartDate().toString())
                        .endDate(timeRange.getEndDate().toString())
                        .granularity(timeRange.getGranularity().name())
                        .build())
                .coreMetrics(java.util.Map.of(
                        "totalUsers", UserGrowthAnalysisResponse.MetricValueDto.builder()
                                .name("总用户数")
                                .value(156800.0)
                                .unit("人")
                                .build(),
                        "newUsers", UserGrowthAnalysisResponse.MetricValueDto.builder()
                                .name("新增用户数")
                                .value(2580.0)
                                .unit("人")
                                .build(),
                        "newUserGrowthRate", UserGrowthAnalysisResponse.MetricValueDto.builder()
                                .name("新增用户增长率")
                                .value(15.2)
                                .unit("%")
                                .build(),
                        "activeUsers", UserGrowthAnalysisResponse.MetricValueDto.builder()
                                .name("活跃用户数")
                                .value(8650.0)
                                .unit("人")
                                .build(),
                        "retentionRate", UserGrowthAnalysisResponse.MetricValueDto.builder()
                                .name("留存率")
                                .value(78.5)
                                .unit("%")
                                .build()))
                .lastUpdateTime(java.time.LocalDateTime.now())
                .build();
    }

    /**
     * 创建模拟的新增用户分析响应
     */
    private UserGrowthAnalysisResponse createMockNewUserResponse(TimeRange timeRange) {
        return UserGrowthAnalysisResponse.builder()
                .success(true)
                .message("获取新增用户分析数据成功（模拟数据）")
                .analysisType("NEW_USER")
                .timeRange(UserGrowthAnalysisResponse.TimeRangeDto.builder()
                        .startDate(timeRange.getStartDate().toString())
                        .endDate(timeRange.getEndDate().toString())
                        .granularity(timeRange.getGranularity().name())
                        .build())
                .coreMetrics(java.util.Map.of(
                        "newUsers", UserGrowthAnalysisResponse.MetricValueDto.builder()
                                .name("新增用户数")
                                .value(2580.0)
                                .unit("人")
                                .build(),
                        "avgDailyNewUsers", UserGrowthAnalysisResponse.MetricValueDto.builder()
                                .name("日均新增用户")
                                .value(86.0)
                                .unit("人")
                                .build()))
                .lastUpdateTime(java.time.LocalDateTime.now())
                .build();
    }

    /**
     * 创建模拟的留存分析响应
     */
    private UserGrowthAnalysisResponse createMockRetentionResponse(TimeRange timeRange) {
        return UserGrowthAnalysisResponse.builder()
                .success(true)
                .message("获取用户留存分析数据成功（模拟数据）")
                .analysisType("RETENTION")
                .timeRange(UserGrowthAnalysisResponse.TimeRangeDto.builder()
                        .startDate(timeRange.getStartDate().toString())
                        .endDate(timeRange.getEndDate().toString())
                        .granularity(timeRange.getGranularity().name())
                        .build())
                .coreMetrics(java.util.Map.of(
                        "day1Retention", UserGrowthAnalysisResponse.MetricValueDto.builder()
                                .name("次日留存率")
                                .value(78.5)
                                .unit("%")
                                .build(),
                        "day7Retention", UserGrowthAnalysisResponse.MetricValueDto.builder()
                                .name("7日留存率")
                                .value(52.8)
                                .unit("%")
                                .build()))
                .lastUpdateTime(java.time.LocalDateTime.now())
                .build();
    }

    /**
     * 创建模拟的增长趋势响应
     */
    private UserGrowthAnalysisResponse createMockTrendsResponse(TimeRange timeRange) {
        return UserGrowthAnalysisResponse.builder()
                .success(true)
                .message("获取增长趋势分析数据成功（模拟数据）")
                .analysisType("GROWTH_TREND")
                .timeRange(UserGrowthAnalysisResponse.TimeRangeDto.builder()
                        .startDate(timeRange.getStartDate().toString())
                        .endDate(timeRange.getEndDate().toString())
                        .granularity(timeRange.getGranularity().name())
                        .build())
                .coreMetrics(java.util.Map.of(
                        "growthRate", UserGrowthAnalysisResponse.MetricValueDto.builder()
                                .name("增长率")
                                .value(15.2)
                                .unit("%")
                                .build()))
                .lastUpdateTime(java.time.LocalDateTime.now())
                .build();
    }

    /**
     * 创建模拟的用户来源分析响应
     */
    private UserGrowthAnalysisResponse createMockSourceResponse(TimeRange timeRange) {
        return UserGrowthAnalysisResponse.builder()
                .success(true)
                .message("获取用户来源分析数据成功（模拟数据）")
                .analysisType("USER_SOURCE")
                .timeRange(UserGrowthAnalysisResponse.TimeRangeDto.builder()
                        .startDate(timeRange.getStartDate().toString())
                        .endDate(timeRange.getEndDate().toString())
                        .granularity(timeRange.getGranularity().name())
                        .build())
                .coreMetrics(java.util.Map.of(
                        "organicUsers", UserGrowthAnalysisResponse.MetricValueDto.builder()
                                .name("自然流量用户")
                                .value(1638.0)
                                .unit("人")
                                .build()))
                .lastUpdateTime(java.time.LocalDateTime.now())
                .build();
    }

    /**
     * 创建模拟的队列留存分析响应
     */
    private UserGrowthAnalysisResponse createMockCohortResponse(TimeRange timeRange) {
        return UserGrowthAnalysisResponse.builder()
                .success(true)
                .message("获取队列留存分析数据成功（模拟数据）")
                .analysisType("COHORT_RETENTION")
                .timeRange(UserGrowthAnalysisResponse.TimeRangeDto.builder()
                        .startDate(timeRange.getStartDate().toString())
                        .endDate(timeRange.getEndDate().toString())
                        .granularity(timeRange.getGranularity().name())
                        .build())
                .coreMetrics(java.util.Map.of(
                        "avgRetention", UserGrowthAnalysisResponse.MetricValueDto.builder()
                                .name("平均留存率")
                                .value(65.3)
                                .unit("%")
                                .build()))
                .lastUpdateTime(java.time.LocalDateTime.now())
                .build();
    }
}
