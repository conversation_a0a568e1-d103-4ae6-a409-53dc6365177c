package com.foxit.crm.modules.system.api.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 更新权限请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
public class PermissionUpdateRequest {

    /**
     * 父权限ID
     */
    @NotNull(message = "父权限ID不能为空")
    private Long parentId;

    /**
     * 权限名称
     */
    @NotBlank(message = "权限名称不能为空")
    @Size(max = 100, message = "权限名称长度不能超过100个字符")
    private String permissionName;

    /**
     * 权限编码
     */
    @NotBlank(message = "权限编码不能为空")
    @Size(max = 100, message = "权限编码长度不能超过100个字符")
    @Pattern(regexp = "^[a-z][a-z0-9_:]*$", message = "权限编码必须以小写字母开头，只能包含小写字母、数字、下划线和冒号")
    private String permissionCode;

    /**
     * 权限类型：1-菜单，2-按钮，3-接口
     */
    @NotNull(message = "权限类型不能为空")
    private Integer permissionType;

    /**
     * 路由路径
     */
    @Size(max = 255, message = "路由路径长度不能超过255个字符")
    private String path;

    /**
     * 组件路径
     */
    @Size(max = 255, message = "组件路径长度不能超过255个字符")
    private String component;

    /**
     * 图标
     */
    @Size(max = 100, message = "图标长度不能超过100个字符")
    private String icon;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 状态：0-禁用，1-启用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 版本号（乐观锁）
     */
    @NotNull(message = "版本号不能为空")
    private Integer version;
}
