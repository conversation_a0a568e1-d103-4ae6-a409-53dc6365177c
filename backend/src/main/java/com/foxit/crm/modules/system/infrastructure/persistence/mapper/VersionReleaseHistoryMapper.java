package com.foxit.crm.modules.system.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.VersionReleaseHistoryPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 版本发布历史Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Mapper
public interface VersionReleaseHistoryMapper extends BaseMapper<VersionReleaseHistoryPO> {

    /**
     * 根据版本ID获取发布历史
     */
    @Select("SELECT * FROM version_release_history WHERE version_id = #{versionId} AND deleted = 0 ORDER BY action_time DESC")
    List<VersionReleaseHistoryPO> findByVersionId(@Param("versionId") Long versionId);

    /**
     * 根据操作人获取发布历史
     */
    @Select("SELECT * FROM version_release_history WHERE action_by = #{actionBy} AND deleted = 0 ORDER BY action_time DESC LIMIT #{limit}")
    List<VersionReleaseHistoryPO> findByActionBy(@Param("actionBy") Long actionBy, @Param("limit") Integer limit);

    /**
     * 根据时间范围获取发布历史
     */
    @Select("SELECT * FROM version_release_history WHERE action_time BETWEEN #{startTime} AND #{endTime} AND deleted = 0 ORDER BY action_time DESC")
    List<VersionReleaseHistoryPO> findByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据操作类型获取发布历史
     */
    @Select("SELECT * FROM version_release_history WHERE action_type = #{actionType} AND deleted = 0 ORDER BY action_time DESC LIMIT #{limit}")
    List<VersionReleaseHistoryPO> findByActionType(@Param("actionType") Integer actionType, @Param("limit") Integer limit);

    /**
     * 获取最近的发布历史
     */
    @Select("SELECT * FROM version_release_history WHERE deleted = 0 ORDER BY action_time DESC LIMIT #{limit}")
    List<VersionReleaseHistoryPO> findRecentHistory(@Param("limit") Integer limit);
}
