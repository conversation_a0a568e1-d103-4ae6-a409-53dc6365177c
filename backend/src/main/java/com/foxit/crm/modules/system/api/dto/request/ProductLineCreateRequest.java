package com.foxit.crm.modules.system.api.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 创建产品线请求DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
public class ProductLineCreateRequest {

    /**
     * 产品线编码
     */
    @NotBlank(message = "产品线编码不能为空")
    @Size(max = 50, message = "产品线编码长度不能超过50个字符")
    @Pattern(regexp = "^[A-Z][A-Z0-9_]*$", message = "产品线编码必须以大写字母开头，只能包含大写字母、数字和下划线")
    private String code;

    /**
     * 产品线名称
     */
    @NotBlank(message = "产品线名称不能为空")
    @Size(max = 100, message = "产品线名称长度不能超过100个字符")
    private String name;

    /**
     * 产品线描述
     */
    @Size(max = 500, message = "产品线描述长度不能超过500个字符")
    private String description;

    /**
     * 产品线类型：1-阅读器，2-编辑器，3-云服务，4-工具类，5-内容平台
     */
    @NotNull(message = "产品线类型不能为空")
    private Integer type;

    /**
     * 产品线状态：0-禁用，1-启用
     */
    @NotNull(message = "产品线状态不能为空")
    private Integer status;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 产品线图标
     */
    @Size(max = 255, message = "产品线图标长度不能超过255个字符")
    private String icon;

    /**
     * 产品线颜色
     */
    @Size(max = 20, message = "产品线颜色长度不能超过20个字符")
    @Pattern(regexp = "^#[0-9A-Fa-f]{6}$", message = "产品线颜色必须是有效的十六进制颜色值")
    private String color;

    /**
     * 负责人ID
     */
    private Long ownerId;

    /**
     * 负责人姓名
     */
    @Size(max = 50, message = "负责人姓名长度不能超过50个字符")
    private String ownerName;

    /**
     * 数据源配置
     */
    private String dataSourceConfig;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
