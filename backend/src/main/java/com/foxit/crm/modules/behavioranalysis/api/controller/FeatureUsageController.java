package com.foxit.crm.modules.behavioranalysis.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.common.util.SecurityUtils;
import com.foxit.crm.modules.behavioranalysis.application.dto.FeatureUsageRequest;
import com.foxit.crm.modules.behavioranalysis.application.dto.FeatureUsageResponse;
import com.foxit.crm.modules.behavioranalysis.application.service.FeatureUsageService;
import com.foxit.crm.modules.behavioranalysis.domain.repository.FeatureUsageRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 功能使用分析控制器
 * 提供功能使用分析相关的REST API接口
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@RestController
@RequestMapping("/behavior/feature")
@RequiredArgsConstructor
@Tag(name = "功能使用分析", description = "功能使用分析相关接口")
public class FeatureUsageController {

    private final FeatureUsageService featureUsageService;

    @Operation(summary = "获取功能使用统计", description = "获取指定时间范围内的功能使用统计数据")
    @GetMapping("/usage-statistics")
    @PreAuthorize("hasPermission('behavior:feature', 'READ')")
    public Result<FeatureUsageResponse> getFeatureUsageStatistics(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "时间粒度") @RequestParam(required = false, defaultValue = "DAY") TimeRange.TimeGranularity granularity,
            @Parameter(description = "功能ID列表") @RequestParam List<String> featureIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds,
            @Parameter(description = "是否包含对比") @RequestParam(required = false, defaultValue = "false") Boolean includeComparison) {

        try {
            FeatureUsageRequest request = FeatureUsageRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(granularity)
                    .featureIds(featureIds)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .includeComparison(includeComparison)
                    .analysisType("USAGE_STATISTICS")
                    .build();

            FeatureUsageResponse response = featureUsageService.getFeatureUsageStatistics(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取功能使用统计失败", e);
            return Result.error("获取功能使用统计失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取功能热度分析", description = "获取功能的热度分析数据，包括热度排名和受欢迎程度")
    @GetMapping("/heat-analysis")
    @PreAuthorize("hasPermission('behavior:feature', 'READ')")
    public Result<FeatureUsageResponse> getFeatureHeatAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "功能ID列表") @RequestParam(required = false) List<String> featureIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            FeatureUsageRequest request = FeatureUsageRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .featureIds(featureIds)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("HEAT_ANALYSIS")
                    .includeDetails(true)
                    .build();

            FeatureUsageResponse response = featureUsageService.getFeatureHeatAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取功能热度分析失败", e);
            return Result.error("获取功能热度分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取功能路径分析", description = "获取用户在功能间的使用路径分析数据")
    @GetMapping("/path-analysis")
    @PreAuthorize("hasPermission('behavior:feature', 'READ')")
    public Result<FeatureUsageResponse> getFeaturePathAnalysis(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "起始功能ID列表") @RequestParam List<String> startFeatureIds,
            @Parameter(description = "结束功能ID列表") @RequestParam(required = false) List<String> endFeatureIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            FeatureUsageRequest request = FeatureUsageRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .startFeatureIds(startFeatureIds)
                    .endFeatureIds(endFeatureIds)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("PATH_ANALYSIS")
                    .includeDetails(true)
                    .build();

            FeatureUsageResponse response = featureUsageService.getFeaturePathAnalysis(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取功能路径分析失败", e);
            return Result.error("获取功能路径分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取功能价值贡献分析", description = "获取功能对业务目标的价值贡献分析数据")
    @GetMapping("/value-contribution")
    @PreAuthorize("hasPermission('behavior:feature', 'READ')")
    public Result<FeatureUsageResponse> getFeatureValueContribution(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "功能ID列表") @RequestParam List<String> featureIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            FeatureUsageRequest request = FeatureUsageRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .featureIds(featureIds)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("VALUE_CONTRIBUTION")
                    .includeDetails(true)
                    .build();

            FeatureUsageResponse response = featureUsageService.getFeatureValueContribution(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取功能价值贡献分析失败", e);
            return Result.error("获取功能价值贡献分析失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取功能满意度评估", description = "获取基于使用行为的功能满意度评估数据")
    @GetMapping("/satisfaction-evaluation")
    @PreAuthorize("hasPermission('behavior:feature', 'READ')")
    public Result<FeatureUsageResponse> getFeatureSatisfactionEvaluation(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "功能ID列表") @RequestParam List<String> featureIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            FeatureUsageRequest request = FeatureUsageRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .featureIds(featureIds)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .analysisType("SATISFACTION_EVALUATION")
                    .includeDetails(true)
                    .build();

            FeatureUsageResponse response = featureUsageService.getFeatureSatisfactionEvaluation(request);
            return Result.success(response);

        } catch (Exception e) {
            log.error("获取功能满意度评估失败", e);
            return Result.error("获取功能满意度评估失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取功能列表", description = "获取可用的功能列表")
    @GetMapping("/list")
    @PreAuthorize("hasPermission('behavior:feature', 'READ')")
    public Result<List<FeatureUsageRepository.FeatureInfo>> getFeatureList(
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            String dataScope = SecurityUtils.getCurrentUserDataScope();
            List<FeatureUsageRepository.FeatureInfo> featureList = featureUsageService.getFeatureList(productLineIds, dataScope);
            return Result.success(featureList);

        } catch (Exception e) {
            log.error("获取功能列表失败", e);
            return Result.error("获取功能列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取实时功能使用统计", description = "获取实时功能使用统计数据")
    @GetMapping("/realtime-stats")
    @PreAuthorize("hasPermission('behavior:feature', 'READ')")
    public Result<FeatureUsageRepository.FeatureRealTimeStats> getRealTimeStats(
            @Parameter(description = "功能ID列表") @RequestParam(required = false) List<String> featureIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            String dataScope = SecurityUtils.getCurrentUserDataScope();
            FeatureUsageRepository.FeatureRealTimeStats stats = featureUsageService.getRealTimeFeatureStats(featureIds, productLineIds, dataScope);
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取实时功能使用统计失败", e);
            return Result.error("获取实时功能使用统计失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取功能使用分析统计摘要", description = "获取功能使用分析的统计摘要数据")
    @GetMapping("/summary")
    @PreAuthorize("hasPermission('behavior:feature', 'READ')")
    public Result<FeatureUsageRepository.FeatureUsageAnalysisSummary> getSummary(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        try {
            FeatureUsageRequest request = FeatureUsageRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            FeatureUsageRepository.FeatureUsageAnalysisSummary summary = featureUsageService.getFeatureUsageAnalysisSummary(request);
            return Result.success(summary);

        } catch (Exception e) {
            log.error("获取功能使用分析统计摘要失败", e);
            return Result.error("获取功能使用分析统计摘要失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取功能使用趋势对比", description = "获取功能使用趋势对比数据")
    @GetMapping("/trend-comparison")
    @PreAuthorize("hasPermission('behavior:feature', 'READ')")
    public Result<FeatureUsageRepository.FeatureUsageTrendComparison> getTrendComparison(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "功能ID列表") @RequestParam(required = false) List<String> featureIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            FeatureUsageRequest request = FeatureUsageRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .featureIds(featureIds)
                    .productLineIds(productLineIds)
                    .dataScope(SecurityUtils.getCurrentUserDataScope())
                    .userId(SecurityUtils.getCurrentUserId())
                    .build();

            FeatureUsageRepository.FeatureUsageTrendComparison comparison = featureUsageService.getFeatureUsageTrendComparison(request);
            return Result.success(comparison);

        } catch (Exception e) {
            log.error("获取功能使用趋势对比失败", e);
            return Result.error("获取功能使用趋势对比失败: " + e.getMessage());
        }
    }

    // ==================== 公共接口（无需认证） ====================

    /**
     * 获取公共功能使用统计 - 无需认证
     */
    @Operation(summary = "获取公共功能使用统计", description = "获取功能使用统计数据（无需认证）")
    @GetMapping("/public/usage-statistics")
    public Result<FeatureUsageRepository.FeatureUsageAnalysisSummary> getPublicUsageStatistics(
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "功能ID列表") @RequestParam(required = false) List<String> featureIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds,
            @Parameter(description = "是否包含对比数据") @RequestParam(required = false, defaultValue = "false") boolean includeComparison) {

        try {
            FeatureUsageRequest request = FeatureUsageRequest.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .granularity(TimeRange.TimeGranularity.DAY)
                    .featureIds(featureIds)
                    .productLineIds(productLineIds)
                    .includeComparison(includeComparison)
                    .build();

            FeatureUsageRepository.FeatureUsageAnalysisSummary statistics = featureUsageService.getFeatureUsageAnalysisSummary(request);
            return Result.success(statistics);

        } catch (Exception e) {
            log.error("获取公共功能使用统计失败", e);
            return Result.error("获取公共功能使用统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共功能列表 - 无需认证
     */
    @Operation(summary = "获取公共功能列表", description = "获取可用的功能列表（无需认证）")
    @GetMapping("/public/list")
    public Result<List<FeatureUsageRepository.FeatureInfo>> getPublicFeatureList(
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {
        try {
            List<FeatureUsageRepository.FeatureInfo> featureList = featureUsageService.getFeatureList(productLineIds, "PUBLIC");
            return Result.success(featureList);

        } catch (Exception e) {
            log.error("获取公共功能列表失败", e);
            return Result.error("获取公共功能列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取公共实时统计 - 无需认证
     */
    @Operation(summary = "获取公共实时统计", description = "获取功能使用实时统计数据（无需认证）")
    @GetMapping("/public/realtime-stats")
    public Result<FeatureUsageRepository.FeatureRealTimeStats> getPublicRealTimeStats(
            @Parameter(description = "功能ID列表") @RequestParam(required = false) List<String> featureIds,
            @Parameter(description = "产品线ID列表") @RequestParam(required = false) List<Long> productLineIds) {

        try {
            FeatureUsageRepository.FeatureRealTimeStats stats = featureUsageService.getRealTimeFeatureStats(featureIds, productLineIds, "PUBLIC");
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取公共实时统计失败", e);
            return Result.error("获取公共实时统计失败: " + e.getMessage());
        }
    }
}
