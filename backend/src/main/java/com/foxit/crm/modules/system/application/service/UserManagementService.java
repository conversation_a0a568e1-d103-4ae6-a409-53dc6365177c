package com.foxit.crm.modules.system.application.service;

import com.foxit.crm.common.util.PageResult;
import com.foxit.crm.modules.system.api.dto.request.RegisterRequest;
import com.foxit.crm.modules.system.api.dto.response.UserInfoResponse;

/**
 * 用户管理应用服务接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface UserManagementService {

    /**
     * 创建用户
     *
     * @param request 注册请求
     * @return 用户ID
     */
    Long createUser(RegisterRequest request);

    /**
     * 禁用用户
     *
     * @param userId 用户ID
     */
    void disableUser(Long userId);

    /**
     * 启用用户
     *
     * @param userId 用户ID
     */
    void enableUser(Long userId);

    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @return 新密码
     */
    String resetPassword(Long userId);

    /**
     * 获取用户列表
     *
     * @param page    页码
     * @param size    每页大小
     * @param keyword 关键词
     * @return 用户分页结果
     */
    PageResult<UserInfoResponse> getUserList(int page, int size, String keyword);

    /**
     * 获取用户详情
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    UserInfoResponse getUserById(Long userId);

    /**
     * 删除用户
     *
     * @param userId 用户ID
     */
    void deleteUser(Long userId);
}
