package com.foxit.crm.modules.system.domain.model.aggregate;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 产品线聚合根
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@NoArgsConstructor
public class ProductLine {

    /**
     * 产品线ID
     */
    private Long id;

    /**
     * 产品线编码
     */
    private String code;

    /**
     * 产品线名称
     */
    private String name;

    /**
     * 产品线描述
     */
    private String description;

    /**
     * 产品线类型：1-阅读器，2-编辑器，3-云服务，4-工具类，5-内容平台
     */
    private Integer type;

    /**
     * 产品线状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 产品线图标
     */
    private String icon;

    /**
     * 产品线颜色
     */
    private String color;

    /**
     * 负责人ID
     */
    private Long ownerId;

    /**
     * 负责人姓名
     */
    private String ownerName;

    /**
     * 数据源配置
     */
    private String dataSourceConfig;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 版本号（乐观锁）
     */
    private Integer version;

    /**
     * 产品线类型枚举
     */
    public enum ProductLineType {
        READER(1, "阅读器"),
        EDITOR(2, "编辑器"),
        CLOUD_SERVICE(3, "云服务"),
        TOOL(4, "工具类"),
        CONTENT_PLATFORM(5, "内容平台");

        private final Integer code;
        private final String name;

        ProductLineType(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static ProductLineType fromCode(Integer code) {
            for (ProductLineType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 构造函数
     */
    public ProductLine(String code, String name, String description, Integer type, 
                      Integer status, Integer sortOrder, String icon, String color, 
                      Long ownerId, String ownerName, String dataSourceConfig, String remark) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.type = type;
        this.status = status;
        this.sortOrder = sortOrder;
        this.icon = icon;
        this.color = color;
        this.ownerId = ownerId;
        this.ownerName = ownerName;
        this.dataSourceConfig = dataSourceConfig;
        this.remark = remark;
    }

    /**
     * 启用产品线
     */
    public void enable() {
        this.status = 1;
    }

    /**
     * 禁用产品线
     */
    public void disable() {
        this.status = 0;
    }

    /**
     * 检查产品线是否启用
     */
    public boolean isEnabled() {
        return this.status != null && this.status == 1;
    }

    /**
     * 检查产品线编码是否有效
     */
    public boolean isValidCode() {
        return this.code != null && !this.code.trim().isEmpty();
    }

    /**
     * 检查产品线名称是否有效
     */
    public boolean isValidName() {
        return this.name != null && !this.name.trim().isEmpty();
    }

    /**
     * 检查产品线类型是否有效
     */
    public boolean isValidType() {
        return this.type != null && ProductLineType.fromCode(this.type) != null;
    }

    /**
     * 获取产品线类型名称
     */
    public String getTypeName() {
        ProductLineType productLineType = ProductLineType.fromCode(this.type);
        return productLineType != null ? productLineType.getName() : "未知";
    }

    /**
     * 检查数据源配置是否有效
     */
    public boolean hasDataSourceConfig() {
        return this.dataSourceConfig != null && !this.dataSourceConfig.trim().isEmpty();
    }
}
