package com.foxit.crm.modules.system.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.modules.system.api.dto.response.OperationLogDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.OperationLogListResponse;
import com.foxit.crm.modules.system.api.dto.response.OperationLogStatisticsResponse;
import com.foxit.crm.modules.system.application.service.OperationLogService;
import com.foxit.crm.shared.domain.event.OperationLog;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作日志管理控制器
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@RestController
@RequestMapping("/admin/operation-logs")
@Validated
@RequiredArgsConstructor
@PreAuthorize("hasAuthority('ADMIN')") // 整个控制器只允许管理员访问
public class OperationLogController {

    private final OperationLogService operationLogService;

    /**
     * 获取操作日志详情
     */
    @GetMapping("/{id}")
    @OperationLog(value = "查看操作日志详情", operation = "VIEW_OPERATION_LOG")
    public Result<OperationLogDetailResponse> getOperationLogById(@PathVariable Long id) {
        OperationLogDetailResponse response = operationLogService.getOperationLogById(id);
        return Result.success(response);
    }

    /**
     * 分页查询操作日志列表
     */
    @GetMapping
    @OperationLog(value = "查看操作日志列表", operation = "LIST_OPERATION_LOGS")
    public Result<OperationLogListResponse> getOperationLogList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(required = false) Long userId) {
        OperationLogListResponse response = operationLogService.getOperationLogList(page, size, keyword, status, startTime, endTime, userId);
        return Result.success(response);
    }

    /**
     * 获取用户操作日志列表
     */
    @GetMapping("/user/{userId}")
    @OperationLog(value = "获取用户操作日志", operation = "GET_USER_OPERATION_LOGS")
    public Result<OperationLogListResponse> getUserOperationLogs(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        OperationLogListResponse response = operationLogService.getUserOperationLogs(userId, page, size);
        return Result.success(response);
    }

    /**
     * 根据操作类型获取操作日志列表
     */
    @GetMapping("/operation/{operation}")
    @OperationLog(value = "按操作类型获取日志", operation = "GET_OPERATION_LOGS_BY_TYPE")
    public Result<OperationLogListResponse> getOperationLogsByType(
            @PathVariable String operation,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        OperationLogListResponse response = operationLogService.getOperationLogsByType(operation, page, size);
        return Result.success(response);
    }

    /**
     * 获取失败操作日志列表
     */
    @GetMapping("/failed")
    @OperationLog(value = "获取失败操作日志", operation = "GET_FAILED_OPERATION_LOGS")
    public Result<OperationLogListResponse> getFailedOperationLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        OperationLogListResponse response = operationLogService.getFailedOperationLogs(page, size);
        return Result.success(response);
    }

    /**
     * 获取慢操作日志列表
     */
    @GetMapping("/slow")
    @OperationLog(value = "获取慢操作日志", operation = "GET_SLOW_OPERATION_LOGS")
    public Result<OperationLogListResponse> getSlowOperationLogs(
            @RequestParam(defaultValue = "5000") Long minExecutionTime,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        OperationLogListResponse response = operationLogService.getSlowOperationLogs(minExecutionTime, page, size);
        return Result.success(response);
    }

    /**
     * 获取敏感操作日志列表
     */
    @GetMapping("/sensitive")
    @OperationLog(value = "获取敏感操作日志", operation = "GET_SENSITIVE_OPERATION_LOGS")
    public Result<OperationLogListResponse> getSensitiveOperationLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        OperationLogListResponse response = operationLogService.getSensitiveOperationLogs(page, size);
        return Result.success(response);
    }

    /**
     * 根据IP地址获取操作日志列表
     */
    @GetMapping("/ip/{clientIp}")
    @OperationLog(value = "按IP获取操作日志", operation = "GET_OPERATION_LOGS_BY_IP")
    public Result<OperationLogListResponse> getOperationLogsByIp(
            @PathVariable String clientIp,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        OperationLogListResponse response = operationLogService.getOperationLogsByIp(clientIp, page, size);
        return Result.success(response);
    }

    /**
     * 获取最近操作日志
     */
    @GetMapping("/recent")
    @OperationLog(value = "获取最近操作日志", operation = "GET_RECENT_OPERATION_LOGS")
    public Result<List<OperationLogDetailResponse>> getRecentOperationLogs(
            @RequestParam(defaultValue = "10") Integer limit) {
        List<OperationLogDetailResponse> response = operationLogService.getRecentOperationLogs(limit);
        return Result.success(response);
    }

    /**
     * 获取用户最近操作日志
     */
    @GetMapping("/user/{userId}/recent")
    @OperationLog(value = "获取用户最近操作日志", operation = "GET_USER_RECENT_OPERATION_LOGS")
    public Result<List<OperationLogDetailResponse>> getUserRecentOperationLogs(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "10") Integer limit) {
        List<OperationLogDetailResponse> response = operationLogService.getUserRecentOperationLogs(userId, limit);
        return Result.success(response);
    }

    /**
     * 获取操作日志统计信息
     * 支持自定义时间范围和预设时间范围（today, week, month）
     */
    @GetMapping("/statistics")
    @OperationLog(value = "获取操作日志统计", operation = "GET_OPERATION_LOG_STATISTICS")
    public Result<OperationLogStatisticsResponse> getOperationLogStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(required = false) String timeRange) {

        OperationLogStatisticsResponse response;

        // 如果指定了预设时间范围，使用预设逻辑
        if ("today".equals(timeRange)) {
            response = operationLogService.getTodayOperationStatistics();
        } else if ("week".equals(timeRange)) {
            response = operationLogService.getWeekOperationStatistics();
        } else if ("month".equals(timeRange)) {
            response = operationLogService.getMonthOperationStatistics();
        } else {
            // 使用自定义时间范围
            response = operationLogService.getOperationLogStatistics(startTime, endTime);
        }

        return Result.success(response);
    }

    /**
     * 清理过期操作日志
     */
    @DeleteMapping("/cleanup")
    @OperationLog(value = "清理过期操作日志", operation = "CLEANUP_EXPIRED_OPERATION_LOGS", saveParams = true)
    public Result<String> cleanExpiredOperationLogs(@RequestParam(defaultValue = "90") Integer retentionDays) {
        operationLogService.cleanExpiredOperationLogs(retentionDays);
        return Result.success("过期操作日志清理成功");
    }

    /**
     * 导出操作日志
     */
    @GetMapping("/export")
    @OperationLog(value = "导出操作日志", operation = "EXPORT_OPERATION_LOGS", saveParams = true)
    public ResponseEntity<byte[]> exportOperationLogs(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(required = false) Long userId) {
        
        byte[] data = operationLogService.exportOperationLogs(keyword, status, startTime, endTime, userId);
        
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=operation_logs.xlsx")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(data);
    }

    /**
     * 生成审计报告
     */
    @GetMapping("/audit-report")
    @OperationLog(value = "生成审计报告", operation = "GENERATE_AUDIT_REPORT", saveParams = true)
    public ResponseEntity<byte[]> generateAuditReport(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        byte[] data = operationLogService.generateAuditReport(startTime, endTime);
        
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=audit_report.pdf")
                .contentType(MediaType.APPLICATION_PDF)
                .body(data);
    }
}
