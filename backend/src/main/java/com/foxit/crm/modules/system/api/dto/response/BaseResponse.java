package com.foxit.crm.modules.system.api.dto.response;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 基础响应DTO - 包含通用字段
 * 
 * 所有响应DTO的基类，包含ID和时间戳等通用字段
 * 
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
public abstract class BaseResponse {

    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
