package com.foxit.crm.modules.system.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.modules.system.api.dto.request.PermissionCreateRequest;
import com.foxit.crm.modules.system.api.dto.request.PermissionUpdateRequest;
import com.foxit.crm.modules.system.api.dto.response.PermissionDetailResponse;
import com.foxit.crm.modules.system.api.dto.response.PermissionTreeResponse;
import com.foxit.crm.modules.system.api.dto.response.PageResponse;
import com.foxit.crm.modules.system.application.service.PermissionService;
import com.foxit.crm.shared.domain.event.OperationLog;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限管理控制器
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@RestController
@RequestMapping("/admin/permissions")
@Validated
@RequiredArgsConstructor
@PreAuthorize("hasAuthority('ADMIN')") // 整个控制器只允许管理员访问
public class PermissionController {

    private final PermissionService permissionService;

    /**
     * 创建权限
     */
    @PostMapping
    @OperationLog(value = "创建权限", operation = "CREATE_PERMISSION", saveParams = true)
    public Result<Long> createPermission(@Valid @RequestBody PermissionCreateRequest request) {
        Long permissionId = permissionService.createPermission(request);
        return Result.success(permissionId, "权限创建成功");
    }

    /**
     * 更新权限
     */
    @PutMapping("/{id}")
    @OperationLog(value = "更新权限", operation = "UPDATE_PERMISSION", saveParams = true)
    public Result<String> updatePermission(@PathVariable Long id, @Valid @RequestBody PermissionUpdateRequest request) {
        permissionService.updatePermission(id, request);
        return Result.success("权限更新成功");
    }

    /**
     * 删除权限
     */
    @DeleteMapping("/{id}")
    @OperationLog(value = "删除权限", operation = "DELETE_PERMISSION")
    public Result<String> deletePermission(@PathVariable Long id) {
        permissionService.deletePermission(id);
        return Result.success("权限删除成功");
    }

    /**
     * 获取权限详情
     */
    @GetMapping("/{id}")
    @OperationLog(value = "查看权限详情", operation = "VIEW_PERMISSION")
    public Result<PermissionDetailResponse> getPermissionById(@PathVariable Long id) {
        PermissionDetailResponse response = permissionService.getPermissionById(id);
        return Result.success(response);
    }

    /**
     * 分页查询权限列表
     */
    @GetMapping
    @OperationLog(value = "查看权限列表", operation = "LIST_PERMISSIONS")
    public Result<PageResponse<PermissionDetailResponse>> getPermissionList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer permissionType) {
        PageResponse<PermissionDetailResponse> response = permissionService.getPermissionList(page, size, keyword,
                permissionType);
        return Result.success(response);
    }

    /**
     * 获取权限树结构
     */
    @GetMapping("/tree")
    @OperationLog(value = "获取权限树", operation = "GET_PERMISSION_TREE")
    public Result<List<PermissionTreeResponse>> getPermissionTree() {
        List<PermissionTreeResponse> response = permissionService.getPermissionTree();
        return Result.success(response);
    }

    /**
     * 根据父权限ID获取子权限列表
     */
    @GetMapping("/parent/{parentId}")
    @OperationLog(value = "获取子权限列表", operation = "GET_CHILD_PERMISSIONS")
    public Result<List<PermissionDetailResponse>> getPermissionsByParentId(@PathVariable Long parentId) {
        List<PermissionDetailResponse> response = permissionService.getPermissionsByParentId(parentId);
        return Result.success(response);
    }

    /**
     * 根据权限类型获取权限列表
     */
    @GetMapping("/type/{permissionType}")
    @OperationLog(value = "按类型获取权限列表", operation = "GET_PERMISSIONS_BY_TYPE")
    public Result<List<PermissionDetailResponse>> getPermissionsByType(@PathVariable Integer permissionType) {
        List<PermissionDetailResponse> response = permissionService.getPermissionsByType(permissionType);
        return Result.success(response);
    }

    /**
     * 根据用户ID获取权限列表
     */
    @GetMapping("/user/{userId}")
    @OperationLog(value = "获取用户权限列表", operation = "GET_USER_PERMISSIONS")
    public Result<List<PermissionDetailResponse>> getPermissionsByUserId(@PathVariable Long userId) {
        List<PermissionDetailResponse> response = permissionService.getPermissionsByUserId(userId);
        return Result.success(response);
    }

    /**
     * 根据角色ID获取权限列表
     */
    @GetMapping("/role/{roleId}")
    @OperationLog(value = "获取角色权限列表", operation = "GET_ROLE_PERMISSIONS")
    public Result<List<PermissionDetailResponse>> getPermissionsByRoleId(@PathVariable Long roleId) {
        List<PermissionDetailResponse> response = permissionService.getPermissionsByRoleId(roleId);
        return Result.success(response);
    }

    /**
     * 启用权限
     */
    @PutMapping("/{id}/enable")
    @OperationLog(value = "启用权限", operation = "ENABLE_PERMISSION")
    public Result<String> enablePermission(@PathVariable Long id) {
        permissionService.enablePermission(id);
        return Result.success("权限启用成功");
    }

    /**
     * 禁用权限
     */
    @PutMapping("/{id}/disable")
    @OperationLog(value = "禁用权限", operation = "DISABLE_PERMISSION")
    public Result<String> disablePermission(@PathVariable Long id) {
        permissionService.disablePermission(id);
        return Result.success("权限禁用成功");
    }

    /**
     * 获取所有启用的菜单权限（用于前端菜单构建）
     */
    @GetMapping("/menu/enabled")
    @OperationLog(value = "获取启用菜单权限", operation = "GET_ENABLED_MENU_PERMISSIONS")
    public Result<List<PermissionTreeResponse>> getEnabledMenuPermissions() {
        List<PermissionTreeResponse> response = permissionService.getEnabledMenuPermissions();
        return Result.success(response);
    }

    /**
     * 根据权限ID列表获取权限详情
     */
    @PostMapping("/batch")
    @OperationLog(value = "批量获取权限详情", operation = "GET_PERMISSIONS_BY_IDS", saveParams = true)
    public Result<List<PermissionDetailResponse>> getPermissionsByIds(@RequestBody List<Long> ids) {
        List<PermissionDetailResponse> response = permissionService.getPermissionsByIds(ids);
        return Result.success(response);
    }
}
