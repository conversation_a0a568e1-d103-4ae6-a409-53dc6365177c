package com.foxit.crm.modules.system.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.modules.system.api.dto.response.OperationLogListResponse;
import com.foxit.crm.modules.system.application.service.OperationLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * 公共操作日志控制器（无需认证）
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@RestController
@RequestMapping("/admin/operation-logs")
@Validated
@RequiredArgsConstructor
@Tag(name = "公共操作日志", description = "公共操作日志相关接口（无需认证）")
public class PublicOperationLogController {

    private final OperationLogService operationLogService;

    /**
     * 获取公共操作日志列表 - 无需认证
     */
    @Operation(summary = "获取公共操作日志列表", description = "获取操作日志列表（无需认证，数据已脱敏）")
    @GetMapping("/public")
    public Result<OperationLogListResponse> getPublicOperationLogList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "关键词搜索") @RequestParam(required = false) String keyword,
            @Parameter(description = "状态筛选") @RequestParam(required = false) Integer status,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId) {
        
        // 公共接口只返回基本的操作日志信息，不包含敏感数据
        OperationLogListResponse response = operationLogService.getPublicOperationLogList(page, size, keyword, status, startTime, endTime, userId);
        return Result.success(response);
    }
}
