package com.foxit.crm.modules.dashboard.infrastructure.security;

import com.foxit.crm.modules.dashboard.application.dto.request.DashboardQueryRequest;
import com.foxit.crm.modules.dashboard.application.service.DashboardPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Dashboard数据权限拦截器
 * 基于AOP实现Dashboard数据的权限控制和数据隔离
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class DashboardDataPermissionInterceptor {

    private final DashboardPermissionService permissionService;

    /**
     * 拦截Dashboard数据查询方法
     */
    @Around("execution(* com.foxit.crm.modules.dashboard.application.service.DashboardService.*(..))")
    public Object interceptDashboardService(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();

        log.debug("拦截Dashboard服务方法: {}", methodName);

        // 获取当前用户ID（这里简化处理）
        Long currentUserId = getCurrentUserId();

        // 检查基本访问权限
        if (!checkBasicPermission(currentUserId, methodName)) {
            throw new SecurityException("无权限访问Dashboard数据");
        }

        // 检查访问频率限制
        if (!permissionService.checkRateLimit(currentUserId, methodName)) {
            throw new SecurityException("访问频率超限，请稍后再试");
        }

        // 过滤和修改请求参数
        Object[] filteredArgs = filterRequestArgs(currentUserId, methodName, args);

        try {
            // 执行原方法
            Object result = joinPoint.proceed(filteredArgs);

            // 过滤响应数据
            Object filteredResult = filterResponseData(currentUserId, methodName, result);

            // 记录访问日志
            permissionService.logDashboardAccess(currentUserId, methodName, "SUCCESS", "数据访问成功");

            return filteredResult;

        } catch (Exception e) {
            // 记录失败日志
            permissionService.logDashboardAccess(currentUserId, methodName, "FAILED", e.getMessage());
            throw e;
        }
    }

    /**
     * 检查基本权限
     */
    private boolean checkBasicPermission(Long userId, String methodName) {
        switch (methodName) {
            case "getOverviewDashboard":
                return permissionService.hasPermission(userId, "dashboard:overview:read");
            case "getProductLineDashboard":
                return permissionService.hasPermission(userId, "dashboard:product:read");
            case "getRealTimeStats":
                return permissionService.hasPermission(userId, "dashboard:realtime:read");
            case "getCoreMetrics":
            case "getChartData":
                return permissionService.hasPermission(userId, "dashboard:overview:read");
            case "refreshDashboardCache":
                return permissionService.hasPermission(userId, "dashboard:cache:write");
            case "updateDashboardConfig":
                return permissionService.hasPermission(userId, "dashboard:config:write");
            default:
                return true;
        }
    }

    /**
     * 过滤请求参数
     */
    private Object[] filterRequestArgs(Long userId, String methodName, Object[] args) {
        if (args == null || args.length == 0) {
            return args;
        }

        // 处理Dashboard查询请求
        for (int i = 0; i < args.length; i++) {
            if (args[i] instanceof DashboardQueryRequest) {
                args[i] = filterDashboardQueryRequest(userId,
                        (DashboardQueryRequest) args[i]);
            }
        }

        return args;
    }

    /**
     * 过滤Dashboard查询请求
     */
    private DashboardQueryRequest filterDashboardQueryRequest(
            Long userId, DashboardQueryRequest request) {

        // 设置用户的数据权限范围
        String userDataScope = permissionService.getDataScope(userId);
        if (request.getDataScope() == null || !isValidDataScope(userId, request.getDataScope())) {
            request.setDataScope(userDataScope);
        }

        // 过滤产品线ID列表
        if (request.getProductLineIds() != null && !request.getProductLineIds().isEmpty()) {
            List<Long> filteredIds = permissionService.filterAccessibleProductLines(userId,
                    request.getProductLineIds());
            request.setProductLineIds(filteredIds);
        } else {
            // 如果没有指定产品线，则使用用户可访问的所有产品线
            List<Long> accessibleIds = permissionService.getAccessibleProductLineIds(userId);
            request.setProductLineIds(accessibleIds);
        }

        log.debug("过滤后的Dashboard查询请求: userId={}, dataScope={}, productLineIds={}",
                userId, request.getDataScope(), request.getProductLineIds());

        return request;
    }

    /**
     * 检查数据权限范围是否有效
     */
    private boolean isValidDataScope(Long userId, String requestedScope) {
        String userDataScope = permissionService.getDataScope(userId);

        // 用户只能访问自己权限范围内的数据
        switch (userDataScope) {
            case "all":
                return true; // 管理员可以访问所有范围
            case "dept":
                return "dept".equals(requestedScope) || "own".equals(requestedScope);
            case "own":
                return "own".equals(requestedScope);
            default:
                return false;
        }
    }

    /**
     * 过滤响应数据
     */
    private Object filterResponseData(Long userId, String methodName, Object result) {
        if (result == null) {
            return result;
        }

        // 根据用户权限过滤敏感数据
        String dataScope = permissionService.getDataScope(userId);

        if ("own".equals(dataScope)) {
            // 普通用户只能看到有限的数据
            result = maskSensitiveData(result);
        }

        return result;
    }

    /**
     * 掩码敏感数据
     */
    private Object maskSensitiveData(Object data) {
        // 这里可以实现对敏感数据的掩码处理
        // 例如：隐藏具体的收入金额，只显示趋势
        log.debug("对敏感数据进行掩码处理");
        return data;
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        // 这里应该从Spring Security上下文获取当前用户ID
        // 暂时返回固定值用于测试
        return 1L;
    }
}
