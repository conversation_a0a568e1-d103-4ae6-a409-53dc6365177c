package com.foxit.crm.modules.system.domain.model.aggregate;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品版本聚合根
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Data
@NoArgsConstructor
public class ProductVersion {

    /**
     * 版本ID
     */
    private Long id;

    /**
     * 产品线ID
     */
    private Long productLineId;

    /**
     * 版本号
     */
    private String versionNumber;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 版本描述
     */
    private String description;

    /**
     * 发布说明
     */
    private String releaseNotes;

    /**
     * 版本类型
     */
    private VersionType versionType;

    /**
     * 版本状态
     */
    private VersionStatus status;

    /**
     * 是否当前版本
     */
    private Boolean isCurrent;

    /**
     * 发布时间
     */
    private LocalDateTime releaseDate;

    /**
     * 计划发布时间
     */
    private LocalDateTime plannedDate;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 下载地址
     */
    private String downloadUrl;

    /**
     * 下载次数
     */
    private Integer downloadCount;

    /**
     * 支持平台列表
     */
    private List<String> platforms;

    /**
     * 新功能列表
     */
    private List<String> features;

    /**
     * 修复问题列表
     */
    private List<String> bugFixes;

    /**
     * 破坏性变更列表
     */
    private List<String> breakingChanges;

    /**
     * 依赖信息
     */
    private List<String> dependencies;

    /**
     * 系统要求
     */
    private List<String> systemRequirements;

    /**
     * MD5校验值
     */
    private String checksumMd5;

    /**
     * SHA256校验值
     */
    private String checksumSha256;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人姓名
     */
    private String createdByName;

    /**
     * 审批人ID
     */
    private Long approvedBy;

    /**
     * 审批人姓名
     */
    private String approvedByName;

    /**
     * 审批时间
     */
    private LocalDateTime approvedAt;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 版本号（乐观锁）
     */
    private Integer version;

    /**
     * 版本类型枚举
     */
    public enum VersionType {
        MAJOR(1, "主版本"),
        MINOR(2, "次版本"),
        PATCH(3, "修订版"),
        PRERELEASE(4, "预发布版");

        private final Integer code;
        private final String name;

        VersionType(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static VersionType fromCode(Integer code) {
            for (VersionType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 版本状态枚举
     */
    public enum VersionStatus {
        DEVELOPMENT(1, "开发中"),
        TESTING(2, "测试中"),
        PRERELEASE(3, "预发布"),
        RELEASED(4, "已发布"),
        DEPRECATED(5, "已废弃");

        private final Integer code;
        private final String name;

        VersionStatus(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static VersionStatus fromCode(Integer code) {
            for (VersionStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 构造函数
     */
    public ProductVersion(Long productLineId, String versionNumber, String versionName, 
                         String description, String releaseNotes, VersionType versionType,
                         Long createdBy, String createdByName) {
        this.productLineId = productLineId;
        this.versionNumber = versionNumber;
        this.versionName = versionName;
        this.description = description;
        this.releaseNotes = releaseNotes;
        this.versionType = versionType;
        this.status = VersionStatus.DEVELOPMENT;
        this.isCurrent = false;
        this.downloadCount = 0;
        this.createdBy = createdBy;
        this.createdByName = createdByName;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 业务方法：发布版本
     */
    public void release(Long approvedBy, String approvedByName, String reason) {
        if (this.status != VersionStatus.PRERELEASE) {
            throw new IllegalStateException("只有预发布状态的版本才能发布");
        }
        this.status = VersionStatus.RELEASED;
        this.releaseDate = LocalDateTime.now();
        this.approvedBy = approvedBy;
        this.approvedByName = approvedByName;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 业务方法：设为当前版本
     */
    public void setAsCurrent() {
        if (this.status != VersionStatus.RELEASED) {
            throw new IllegalStateException("只有已发布的版本才能设为当前版本");
        }
        this.isCurrent = true;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 业务方法：取消当前版本
     */
    public void unsetAsCurrent() {
        this.isCurrent = false;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 业务方法：废弃版本
     */
    public void deprecate(String reason) {
        if (this.isCurrent) {
            throw new IllegalStateException("当前版本不能被废弃");
        }
        this.status = VersionStatus.DEPRECATED;
        this.remark = reason;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 业务方法：增加下载次数
     */
    public void increaseDownloadCount(Integer count) {
        this.downloadCount += count;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 业务方法：更新版本状态
     */
    public void updateStatus(VersionStatus newStatus, String reason) {
        // 状态转换验证
        if (!isValidStatusTransition(this.status, newStatus)) {
            throw new IllegalStateException(
                String.format("不能从状态 %s 转换到 %s", this.status.getName(), newStatus.getName())
            );
        }
        this.status = newStatus;
        this.remark = reason;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 验证状态转换是否合法
     */
    private boolean isValidStatusTransition(VersionStatus from, VersionStatus to) {
        if (from == to) return true;
        
        switch (from) {
            case DEVELOPMENT:
                return to == VersionStatus.TESTING || to == VersionStatus.DEPRECATED;
            case TESTING:
                return to == VersionStatus.PRERELEASE || to == VersionStatus.DEVELOPMENT || to == VersionStatus.DEPRECATED;
            case PRERELEASE:
                return to == VersionStatus.RELEASED || to == VersionStatus.TESTING || to == VersionStatus.DEPRECATED;
            case RELEASED:
                return to == VersionStatus.DEPRECATED;
            case DEPRECATED:
                return false; // 废弃状态不能转换到其他状态
            default:
                return false;
        }
    }

    /**
     * 业务方法：检查是否可以删除
     */
    public boolean canBeDeleted() {
        return !this.isCurrent && this.status != VersionStatus.RELEASED;
    }

    /**
     * 业务方法：获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (fileSize == null) return "未知";
        
        double size = fileSize.doubleValue();
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", size, units[unitIndex]);
    }
}
