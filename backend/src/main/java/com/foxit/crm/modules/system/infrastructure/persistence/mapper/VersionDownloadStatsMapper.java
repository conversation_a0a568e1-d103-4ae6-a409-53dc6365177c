package com.foxit.crm.modules.system.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.VersionDownloadStatsPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

/**
 * 版本下载统计Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Mapper
public interface VersionDownloadStatsMapper extends BaseMapper<VersionDownloadStatsPO> {

    /**
     * 根据版本ID和日期获取下载统计
     */
    @Select("SELECT * FROM version_download_stats WHERE version_id = #{versionId} AND download_date = #{downloadDate} AND deleted = 0")
    VersionDownloadStatsPO findByVersionIdAndDate(@Param("versionId") Long versionId, @Param("downloadDate") LocalDate downloadDate);

    /**
     * 根据版本ID获取下载统计列表
     */
    @Select("SELECT * FROM version_download_stats WHERE version_id = #{versionId} AND deleted = 0 ORDER BY download_date DESC")
    List<VersionDownloadStatsPO> findByVersionId(@Param("versionId") Long versionId);

    /**
     * 根据版本ID和日期范围获取下载统计
     */
    @Select("SELECT * FROM version_download_stats WHERE version_id = #{versionId} AND download_date BETWEEN #{startDate} AND #{endDate} AND deleted = 0 ORDER BY download_date DESC")
    List<VersionDownloadStatsPO> findByVersionIdAndDateRange(@Param("versionId") Long versionId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 获取版本总下载次数
     */
    @Select("SELECT COALESCE(SUM(download_count), 0) FROM version_download_stats WHERE version_id = #{versionId} AND deleted = 0")
    Long getTotalDownloadsByVersionId(@Param("versionId") Long versionId);

    /**
     * 获取版本总独立下载次数
     */
    @Select("SELECT COALESCE(SUM(unique_downloads), 0) FROM version_download_stats WHERE version_id = #{versionId} AND deleted = 0")
    Long getTotalUniqueDownloadsByVersionId(@Param("versionId") Long versionId);

    /**
     * 获取最近N天的下载统计
     */
    @Select("SELECT * FROM version_download_stats WHERE version_id = #{versionId} AND download_date >= #{startDate} AND deleted = 0 ORDER BY download_date DESC")
    List<VersionDownloadStatsPO> findRecentStats(@Param("versionId") Long versionId, @Param("startDate") LocalDate startDate);

    /**
     * 获取热门版本下载排行
     */
    @Select("SELECT version_id, SUM(download_count) as total_downloads FROM version_download_stats WHERE download_date >= #{startDate} AND deleted = 0 GROUP BY version_id ORDER BY total_downloads DESC LIMIT #{limit}")
    List<Object> getTopDownloadVersions(@Param("startDate") LocalDate startDate, @Param("limit") Integer limit);
}
