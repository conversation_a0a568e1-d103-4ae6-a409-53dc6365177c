package com.foxit.crm.modules.system.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.foxit.crm.modules.system.infrastructure.persistence.entity.ProductVersionPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 产品版本Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Mapper
public interface ProductVersionMapper extends BaseMapper<ProductVersionPO> {

    /**
     * 根据产品线ID获取当前版本
     */
    @Select("SELECT * FROM product_version WHERE product_line_id = #{productLineId} AND is_current = 1 AND deleted = 0")
    ProductVersionPO getCurrentVersionByProductLineId(@Param("productLineId") Long productLineId);

    /**
     * 根据产品线ID获取最新版本号
     */
    @Select("SELECT version_number FROM product_version WHERE product_line_id = #{productLineId} AND deleted = 0 ORDER BY create_time DESC LIMIT 1")
    String getLatestVersionNumber(@Param("productLineId") Long productLineId);

    /**
     * 检查版本号是否存在
     */
    @Select("SELECT COUNT(1) FROM product_version WHERE product_line_id = #{productLineId} AND version_number = #{versionNumber} AND deleted = 0")
    int countByProductLineIdAndVersionNumber(@Param("productLineId") Long productLineId, @Param("versionNumber") String versionNumber);

    /**
     * 检查版本号是否存在（排除指定ID）
     */
    @Select("SELECT COUNT(1) FROM product_version WHERE product_line_id = #{productLineId} AND version_number = #{versionNumber} AND id != #{excludeId} AND deleted = 0")
    int countByProductLineIdAndVersionNumberExcludeId(@Param("productLineId") Long productLineId, @Param("versionNumber") String versionNumber, @Param("excludeId") Long excludeId);

    /**
     * 清除产品线的当前版本标记
     */
    @Update("UPDATE product_version SET is_current = 0 WHERE product_line_id = #{productLineId} AND deleted = 0")
    int clearCurrentVersionByProductLineId(@Param("productLineId") Long productLineId);

    /**
     * 设置当前版本
     */
    @Update("UPDATE product_version SET is_current = 1 WHERE id = #{versionId} AND deleted = 0")
    int setCurrentVersion(@Param("versionId") Long versionId);

    /**
     * 增加下载次数
     */
    @Update("UPDATE product_version SET download_count = download_count + #{count} WHERE id = #{versionId} AND deleted = 0")
    int increaseDownloadCount(@Param("versionId") Long versionId, @Param("count") Integer count);

    /**
     * 根据产品线ID和状态获取版本列表
     */
    @Select("SELECT * FROM product_version WHERE product_line_id = #{productLineId} AND status = #{status} AND deleted = 0 ORDER BY create_time DESC")
    List<ProductVersionPO> findByProductLineIdAndStatus(@Param("productLineId") Long productLineId, @Param("status") Integer status);

    /**
     * 根据状态获取版本列表
     */
    @Select("SELECT * FROM product_version WHERE status = #{status} AND deleted = 0 ORDER BY create_time DESC")
    List<ProductVersionPO> findByStatus(@Param("status") Integer status);

    /**
     * 获取版本统计信息
     */
    @Select("SELECT status, COUNT(1) as count FROM product_version WHERE product_line_id = #{productLineId} AND deleted = 0 GROUP BY status")
    List<Object> getVersionStatsByProductLineId(@Param("productLineId") Long productLineId);
}
