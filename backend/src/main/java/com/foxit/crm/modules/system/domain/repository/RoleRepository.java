package com.foxit.crm.modules.system.domain.repository;

import com.foxit.crm.modules.system.domain.model.aggregate.Role;

import java.util.List;
import java.util.Optional;

/**
 * 角色仓储接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface RoleRepository {

    /**
     * 保存角色
     */
    Role save(Role role);

    /**
     * 根据ID查找角色
     */
    Optional<Role> findById(Long id);

    /**
     * 根据角色编码查找角色
     */
    Optional<Role> findByRoleCode(String roleCode);

    /**
     * 查找所有启用的角色
     */
    List<Role> findAllEnabled();

    /**
     * 分页查询角色
     */
    List<Role> findByPage(int page, int size, String keyword);

    /**
     * 统计角色总数
     */
    long count(String keyword);

    /**
     * 根据用户ID查找角色列表
     */
    List<Role> findByUserId(Long userId);

    /**
     * 删除角色
     */
    void deleteById(Long id);

    /**
     * 检查角色编码是否存在
     */
    boolean existsByRoleCode(String roleCode);

    /**
     * 检查角色编码是否存在（排除指定ID）
     */
    boolean existsByRoleCodeAndIdNot(String roleCode, Long id);

    /**
     * 检查角色名称是否存在
     */
    boolean existsByRoleName(String roleName);
}
