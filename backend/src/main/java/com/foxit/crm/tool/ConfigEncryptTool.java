package com.foxit.crm.tool;

import com.foxit.crm.common.util.JasyptUtil;

/**
 * Jasypt 配置加密工具类
 * <p>
 * 提供命令行方式的配置加密工具。
 * 可以快速加密数据库密码、密钥等敏感信息。
 * </p>
 *
 * <AUTHOR> Dev Team
 */
public class ConfigEncryptTool {

    /**
     * 命令行加密工具入口
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 参数: 操作类型(enc/dec) 密钥 值
        if (args.length < 3) {
            printUsage();
            return;
        }

        String operation = args[0];
        String password = args[1];
        String value = args[2];

        if ("enc".equalsIgnoreCase(operation)) {
            encryptValue(value, password);
        } else if ("dec".equalsIgnoreCase(operation)) {
            decryptValue(value, password);
        } else {
            System.out.println("不支持的操作: " + operation);
            printUsage();
        }
    }

    /**
     * 加密值并打印结果
     *
     * @param value    要加密的值
     * @param password 加密密钥
     */
    private static void encryptValue(String value, String password) {
        try {
            System.out.println("\n==== 加密操作 ====");
            System.out.println("原始值: " + value);
            String encrypted = JasyptUtil.encrypt(value, password);
            System.out.println("加密后: " + encrypted);
            System.out.println("\n配置示例:");
            System.out.println("password: ENC(" + encrypted + ")");
            System.out.println("==== 操作成功 ====\n");
        } catch (Exception e) {
            System.err.println("加密失败: " + e.getMessage());
        }
    }

    /**
     * 解密值并打印结果
     *
     * @param value    要解密的值
     * @param password 解密密钥
     */
    private static void decryptValue(String value, String password) {
        try {
            System.out.println("\n==== 解密操作 ====");
            System.out.println("加密值: " + value);
            String decrypted = JasyptUtil.decrypt(value, password);
            System.out.println("解密后: " + decrypted);
            System.out.println("==== 操作成功 ====\n");
        } catch (Exception e) {
            System.err.println("解密失败: " + e.getMessage());
        }
    }

    /**
     * 打印使用说明
     */
    private static void printUsage() {
        System.out.println("\n==== SCRM-Next 配置加密工具 ====");
        System.out.println("使用方法:");
        System.out.println("java -cp ... com.foxit.crm.tool.ConfigEncryptTool [enc|dec] [密钥] [值]");
        System.out.println();
        System.out.println("示例加密: ");
        System.out.println("java -cp ... com.foxit.crm.tool.ConfigEncryptTool enc yourSecretKey yourPassword");
        System.out.println();
        System.out.println("示例解密: ");
        System.out.println("java -cp ... com.foxit.crm.tool.ConfigEncryptTool dec yourSecretKey encryptedValue");
        System.out.println("============================\n");
    }
}
