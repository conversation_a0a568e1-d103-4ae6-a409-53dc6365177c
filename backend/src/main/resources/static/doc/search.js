let api = [];
const apiDocListSize = 2
api.push({
    name: '系统管理',
    order: '1',
    list: []
})
api[0].list.push({
    alias: 'SystemConfigController',
    order: '1',
    link: '系统配置管理控制器',
    desc: '系统配置管理控制器',
    list: []
})
api[0].list[0].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs',
    methodId: '56eb6763e6bef5c76c426af78b0979de',
    desc: '创建系统配置',
});
api[0].list[0].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/{id}',
    methodId: '4151f03a2e0a6ce41d0b03f150d2979e',
    desc: '更新系统配置',
});
api[0].list[0].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/{id}',
    methodId: 'b52ce8608316270f8668b32386e83c08',
    desc: '删除系统配置',
});
api[0].list[0].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/{id}',
    methodId: '9fd399feaecb48c18b64ea530da9f4ba',
    desc: '获取系统配置详情',
});
api[0].list[0].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/key/{configKey}',
    methodId: '36777205a0b31c52ef1b124b74f78f99',
    desc: '根据配置键获取系统配置详情',
});
api[0].list[0].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs',
    methodId: 'af7e15eeb02c89df6d664539ca739b64',
    desc: '分页查询系统配置列表',
});
api[0].list[0].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/group/{configGroup}',
    methodId: '71dd55ab96331210e51b4373385d2c38',
    desc: '根据配置分组获取系统配置列表',
});
api[0].list[0].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/enabled',
    methodId: 'eedbe4656c24bbe2b9fb60b976e44fa9',
    desc: '获取所有启用的系统配置',
});
api[0].list[0].list.push({
    order: '9',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/system',
    methodId: '446d667435c384cc26c3b3d8f5a111da',
    desc: '获取系统内置配置',
});
api[0].list[0].list.push({
    order: '10',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/user',
    methodId: 'af8cc0eb21e4c6b38fb72910c9120e57',
    desc: '获取用户自定义配置',
});
api[0].list[0].list.push({
    order: '11',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/{id}/enable',
    methodId: '3c1dc8d32181b3badd722bd8953c3e01',
    desc: '启用系统配置',
});
api[0].list[0].list.push({
    order: '12',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/{id}/disable',
    methodId: 'f155a5e98ebd5e2196d988927c8c7516',
    desc: '禁用系统配置',
});
api[0].list[0].list.push({
    order: '13',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/batch/enable',
    methodId: 'ab11608b74f259e9331059a2be700a4c',
    desc: '批量启用系统配置',
});
api[0].list[0].list.push({
    order: '14',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/batch/disable',
    methodId: '6e4c5b34f9d128ccc6026b1d4de97cda',
    desc: '批量禁用系统配置',
});
api[0].list[0].list.push({
    order: '15',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/groups',
    methodId: 'c06543430ac3f9544cb47fb5f8fa349e',
    desc: '获取所有配置分组',
});
api[0].list[0].list.push({
    order: '16',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/check-key',
    methodId: '31d8067be5d8675fe997ea02895244a4',
    desc: '检查配置键是否可用',
});
api[0].list[0].list.push({
    order: '17',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/value/{configKey}',
    methodId: '6f2b3fdf6c603fd96945e380d22b1bd7',
    desc: '根据配置键获取配置值',
});
api[0].list[0].list.push({
    order: '18',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/value/{configKey}',
    methodId: '062f2c16b9afe6d00d53b8995c436dc8',
    desc: '根据配置键设置配置值',
});
api[0].list[0].list.push({
    order: '19',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/values/batch',
    methodId: '7bd7dc08ff9d233e77e02fca390dfa2c',
    desc: '批量获取配置值',
});
api[0].list[0].list.push({
    order: '20',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/values/batch',
    methodId: 'f5d052102cf67685f0a5f2a30ccd69a4',
    desc: '批量设置配置值',
});
api[0].list[0].list.push({
    order: '21',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/system-configs/refresh-cache',
    methodId: 'c5662fc1dd82d728f269e86502e6b793',
    desc: '刷新配置缓存',
});
api[0].list.push({
    alias: 'UserAccountController',
    order: '2',
    link: '用户账户控制器_-_认证相关接口',
    desc: '用户账户控制器 - 认证相关接口',
    list: []
})
api[0].list[1].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/auth/login',
    methodId: '7a6bba06b8b893757e2269789a769386',
    desc: '用户登录 - 唯一允许未认证访问的接口',
});
api[0].list[1].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/auth/logout',
    methodId: '9a9a4827a91fd4e297aad8c45a12c4eb',
    desc: '用户登出 - 需要认证',
});
api[0].list[1].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/auth/user/info',
    methodId: 'c0064bc336ba4ebbbeb2e34b408cc24f',
    desc: '获取当前用户信息 - 需要认证',
});
api[0].list[1].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/auth/health',
    methodId: '89d199d90a10f448d54b103c522eec4a',
    desc: '健康检查 - 允许未认证访问',
});
api[0].list.push({
    alias: 'RoleController',
    order: '3',
    link: '角色管理控制器',
    desc: '角色管理控制器',
    list: []
})
api[0].list[2].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/roles',
    methodId: 'e17084156d4ce118ff973f164e73f4f1',
    desc: '创建角色',
});
api[0].list[2].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/roles/{id}',
    methodId: 'f20fa7daafe25c612940586cce4bcaf5',
    desc: '更新角色',
});
api[0].list[2].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/roles/{id}',
    methodId: 'a4153344f4378a22675137386723b8f1',
    desc: '删除角色',
});
api[0].list[2].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/roles/{id}',
    methodId: '26dde1672137fe9fdfbc92b8ce369ab7',
    desc: '获取角色详情',
});
api[0].list[2].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/roles',
    methodId: 'b43c1e04c3287e4170c9134bccece2b9',
    desc: '分页查询角色列表',
});
api[0].list[2].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/roles/enabled',
    methodId: 'f97ccb7bfc92d16d1b8724440d42b78f',
    desc: '获取所有启用的角色（简单信息）',
});
api[0].list[2].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/roles/{id}/enable',
    methodId: 'd36acab72395ef9abdf0f857be602792',
    desc: '启用角色',
});
api[0].list[2].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/roles/{id}/disable',
    methodId: '340eebcf9dfe57e3a32401862ab83710',
    desc: '禁用角色',
});
api[0].list[2].list.push({
    order: '9',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/roles/{id}/permissions',
    methodId: 'b97d22ff86ec78dc4164dd3261b078c5',
    desc: '为角色分配权限',
});
api[0].list[2].list.push({
    order: '10',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/roles/{id}/permissions',
    methodId: '2154319a1c9809561bb55f678be02915',
    desc: '获取角色的权限列表',
});
api[0].list.push({
    alias: 'ProductLineController',
    order: '4',
    link: '产品线管理控制器',
    desc: '产品线管理控制器',
    list: []
})
api[0].list[3].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines',
    methodId: '4dc9cef9b44cb7348d6251d65afa9df1',
    desc: '创建产品线',
});
api[0].list[3].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/{id}',
    methodId: '56603945d9184ef7744c0526f74e2967',
    desc: '更新产品线',
});
api[0].list[3].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/{id}',
    methodId: '3d79565d8dbddd0ac3239db39fd1c7ab',
    desc: '删除产品线',
});
api[0].list[3].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/{id}',
    methodId: '547ab766a23e565baf1a9ed4ce9bf5f1',
    desc: '获取产品线详情',
});
api[0].list[3].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines',
    methodId: 'ac55bf37a0f3c2d63280edfe0e11e98a',
    desc: '分页查询产品线列表',
});
api[0].list[3].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/enabled',
    methodId: 'fcd0974833b723e2c4f864f63e25ead9',
    desc: '获取所有启用的产品线（简单信息）',
});
api[0].list[3].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/type/{type}',
    methodId: '26b2f6881bddb9070f544895a54aed27',
    desc: '根据产品线类型获取产品线列表',
});
api[0].list[3].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/owner/{ownerId}',
    methodId: '18dce0c3223dce0aa7c3b40af411eb74',
    desc: '根据负责人ID获取产品线列表',
});
api[0].list[3].list.push({
    order: '9',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/user/{userId}',
    methodId: 'ccd83645282e2ac707608e769a3164bb',
    desc: '根据用户ID获取有权限的产品线列表',
});
api[0].list[3].list.push({
    order: '10',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/{id}/enable',
    methodId: '096608c5b3690f6b6783ffd99c286157',
    desc: '启用产品线',
});
api[0].list[3].list.push({
    order: '11',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/{id}/disable',
    methodId: 'f3ed988865dcafe7004b7131a42bdfb9',
    desc: '禁用产品线',
});
api[0].list[3].list.push({
    order: '12',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/batch/enable',
    methodId: '9a71a5b7dd4676783ae2baa726910622',
    desc: '批量启用产品线',
});
api[0].list[3].list.push({
    order: '13',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/batch/disable',
    methodId: 'f23eaf5f6c69ea13d3f5ec7c5ae2bc22',
    desc: '批量禁用产品线',
});
api[0].list[3].list.push({
    order: '14',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/{id}/data-source',
    methodId: 'd8f012d9bc80eaf093f835fb92d9c5ea',
    desc: '更新产品线数据源配置',
});
api[0].list[3].list.push({
    order: '15',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/{id}/data-source',
    methodId: '14a713ca50b36b31e6efb6f4f70a9ddc',
    desc: '获取产品线数据源配置',
});
api[0].list[3].list.push({
    order: '16',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/check-code',
    methodId: '********************************',
    desc: '检查产品线编码是否可用',
});
api[0].list[3].list.push({
    order: '17',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/check-name',
    methodId: '9c606f8dc4563133dde495db6a8b4a40',
    desc: '检查产品线名称是否可用',
});
api[0].list[3].list.push({
    order: '18',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/product-lines/statistics',
    methodId: 'e2b97306bd0110543613473285daa050',
    desc: '获取产品线类型统计信息',
});
api[0].list.push({
    alias: 'PermissionController',
    order: '5',
    link: '权限管理控制器',
    desc: '权限管理控制器',
    list: []
})
api[0].list[4].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/permissions',
    methodId: '37c8683329865b3b777007e07e1d8a65',
    desc: '创建权限',
});
api[0].list[4].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/permissions/{id}',
    methodId: 'f073efcb48667ba7d0d403fc81ce776e',
    desc: '更新权限',
});
api[0].list[4].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/permissions/{id}',
    methodId: 'bac1170ab7fa10f9e32ffe8232fb1b8c',
    desc: '删除权限',
});
api[0].list[4].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/permissions/{id}',
    methodId: '5d26331ec8207ccce8f67588ba742bb3',
    desc: '获取权限详情',
});
api[0].list[4].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/permissions',
    methodId: '34338959e9da29902ee992fae55c44be',
    desc: '分页查询权限列表',
});
api[0].list[4].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/permissions/tree',
    methodId: '0610a63c8a4d22ca152c20bf999fbf61',
    desc: '获取权限树结构',
});
api[0].list[4].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/permissions/parent/{parentId}',
    methodId: 'ef07887bb50d43aeb650d1f12490bdcc',
    desc: '根据父权限ID获取子权限列表',
});
api[0].list[4].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/permissions/type/{permissionType}',
    methodId: 'd310f9aea4f14e0c9bad9f959535f352',
    desc: '根据权限类型获取权限列表',
});
api[0].list[4].list.push({
    order: '9',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/permissions/user/{userId}',
    methodId: '70637f8410ba1492e94cf8e69a6244cb',
    desc: '根据用户ID获取权限列表',
});
api[0].list[4].list.push({
    order: '10',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/permissions/role/{roleId}',
    methodId: 'f20663abfa6fbc7509661efb144dec34',
    desc: '根据角色ID获取权限列表',
});
api[0].list[4].list.push({
    order: '11',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/permissions/{id}/enable',
    methodId: 'a8253d63a1100d64b5d8f488535562ea',
    desc: '启用权限',
});
api[0].list[4].list.push({
    order: '12',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/permissions/{id}/disable',
    methodId: 'ada6ecaadbf564fc9177f189e6a5f8f3',
    desc: '禁用权限',
});
api[0].list[4].list.push({
    order: '13',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/permissions/menu/enabled',
    methodId: '94fe88cc2d44be51af1041928ae9f551',
    desc: '获取所有启用的菜单权限（用于前端菜单构建）',
});
api[0].list[4].list.push({
    order: '14',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/permissions/batch',
    methodId: '637db4d2c1e92675103fb9c3dd3ab9c6',
    desc: '根据权限ID列表获取权限详情',
});
api[0].list.push({
    alias: 'ProductVersionController',
    order: '6',
    link: '产品版本管理控制器',
    desc: '产品版本管理控制器',
    list: []
})
api[0].list[5].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions',
    methodId: 'cafd7fb75de6d1340ae34faaca8fc46d',
    desc: '创建产品版本',
});
api[0].list[5].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/{id}',
    methodId: '4bb2cbcccc989d32e38b34caec444055',
    desc: '更新产品版本',
});
api[0].list[5].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/{id}',
    methodId: '26af25eca08cf141b10595ac0e97800e',
    desc: '删除产品版本',
});
api[0].list[5].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/batch',
    methodId: 'c9d8fed2d6c836e2fdff174e3e3c89cc',
    desc: '批量删除产品版本',
});
api[0].list[5].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/{id}',
    methodId: '973b82651652a5629bcb41b06ae195df',
    desc: '获取产品版本详情',
});
api[0].list[5].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions',
    methodId: '42d6d71748394a172e0606e204937227',
    desc: '分页查询产品版本列表',
});
api[0].list[5].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/by-product-line/{productLineId}',
    methodId: '15e255bf05463a78966613a64bd66046',
    desc: '获取产品线版本列表',
});
api[0].list[5].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/current/{productLineId}',
    methodId: '8966b8dd3ed08950ab7826f37d1b01a8',
    desc: '获取产品线当前版本',
});
api[0].list[5].list.push({
    order: '9',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/{id}/release',
    methodId: 'e530f8cd1bcba81644d5ef8f40c2e23b',
    desc: '发布版本',
});
api[0].list[5].list.push({
    order: '10',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/{id}/set-current',
    methodId: '9c97d2db45c9b29b67c542bee7670c2e',
    desc: '设置当前版本',
});
api[0].list[5].list.push({
    order: '11',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/{id}/deprecate',
    methodId: '5bb3f6d388bf8eef36ac2d0170ed492d',
    desc: '废弃版本',
});
api[0].list[5].list.push({
    order: '12',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/{id}/status',
    methodId: '19765eafe8e7cb092386b2547d27c1bd',
    desc: '更新版本状态',
});
api[0].list[5].list.push({
    order: '13',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/{id}/download',
    methodId: '693da939b51698318a5e209ae8e5d2c5',
    desc: '记录版本下载',
});
api[0].list[5].list.push({
    order: '14',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/{id}/history',
    methodId: '1db00a48f529dbeda1b1044548f6a283',
    desc: '获取版本发布历史',
});
api[0].list[5].list.push({
    order: '15',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/stats/{productLineId}',
    methodId: '5d401d8087841800584a0a3ce5fe49f9',
    desc: '获取版本统计信息',
});
api[0].list[5].list.push({
    order: '16',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/top-downloads',
    methodId: '072f6ab5a2e06d40b255707ca10e79d9',
    desc: '获取热门版本排行',
});
api[0].list[5].list.push({
    order: '17',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/check-version-number',
    methodId: 'd0ed2aa7c2c5e63289d44beaa40caae3',
    desc: '检查版本号可用性',
});
api[0].list[5].list.push({
    order: '18',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/suggest-version-number',
    methodId: 'd15ef010ce1eb7ef2d8d40a9eb9b100a',
    desc: '建议下一个版本号',
});
api[0].list[5].list.push({
    order: '19',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/batch/status',
    methodId: '8ed8ba41ae550dac5547eabac69275c8',
    desc: '批量更新版本状态',
});
api[0].list[5].list.push({
    order: '20',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/{id}/download-stats',
    methodId: '5cfed21a763beef73771ee075f3ed117',
    desc: '获取版本下载统计',
});
api[0].list[5].list.push({
    order: '21',
    deprecated: 'false',
    url: 'http://localhost:8080/api/product-versions/sync/{productLineId}',
    methodId: 'a40d31863302d5a41bf726c8048aec16',
    desc: '同步版本信息',
});
api[0].list.push({
    alias: 'UserManagementController',
    order: '7',
    link: '用户管理控制器_-_管理员专用',
    desc: '用户管理控制器 - 管理员专用',
    list: []
})
api[0].list[6].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/users/create',
    methodId: '71f99489f80f30fb927713d9c74b5b0e',
    desc: '创建用户（管理员专用）  原来的注册接口移到这里，只有管理员可以创建新用户',
});
api[0].list[6].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/users/{userId}/disable',
    methodId: '59e7e5d6e655cbb4247c9b4de3060edc',
    desc: '禁用用户',
});
api[0].list[6].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/users/{userId}/enable',
    methodId: '4c8d14d1598f7e86a7a178ac977265b2',
    desc: '启用用户',
});
api[0].list[6].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/users/{userId}/reset-password',
    methodId: '281a17cfe126adcb9cdcf15b4ef32450',
    desc: '重置用户密码',
});
api[0].list[6].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/users/{userId}',
    methodId: 'c1db4170796b954e79d18abe7aa24e37',
    desc: '获取用户详情',
});
api[0].list[6].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/users',
    methodId: '14a8615f58876a66ddfe06290338e207',
    desc: '获取用户列表',
});
api[0].list.push({
    alias: 'OperationLogController',
    order: '8',
    link: '操作日志管理控制器',
    desc: '操作日志管理控制器',
    list: []
})
api[0].list[7].list.push({
    order: '1',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/{id}',
    methodId: 'eb028cefabd2db39977081bc5fd64b8f',
    desc: '获取操作日志详情',
});
api[0].list[7].list.push({
    order: '2',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs',
    methodId: '263dc2698fb2429eabbd4d31eddf0763',
    desc: '分页查询操作日志列表',
});
api[0].list[7].list.push({
    order: '3',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/user/{userId}',
    methodId: '41fe866300ec2ac7206af2140b2e17c1',
    desc: '获取用户操作日志列表',
});
api[0].list[7].list.push({
    order: '4',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/operation/{operation}',
    methodId: '94bf9952bebee93e4deec6870e31ba4a',
    desc: '根据操作类型获取操作日志列表',
});
api[0].list[7].list.push({
    order: '5',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/failed',
    methodId: '8d09a0a5e1367d234e09b82d3718271c',
    desc: '获取失败操作日志列表',
});
api[0].list[7].list.push({
    order: '6',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/slow',
    methodId: '6163d04e5af5eaeb6e629cbf8604a335',
    desc: '获取慢操作日志列表',
});
api[0].list[7].list.push({
    order: '7',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/sensitive',
    methodId: '326877ff9effd250264f8e5234443d68',
    desc: '获取敏感操作日志列表',
});
api[0].list[7].list.push({
    order: '8',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/ip/{clientIp}',
    methodId: '300a9e940933e55b51c93cb16f7add8c',
    desc: '根据IP地址获取操作日志列表',
});
api[0].list[7].list.push({
    order: '9',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/recent',
    methodId: 'fb2ba60d52fd60c9259835bc2770c942',
    desc: '获取最近操作日志',
});
api[0].list[7].list.push({
    order: '10',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/user/{userId}/recent',
    methodId: '01422164eb2116785cd5de7e569b045d',
    desc: '获取用户最近操作日志',
});
api[0].list[7].list.push({
    order: '11',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/statistics',
    methodId: '2e3b6fb10f1f7d085683765b390f9dec',
    desc: '获取操作日志统计信息',
});
api[0].list[7].list.push({
    order: '12',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/statistics/today',
    methodId: '2c28cb298f25d3a9a66315aa58178a04',
    desc: '获取今日操作统计',
});
api[0].list[7].list.push({
    order: '13',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/statistics/week',
    methodId: '77b163a64724c5ea55534232edd172e0',
    desc: '获取本周操作统计',
});
api[0].list[7].list.push({
    order: '14',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/statistics/month',
    methodId: '5fb5d5e11ad8aca83b8d6a92ce9e5e40',
    desc: '获取本月操作统计',
});
api[0].list[7].list.push({
    order: '15',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/cleanup',
    methodId: 'ad3b785d57b0e4ed1425afa230417ebf',
    desc: '清理过期操作日志',
});
api[0].list[7].list.push({
    order: '16',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/export',
    methodId: '1d8e9a7fff5e0aeeac2d0d55ce5f1747',
    desc: '导出操作日志',
});
api[0].list[7].list.push({
    order: '17',
    deprecated: 'false',
    url: 'http://localhost:8080/admin/operation-logs/audit-report',
    methodId: '7711ce36bb52d93aa451fc22f7248b79',
    desc: '生成审计报告',
});
api.push({
    name: '产品版本管理',
    order: '2',
    list: []
})
document.onkeydown = keyDownSearch;
function keyDownSearch(e) {
    const theEvent = e;
    const code = theEvent.keyCode || theEvent.which || theEvent.charCode;
    if (code === 13) {
        const search = document.getElementById('search');
        const searchValue = search.value.toLocaleLowerCase();

        let searchGroup = [];
        for (let i = 0; i < api.length; i++) {

            let apiGroup = api[i];

            let searchArr = [];
            for (let i = 0; i < apiGroup.list.length; i++) {
                let apiData = apiGroup.list[i];
                const desc = apiData.desc;
                if (desc.toLocaleLowerCase().indexOf(searchValue) > -1) {
                    searchArr.push({
                        order: apiData.order,
                        desc: apiData.desc,
                        link: apiData.link,
                        alias: apiData.alias,
                        list: apiData.list
                    });
                } else {
                    let methodList = apiData.list || [];
                    let methodListTemp = [];
                    for (let j = 0; j < methodList.length; j++) {
                        const methodData = methodList[j];
                        const methodDesc = methodData.desc;
                        if (methodDesc.toLocaleLowerCase().indexOf(searchValue) > -1) {
                            methodListTemp.push(methodData);
                            break;
                        }
                    }
                    if (methodListTemp.length > 0) {
                        const data = {
                            order: apiData.order,
                            desc: apiData.desc,
                            link: apiData.link,
                            alias: apiData.alias,
                            list: methodListTemp
                        };
                        searchArr.push(data);
                    }
                }
            }
            if (apiGroup.name.toLocaleLowerCase().indexOf(searchValue) > -1) {
                searchGroup.push({
                    name: apiGroup.name,
                    order: apiGroup.order,
                    list: searchArr
                });
                continue;
            }
            if (searchArr.length === 0) {
                continue;
            }
            searchGroup.push({
                name: apiGroup.name,
                order: apiGroup.order,
                list: searchArr
            });
        }
        let html;
        if (searchValue === '') {
            const liClass = "";
            const display = "display: none";
            html = buildAccordion(api,liClass,display);
            document.getElementById('accordion').innerHTML = html;
        } else {
            const liClass = "open";
            const display = "display: block";
            html = buildAccordion(searchGroup,liClass,display);
            document.getElementById('accordion').innerHTML = html;
        }
        const Accordion = function (el, multiple) {
            this.el = el || {};
            this.multiple = multiple || false;
            const links = this.el.find('.dd');
            links.on('click', {el: this.el, multiple: this.multiple}, this.dropdown);
        };
        Accordion.prototype.dropdown = function (e) {
            const $el = e.data.el;
            let $this = $(this), $next = $this.next();
            $next.slideToggle();
            $this.parent().toggleClass('open');
            if (!e.data.multiple) {
                $el.find('.submenu').not($next).slideUp("20").parent().removeClass('open');
            }
        };
        new Accordion($('#accordion'), false);
    }
}

function buildAccordion(apiGroups, liClass, display) {
    let html = "";
    if (apiGroups.length > 0) {
        if (apiDocListSize === 1) {
            let apiData = apiGroups[0].list;
            let order = apiGroups[0].order;
            for (let j = 0; j < apiData.length; j++) {
                html += '<li class="'+liClass+'">';
                html += '<a class="dd" href="#' + apiData[j].alias + '">' + apiData[j].order + '.&nbsp;' + apiData[j].desc + '</a>';
                html += '<ul class="sectlevel2" style="'+display+'">';
                let doc = apiData[j].list;
                for (let m = 0; m < doc.length; m++) {
                    let spanString;
                    if (doc[m].deprecated === 'true') {
                        spanString='<span class="line-through">';
                    } else {
                        spanString='<span>';
                    }
                    html += '<li><a href="#' + doc[m].methodId + '">' + apiData[j].order + '.' + doc[m].order + '.&nbsp;' + spanString + doc[m].desc + '<span></a> </li>';
                }
                html += '</ul>';
                html += '</li>';
            }
        } else {
            for (let i = 0; i < apiGroups.length; i++) {
                let apiGroup = apiGroups[i];
                html += '<li class="'+liClass+'">';
                html += '<a class="dd" href="#_'+apiGroup.order+'_' + apiGroup.name + '">' + apiGroup.order + '.&nbsp;' + apiGroup.name + '</a>';
                html += '<ul class="sectlevel1">';

                let apiData = apiGroup.list;
                for (let j = 0; j < apiData.length; j++) {
                    html += '<li class="'+liClass+'">';
                    html += '<a class="dd" href="#' + apiData[j].alias + '">' +apiGroup.order+'.'+ apiData[j].order + '.&nbsp;' + apiData[j].desc + '</a>';
                    html += '<ul class="sectlevel2" style="'+display+'">';
                    let doc = apiData[j].list;
                    for (let m = 0; m < doc.length; m++) {
                       let spanString;
                       if (doc[m].deprecated === 'true') {
                           spanString='<span class="line-through">';
                       } else {
                           spanString='<span>';
                       }
                       html += '<li><a href="#' + doc[m].methodId + '">'+apiGroup.order+'.' + apiData[j].order + '.' + doc[m].order + '.&nbsp;' + spanString + doc[m].desc + '<span></a> </li>';
                   }
                    html += '</ul>';
                    html += '</li>';
                }

                html += '</ul>';
                html += '</li>';
            }
        }
    }
    return html;
}