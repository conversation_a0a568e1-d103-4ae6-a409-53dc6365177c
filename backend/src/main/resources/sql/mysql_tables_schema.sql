-- =============================================
-- SCRM-Next MySQL 数据库表结构文件
-- 创建时间：2025-07-02
-- 说明：包含所有MySQL业务表的结构定义（不含数据）
-- 版本：v1.0
-- 注意：开发阶段，直接删除旧表重新创建
-- =============================================

-- 数据库的创建和选择由执行脚本处理

-- =============================================
-- 核心系统表
-- =============================================

-- =============================================
-- 1. 系统用户表
-- 用途：存储系统用户基本信息
-- =============================================
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`
(
    `id`              bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `username`        varchar(50)  NOT NULL COMMENT '用户名',
    `password`        varchar(100) NOT NULL COMMENT '密码',
    `real_name`       varchar(50)           DEFAULT NULL COMMENT '真实姓名',
    `email`           varchar(100)          DEFAULT NULL COMMENT '邮箱',
    `phone`           varchar(20)           DEFAULT NULL COMMENT '手机号',
    `avatar`          varchar(200)          DEFAULT NULL COMMENT '头像URL',
    `status`          tinyint      NOT NULL DEFAULT '1' COMMENT '用户状态：0-禁用，1-启用',
    `user_type`       tinyint      NOT NULL DEFAULT '2' COMMENT '用户类型：1-管理员，2-普通用户',
    `dept_id`         bigint                DEFAULT NULL COMMENT '部门ID',
    `last_login_time` datetime              DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip`   varchar(50)           DEFAULT NULL COMMENT '最后登录IP',
    `remark`          varchar(500)          DEFAULT NULL COMMENT '备注',
    `create_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`       bigint                DEFAULT NULL COMMENT '创建人ID',
    `update_by`       bigint                DEFAULT NULL COMMENT '更新人ID',
    `deleted`         tinyint      NOT NULL DEFAULT '0' COMMENT '逻辑删除标志：0-未删除，1-已删除',
    `version`         int          NOT NULL DEFAULT '0' COMMENT '版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    KEY               `idx_email` (`email`),
    KEY               `idx_phone` (`phone`),
    KEY               `idx_status` (`status`),
    KEY               `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统用户表';

-- =============================================
-- 2. 系统角色表
-- 用途：存储系统角色信息
-- =============================================
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`
(
    `id`          bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `role_name`   varchar(50) NOT NULL COMMENT '角色名称',
    `role_code`   varchar(50) NOT NULL COMMENT '角色编码',
    `description` varchar(200)         DEFAULT NULL COMMENT '角色描述',
    `status`      tinyint     NOT NULL DEFAULT '1' COMMENT '角色状态：0-禁用，1-启用',
    `sort_order`  int         NOT NULL DEFAULT '0' COMMENT '排序号',
    `create_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`   bigint               DEFAULT NULL COMMENT '创建人ID',
    `update_by`   bigint               DEFAULT NULL COMMENT '更新人ID',
    `deleted`     tinyint     NOT NULL DEFAULT '0' COMMENT '逻辑删除标志：0-未删除，1-已删除',
    `version`     int         NOT NULL DEFAULT '0' COMMENT '版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_code` (`role_code`),
    KEY           `idx_status` (`status`),
    KEY           `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统角色表';

-- =============================================
-- 3. 系统权限表
-- 用途：存储系统权限信息
-- =============================================
DROP TABLE IF EXISTS `sys_permission`;
CREATE TABLE `sys_permission`
(
    `id`              bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `permission_name` varchar(50)  NOT NULL COMMENT '权限名称',
    `permission_code` varchar(100) NOT NULL COMMENT '权限编码',
    `permission_type` tinyint      NOT NULL DEFAULT '1' COMMENT '权限类型：1-菜单，2-按钮，3-接口',
    `parent_id`       bigint       NOT NULL DEFAULT '0' COMMENT '父权限ID，0表示根权限',
    `path`            varchar(200)          DEFAULT NULL COMMENT '路径',
    `component`       varchar(200)          DEFAULT NULL COMMENT '组件',
    `icon`            varchar(100)          DEFAULT NULL COMMENT '图标',
    `sort_order`      int          NOT NULL DEFAULT '0' COMMENT '排序号',
    `status`          tinyint      NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `create_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`       bigint                DEFAULT NULL COMMENT '创建人ID',
    `update_by`       bigint                DEFAULT NULL COMMENT '更新人ID',
    `deleted`         tinyint      NOT NULL DEFAULT '0' COMMENT '逻辑删除标志：0-未删除，1-已删除',
    `version`         int          NOT NULL DEFAULT '0' COMMENT '版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permission_code` (`permission_code`),
    KEY               `idx_parent_id` (`parent_id`),
    KEY               `idx_status` (`status`),
    KEY               `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统权限表';

-- =============================================
-- 4. 用户角色关联表
-- 用途：存储用户和角色的关联关系
-- =============================================
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`
(
    `id`          bigint   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id`     bigint   NOT NULL COMMENT '用户ID',
    `role_id`     bigint   NOT NULL COMMENT '角色ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`   bigint            DEFAULT NULL COMMENT '创建人ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
    KEY           `idx_user_id` (`user_id`),
    KEY           `idx_role_id` (`role_id`),
    CONSTRAINT `fk_sys_user_role_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`),
    CONSTRAINT `fk_sys_user_role_role` FOREIGN KEY (`role_id`) REFERENCES `sys_role` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- =============================================
-- 5. 角色权限关联表
-- 用途：存储角色和权限的关联关系
-- =============================================
DROP TABLE IF EXISTS `sys_role_permission`;
CREATE TABLE `sys_role_permission`
(
    `id`            bigint   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `role_id`       bigint   NOT NULL COMMENT '角色ID',
    `permission_id` bigint   NOT NULL COMMENT '权限ID',
    `create_time`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`     bigint            DEFAULT NULL COMMENT '创建人ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
    KEY             `idx_role_id` (`role_id`),
    KEY             `idx_permission_id` (`permission_id`),
    CONSTRAINT `fk_sys_role_permission_role` FOREIGN KEY (`role_id`) REFERENCES `sys_role` (`id`),
    CONSTRAINT `fk_sys_role_permission_permission` FOREIGN KEY (`permission_id`) REFERENCES `sys_permission` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- =============================================
-- 6. 系统操作日志表
-- 用途：记录系统操作日志
-- =============================================
DROP TABLE IF EXISTS `sys_operation_log`;
CREATE TABLE `sys_operation_log`
(
    `id`           bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id`      bigint                DEFAULT NULL COMMENT '操作用户ID',
    `username`     varchar(50)           DEFAULT NULL COMMENT '操作用户名',
    `operation`    varchar(100) NOT NULL COMMENT '操作类型',
    `method`       varchar(200) NOT NULL COMMENT '请求方法',
    `params`       text COMMENT '请求参数',
    `result`       text COMMENT '返回结果',
    `ip`           varchar(50)           DEFAULT NULL COMMENT 'IP地址',
    `location`     varchar(100)          DEFAULT NULL COMMENT '操作地点',
    `user_agent`   varchar(500)          DEFAULT NULL COMMENT '用户代理',
    `execute_time` bigint                DEFAULT NULL COMMENT '执行时间(毫秒)',
    `status`       tinyint      NOT NULL DEFAULT '1' COMMENT '操作状态：0-失败，1-成功',
    `error_msg`    text COMMENT '错误信息',
    `create_time`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY            `idx_user_id` (`user_id`),
    KEY            `idx_username` (`username`),
    KEY            `idx_operation` (`operation`),
    KEY            `idx_status` (`status`),
    KEY            `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统操作日志表';

-- =============================================
-- 7. 系统配置表
-- 用途：存储系统配置信息
-- =============================================
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`
(
    `id`           bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_name`  varchar(100) NOT NULL COMMENT '配置名称',
    `config_key`   varchar(100) NOT NULL COMMENT '配置键',
    `config_value` text COMMENT '配置值',
    `config_type`  tinyint      NOT NULL DEFAULT '1' COMMENT '配置类型：1-字符串，2-数字，3-布尔值，4-JSON',
    `config_group` varchar(100)          DEFAULT NULL COMMENT '配置分组',
    `description`  varchar(500)          DEFAULT NULL COMMENT '配置描述',
    `is_system`    tinyint      NOT NULL DEFAULT '0' COMMENT '是否系统配置：0-否，1-是',
    `status`       tinyint      NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `sort_order`   int          NOT NULL DEFAULT '0' COMMENT '排序号',
    `remark`       varchar(500)          DEFAULT NULL COMMENT '备注',
    `create_time`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`    bigint                DEFAULT NULL COMMENT '创建人ID',
    `update_by`    bigint                DEFAULT NULL COMMENT '更新人ID',
    `deleted`      tinyint      NOT NULL DEFAULT '0' COMMENT '逻辑删除标志：0-未删除，1-已删除',
    `version`      int          NOT NULL DEFAULT '0' COMMENT '版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_key` (`config_key`),
    KEY            `idx_config_type` (`config_type`),
    KEY            `idx_status` (`status`),
    KEY            `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- =============================================
-- 8. 产品线表
-- 用途：存储产品线信息
-- =============================================
DROP TABLE IF EXISTS `product_line`;
CREATE TABLE `product_line`
(
    `id`                 bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name`               varchar(100) NOT NULL COMMENT '产品线名称',
    `code`               varchar(50)  NOT NULL COMMENT '产品线编码',
    `type`               tinyint      NOT NULL COMMENT '产品线类型：1-阅读器，2-编辑器，3-云服务，4-工具类，5-内容平台',
    `description`        varchar(500)          DEFAULT NULL COMMENT '产品线描述',
    `logo`               varchar(200)          DEFAULT NULL COMMENT '产品线Logo',
    `icon`               varchar(255)          DEFAULT NULL COMMENT '产品线图标',
    `color`              varchar(50)           DEFAULT NULL COMMENT '产品线颜色',
    `website`            varchar(200)          DEFAULT NULL COMMENT '官网地址',
    `owner_id`           bigint                DEFAULT NULL COMMENT '负责人ID',
    `owner_name`         varchar(100)          DEFAULT NULL COMMENT '负责人名称',
    `data_source_config` json                  DEFAULT NULL COMMENT '数据源配置',
    `remark`             varchar(500)          DEFAULT NULL COMMENT '备注',
    `status`             tinyint      NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `sort_order`         int          NOT NULL DEFAULT '0' COMMENT '排序号',
    `create_time`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`          bigint                DEFAULT NULL COMMENT '创建人ID',
    `update_by`          bigint                DEFAULT NULL COMMENT '更新人ID',
    `deleted`            tinyint      NOT NULL DEFAULT '0' COMMENT '逻辑删除标志：0-未删除，1-已删除',
    `version`            int          NOT NULL DEFAULT '0' COMMENT '版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`),
    KEY                  `idx_type` (`type`),
    KEY                  `idx_status` (`status`),
    KEY                  `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品线表';

-- =============================================
-- 9. 产品版本表
-- 用途：存储产品版本信息
-- =============================================
DROP TABLE IF EXISTS `product_version`;
CREATE TABLE `product_version`
(
    `id`              bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_line_id` bigint      NOT NULL COMMENT '产品线ID',
    `version_number`  varchar(50) NOT NULL COMMENT '版本号',
    `version_name`    varchar(100)         DEFAULT NULL COMMENT '版本名称',
    `description`     text COMMENT '版本描述',
    `release_notes`   text COMMENT '发布说明',
    `download_url`    varchar(500)         DEFAULT NULL COMMENT '下载地址',
    `file_size`       bigint               DEFAULT NULL COMMENT '文件大小(字节)',
    `file_hash`       varchar(100)         DEFAULT NULL COMMENT '文件哈希值',
    `is_latest`       tinyint     NOT NULL DEFAULT '0' COMMENT '是否最新版本：0-否，1-是',
    `is_stable`       tinyint     NOT NULL DEFAULT '1' COMMENT '是否稳定版本：0-否，1-是',
    `release_time`    datetime             DEFAULT NULL COMMENT '发布时间',
    `status`          tinyint     NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `create_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`       bigint               DEFAULT NULL COMMENT '创建人ID',
    `update_by`       bigint               DEFAULT NULL COMMENT '更新人ID',
    `deleted`         tinyint     NOT NULL DEFAULT '0' COMMENT '逻辑删除标志：0-未删除，1-已删除',
    `version`         int         NOT NULL DEFAULT '0' COMMENT '版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_product_version` (`product_line_id`, `version_number`),
    KEY               `idx_product_line_id` (`product_line_id`),
    KEY               `idx_is_latest` (`is_latest`),
    KEY               `idx_is_stable` (`is_stable`),
    KEY               `idx_release_time` (`release_time`),
    KEY               `idx_status` (`status`),
    CONSTRAINT `fk_product_version_product_line` FOREIGN KEY (`product_line_id`) REFERENCES `product_line` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品版本表';

-- =============================================
-- 业务扩展表
-- =============================================

-- =============================================
-- 10. 用户登录记录表
-- 用途：记录用户登录登出行为，用于登录行为分析
-- =============================================
DROP TABLE IF EXISTS `user_login_records`;
CREATE TABLE `user_login_records`
(
    `id`               bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id`          bigint      NOT NULL COMMENT '用户ID',
    `username`         varchar(50) NOT NULL COMMENT '用户名',
    `login_time`       datetime    NOT NULL COMMENT '登录时间',
    `logout_time`      datetime             DEFAULT NULL COMMENT '登出时间',
    `session_duration` bigint               DEFAULT NULL COMMENT '会话时长(毫秒)',
    `ip_address`       varchar(50)          DEFAULT NULL COMMENT 'IP地址',
    `location`         varchar(100)         DEFAULT NULL COMMENT '登录地点',
    `user_agent`       varchar(500)         DEFAULT NULL COMMENT '用户代理',
    `device_type`      varchar(50)          DEFAULT NULL COMMENT '设备类型：desktop, mobile, tablet',
    `platform`         varchar(50)          DEFAULT NULL COMMENT '平台：windows, macos, android, ios',
    `browser`          varchar(100)         DEFAULT NULL COMMENT '浏览器类型',
    `session_id`       varchar(100)         DEFAULT NULL COMMENT '会话ID',
    `login_status`     tinyint     NOT NULL DEFAULT '1' COMMENT '登录状态：1-成功，0-失败',
    `login_type`       tinyint     NOT NULL DEFAULT '1' COMMENT '登录类型：1-账号密码，2-第三方登录',
    `failure_reason`   varchar(200)         DEFAULT NULL COMMENT '登录失败原因',
    `create_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY                `idx_user_id` (`user_id`),
    KEY                `idx_username` (`username`),
    KEY                `idx_login_time` (`login_time`),
    KEY                `idx_session_id` (`session_id`),
    KEY                `idx_ip_address` (`ip_address`),
    KEY                `idx_login_status` (`login_status`),
    KEY                `idx_user_login_time` (`user_id`, `login_time`),
    KEY                `idx_login_time_status` (`login_time`, `login_status`),
    KEY                `idx_device_platform` (`device_type`, `platform`),
    CONSTRAINT `fk_user_login_records_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户登录记录表';

-- =============================================
-- 11. 用户分群表
-- 用途：存储用户分群配置和规则
-- =============================================
DROP TABLE IF EXISTS `user_segments`;
CREATE TABLE `user_segments`
(
    `id`                 bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `segment_name`       varchar(100) NOT NULL COMMENT '分群名称',
    `segment_code`       varchar(50)  NOT NULL COMMENT '分群编码',
    `description`        varchar(500)          DEFAULT NULL COMMENT '分群描述',
    `segment_type`       tinyint      NOT NULL DEFAULT '1' COMMENT '分群类型：1-静态分群，2-动态分群',
    `segment_rules`      json COMMENT '分群规则JSON',
    `user_count`         int          NOT NULL DEFAULT '0' COMMENT '用户数量',
    `last_calculated`    datetime              DEFAULT NULL COMMENT '最后计算时间',
    `calculation_status` tinyint      NOT NULL DEFAULT '0' COMMENT '计算状态：0-未计算，1-计算中，2-计算完成，3-计算失败',
    `status`             tinyint      NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `sort_order`         int          NOT NULL DEFAULT '0' COMMENT '排序号',
    `create_time`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`          bigint                DEFAULT NULL COMMENT '创建人ID',
    `update_by`          bigint                DEFAULT NULL COMMENT '更新人ID',
    `deleted`            tinyint      NOT NULL DEFAULT '0' COMMENT '逻辑删除标志：0-未删除，1-已删除',
    `version`            int          NOT NULL DEFAULT '0' COMMENT '版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_segment_code` (`segment_code`),
    KEY                  `idx_segment_type` (`segment_type`),
    KEY                  `idx_status` (`status`),
    KEY                  `idx_create_time` (`create_time`),
    KEY                  `idx_type_status` (`segment_type`, `status`),
    KEY                  `idx_calculation_status` (`calculation_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户分群表';

-- =============================================
-- 12. 用户分群成员表
-- 用途：存储用户分群的成员关系
-- =============================================
DROP TABLE IF EXISTS `user_segment_members`;
CREATE TABLE `user_segment_members`
(
    `id`          bigint   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `segment_id`  bigint   NOT NULL COMMENT '分群ID',
    `user_id`     bigint   NOT NULL COMMENT '用户ID',
    `join_time`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    `leave_time`  datetime          DEFAULT NULL COMMENT '离开时间',
    `status`      tinyint  NOT NULL DEFAULT '1' COMMENT '状态：1-活跃，0-已离开',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_segment_user` (`segment_id`, `user_id`),
    KEY           `idx_segment_id` (`segment_id`),
    KEY           `idx_user_id` (`user_id`),
    KEY           `idx_status` (`status`),
    KEY           `idx_segment_status` (`segment_id`, `status`),
    KEY           `idx_user_status` (`user_id`, `status`),
    CONSTRAINT `fk_user_segment_members_segment` FOREIGN KEY (`segment_id`) REFERENCES `user_segments` (`id`),
    CONSTRAINT `fk_user_segment_members_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户分群成员表';

-- =============================================
-- 13. 产品使用统计表
-- 用途：存储产品线级别的使用统计数据
-- =============================================
DROP TABLE IF EXISTS `product_usage_stats`;
CREATE TABLE `product_usage_stats`
(
    `id`                   bigint   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_line_id`      bigint   NOT NULL COMMENT '产品线ID',
    `stat_date`            date     NOT NULL COMMENT '统计日期',
    `total_users`          int      NOT NULL DEFAULT '0' COMMENT '总用户数',
    `active_users`         int      NOT NULL DEFAULT '0' COMMENT '活跃用户数',
    `new_users`            int      NOT NULL DEFAULT '0' COMMENT '新增用户数',
    `returning_users`      int      NOT NULL DEFAULT '0' COMMENT '回访用户数',
    `sessions`             int      NOT NULL DEFAULT '0' COMMENT '会话数',
    `page_views`           bigint   NOT NULL DEFAULT '0' COMMENT '页面浏览数',
    `avg_session_duration` decimal(10, 2)    DEFAULT NULL COMMENT '平均会话时长(分钟)',
    `bounce_rate`          decimal(5, 2)     DEFAULT NULL COMMENT '跳出率(%)',
    `conversion_rate`      decimal(5, 2)     DEFAULT NULL COMMENT '转化率(%)',
    `revenue`              decimal(15, 2)    DEFAULT NULL COMMENT '收入',
    `create_time`          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_product_date` (`product_line_id`, `stat_date`),
    KEY                    `idx_stat_date` (`stat_date`),
    KEY                    `idx_product_line_id` (`product_line_id`),
    KEY                    `idx_date_range` (`stat_date`, `product_line_id`),
    CONSTRAINT `fk_product_usage_stats_product_line` FOREIGN KEY (`product_line_id`) REFERENCES `product_line` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品使用统计表';

-- =============================================
-- 14. 数据权限配置表
-- 用途：配置用户的数据访问权限
-- =============================================
DROP TABLE IF EXISTS `data_permission_config`;
CREATE TABLE `data_permission_config`
(
    `id`               bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id`          bigint       NOT NULL COMMENT '用户ID',
    `resource_type`    varchar(50)  NOT NULL COMMENT '资源类型：product_line, user_data, report等',
    `resource_id`      varchar(100) NOT NULL COMMENT '资源ID',
    `permission_type`  varchar(50)  NOT NULL COMMENT '权限类型：read, write, delete, export等',
    `permission_scope` varchar(50)  NOT NULL COMMENT '权限范围：all, own, department, custom',
    `scope_conditions` json COMMENT '范围条件JSON',
    `status`           tinyint      NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `expire_time`      datetime              DEFAULT NULL COMMENT '过期时间',
    `create_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`        bigint                DEFAULT NULL COMMENT '创建人ID',
    `update_by`        bigint                DEFAULT NULL COMMENT '更新人ID',
    `deleted`          tinyint      NOT NULL DEFAULT '0' COMMENT '逻辑删除标志：0-未删除，1-已删除',
    `version`          int          NOT NULL DEFAULT '0' COMMENT '版本号',
    PRIMARY KEY (`id`),
    KEY                `idx_user_id` (`user_id`),
    KEY                `idx_resource_type` (`resource_type`),
    KEY                `idx_resource_id` (`resource_id`),
    KEY                `idx_permission_type` (`permission_type`),
    KEY                `idx_status` (`status`),
    KEY                `idx_user_resource` (`user_id`, `resource_type`, `resource_id`),
    KEY                `idx_resource_permission` (`resource_type`, `permission_type`),
    CONSTRAINT `fk_data_permission_config_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据权限配置表';

-- =============================================
-- 15. 功能配置表
-- 用途：配置系统功能的开关和参数
-- =============================================
DROP TABLE IF EXISTS `feature_config`;
CREATE TABLE `feature_config`
(
    `id`              bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `feature_code`    varchar(100) NOT NULL COMMENT '功能编码',
    `feature_name`    varchar(100) NOT NULL COMMENT '功能名称',
    `feature_type`    varchar(50)  NOT NULL COMMENT '功能类型：analysis, report, export等',
    `product_line_id` bigint                DEFAULT NULL COMMENT '产品线ID，NULL表示全局功能',
    `is_enabled`      tinyint      NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
    `config_params`   json COMMENT '配置参数JSON',
    `access_roles`    json COMMENT '可访问角色列表',
    `description`     varchar(500)          DEFAULT NULL COMMENT '功能描述',
    `sort_order`      int          NOT NULL DEFAULT '0' COMMENT '排序号',
    `create_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`       bigint                DEFAULT NULL COMMENT '创建人ID',
    `update_by`       bigint                DEFAULT NULL COMMENT '更新人ID',
    `deleted`         tinyint      NOT NULL DEFAULT '0' COMMENT '逻辑删除标志：0-未删除，1-已删除',
    `version`         int          NOT NULL DEFAULT '0' COMMENT '版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_feature_code_product` (`feature_code`, `product_line_id`),
    KEY               `idx_feature_type` (`feature_type`),
    KEY               `idx_product_line_id` (`product_line_id`),
    KEY               `idx_is_enabled` (`is_enabled`),
    KEY               `idx_type_enabled` (`feature_type`, `is_enabled`),
    KEY               `idx_product_enabled` (`product_line_id`, `is_enabled`),
    CONSTRAINT `fk_feature_config_product_line` FOREIGN KEY (`product_line_id`) REFERENCES `product_line` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='功能配置表';


-- =============================================
-- 16. 初始化管理员用户
-- =============================================
INSERT INTO `sys_user` (`id`, `username`, `password`, `real_name`, `email`, `user_type`, `status`, `create_time`,
                        `update_time`)
VALUES (1, 'admin', '$2a$10$R8DIL4Aig2rD8svSNfdOGeRjavbOjnTLjgmIaUiOR1j8RkFaEC91S', '系统管理员', '<EMAIL>', 1,
        1, NOW(), NOW());


-- =============================================
-- 活动分析相关表
-- =============================================

-- 营销活动表
DROP TABLE IF EXISTS `marketing_campaigns`;
CREATE TABLE `marketing_campaigns` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '活动ID',
    `campaign_name` VARCHAR(255) NOT NULL COMMENT '活动名称',
    `campaign_type` VARCHAR(50) NOT NULL COMMENT '活动类型：promotion-促销活动, brand-品牌推广, product-产品发布, user-用户活动, holiday-节日营销',
    `campaign_description` TEXT COMMENT '活动描述',
    `status` VARCHAR(20) NOT NULL DEFAULT 'draft' COMMENT '活动状态：draft-草稿, active-进行中, paused-暂停, completed-已完成, cancelled-已取消',
    `budget` DECIMAL(15,2) COMMENT '活动预算',
    `actual_cost` DECIMAL(15,2) DEFAULT 0 COMMENT '实际花费',
    `target_audience` TEXT COMMENT '目标受众描述',
    `channels` JSON COMMENT '投放渠道：["email", "sms", "push", "social", "ads"]',
    `start_time` DATETIME COMMENT '活动开始时间',
    `end_time` DATETIME COMMENT '活动结束时间',
    `created_by` BIGINT COMMENT '创建人ID',
    `updated_by` BIGINT COMMENT '更新人ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    `version` INT DEFAULT 0 COMMENT '版本号',

    INDEX `idx_campaign_type` (`campaign_type`),
    INDEX `idx_status` (`status`),
    INDEX `idx_start_time` (`start_time`),
    INDEX `idx_end_time` (`end_time`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='营销活动表';

-- 活动效果统计表
DROP TABLE IF EXISTS `campaign_statistics`;
CREATE TABLE `campaign_statistics` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计ID',
    `campaign_id` BIGINT NOT NULL COMMENT '活动ID',
    `stat_date` DATE NOT NULL COMMENT '统计日期',
    `impressions` BIGINT DEFAULT 0 COMMENT '曝光量',
    `clicks` BIGINT DEFAULT 0 COMMENT '点击量',
    `conversions` BIGINT DEFAULT 0 COMMENT '转化数',
    `participants` BIGINT DEFAULT 0 COMMENT '参与人数',
    `new_users` BIGINT DEFAULT 0 COMMENT '新增用户数',
    `revenue` DECIMAL(15,2) DEFAULT 0 COMMENT '收入',
    `cost` DECIMAL(15,2) DEFAULT 0 COMMENT '当日花费',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY `uk_campaign_date` (`campaign_id`, `stat_date`),
    INDEX `idx_campaign_id` (`campaign_id`),
    INDEX `idx_stat_date` (`stat_date`),
    FOREIGN KEY (`campaign_id`) REFERENCES `marketing_campaigns`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动效果统计表';

-- =============================================
-- 粉丝分析相关表
-- =============================================

-- 粉丝基础信息表
DROP TABLE IF EXISTS `fans`;
CREATE TABLE `fans` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '粉丝ID',
    `fan_id` VARCHAR(100) NOT NULL UNIQUE COMMENT '粉丝唯一标识',
    `nickname` VARCHAR(255) NOT NULL COMMENT '昵称',
    `avatar_url` VARCHAR(500) COMMENT '头像URL',
    `platform` VARCHAR(50) NOT NULL COMMENT '平台：wechat-微信, weibo-微博, douyin-抖音, xiaohongshu-小红书, official-官方',
    `platform_user_id` VARCHAR(255) COMMENT '平台用户ID',
    `gender` TINYINT COMMENT '性别：0-未知, 1-男, 2-女',
    `age_range` VARCHAR(20) COMMENT '年龄段：18-25, 26-35, 36-45, 46-55, 55+',
    `region` VARCHAR(100) COMMENT '地区',
    `city` VARCHAR(100) COMMENT '城市',
    `follow_time` DATETIME NOT NULL COMMENT '关注时间',
    `unfollow_time` DATETIME COMMENT '取消关注时间',
    `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态：active-活跃, inactive-不活跃, unfollowed-已取关',
    `source` VARCHAR(50) COMMENT '来源：search-搜索, recommend-推荐, share-分享, ads-广告, direct-直接',
    `fan_type` VARCHAR(50) COMMENT '粉丝类型：potential-潜在, loyal-忠实, vip-VIP, ordinary-普通',
    `fan_value` VARCHAR(20) COMMENT '粉丝价值：high-高价值, medium-中等价值, low-低价值',
    `activity_level` INT DEFAULT 0 COMMENT '活跃度评分(0-100)',
    `interaction_count` BIGINT DEFAULT 0 COMMENT '互动次数',
    `last_interaction_time` DATETIME COMMENT '最后互动时间',
    `tags` JSON COMMENT '标签列表',
    `remark` TEXT COMMENT '备注',
    `created_by` BIGINT COMMENT '创建人ID',
    `updated_by` BIGINT COMMENT '更新人ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    `version` INT DEFAULT 0 COMMENT '版本号',

    INDEX `idx_fan_id` (`fan_id`),
    INDEX `idx_platform` (`platform`),
    INDEX `idx_status` (`status`),
    INDEX `idx_follow_time` (`follow_time`),
    INDEX `idx_source` (`source`),
    INDEX `idx_fan_type` (`fan_type`),
    INDEX `idx_fan_value` (`fan_value`),
    INDEX `idx_activity_level` (`activity_level`),
    INDEX `idx_region` (`region`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='粉丝基础信息表';

-- 粉丝互动记录表
DROP TABLE IF EXISTS `fan_interactions`;
CREATE TABLE `fan_interactions` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '互动ID',
    `fan_id` BIGINT NOT NULL COMMENT '粉丝ID',
    `interaction_type` VARCHAR(50) NOT NULL COMMENT '互动类型：like-点赞, comment-评论, share-分享, message-私信, view-浏览',
    `interaction_date` DATE NOT NULL COMMENT '互动日期',
    `interaction_time` DATETIME NOT NULL COMMENT '互动时间',
    `content_id` VARCHAR(255) COMMENT '内容ID',
    `content_type` VARCHAR(50) COMMENT '内容类型：post-帖子, video-视频, article-文章, product-产品',
    `interaction_value` INT DEFAULT 1 COMMENT '互动价值权重',
    `platform` VARCHAR(50) NOT NULL COMMENT '平台',
    `device_type` VARCHAR(50) COMMENT '设备类型：mobile-手机, desktop-桌面, tablet-平板',
    `ip_address` VARCHAR(45) COMMENT 'IP地址',
    `user_agent` TEXT COMMENT '用户代理',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX `idx_fan_id` (`fan_id`),
    INDEX `idx_interaction_type` (`interaction_type`),
    INDEX `idx_interaction_date` (`interaction_date`),
    INDEX `idx_interaction_time` (`interaction_time`),
    INDEX `idx_platform` (`platform`),
    INDEX `idx_content_type` (`content_type`),
    FOREIGN KEY (`fan_id`) REFERENCES `fans`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='粉丝互动记录表';

-- 粉丝统计表
DROP TABLE IF EXISTS `fan_statistics`;
CREATE TABLE `fan_statistics` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计ID',
    `stat_date` DATE NOT NULL COMMENT '统计日期',
    `platform` VARCHAR(50) NOT NULL COMMENT '平台',
    `total_fans` BIGINT DEFAULT 0 COMMENT '总粉丝数',
    `new_fans` BIGINT DEFAULT 0 COMMENT '新增粉丝数',
    `unfollowed_fans` BIGINT DEFAULT 0 COMMENT '取关粉丝数',
    `active_fans` BIGINT DEFAULT 0 COMMENT '活跃粉丝数',
    `high_value_fans` BIGINT DEFAULT 0 COMMENT '高价值粉丝数',
    `medium_value_fans` BIGINT DEFAULT 0 COMMENT '中等价值粉丝数',
    `low_value_fans` BIGINT DEFAULT 0 COMMENT '低价值粉丝数',
    `total_interactions` BIGINT DEFAULT 0 COMMENT '总互动数',
    `avg_activity_level` DECIMAL(5,2) DEFAULT 0 COMMENT '平均活跃度',
    `retention_rate` DECIMAL(5,2) DEFAULT 0 COMMENT '留存率(%)',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY `uk_date_platform` (`stat_date`, `platform`),
    INDEX `idx_stat_date` (`stat_date`),
    INDEX `idx_platform` (`platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='粉丝统计表';

-- 完成表结构创建
SELECT 'MySQL tables schema created successfully!' as status;
