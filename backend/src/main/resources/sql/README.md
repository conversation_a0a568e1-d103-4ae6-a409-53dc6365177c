# SCRM-Next SQL 文件说明

## 📁 文件结构

本目录包含 4 个核心 SQL 文件，用于 SCRM-Next 项目的数据库初始化：

```
sql/
├── mysql_tables_schema.sql          # MySQL表结构文件（纯结构）
├── mysql_mock_data.sql              # MySQL模拟数据文件
├── clickhouse_tables_schema.sql     # ClickHouse表结构文件（纯结构）
└── clickhouse_mock_data.sql         # ClickHouse模拟数据文件
```

## 🔧 数据库配置

### MySQL 配置

- **主机**: mysql_dev:3306
- **数据库**: scrm_next
- **用途**: 存储用户管理、系统配置、业务元数据

### ClickHouse 配置

- **主机**: ck_dev:8123
- **数据库**: scrm_next
- **用途**: 存储用户行为分析、实时统计数据

## 🚀 使用方法

### 开发环境初始化

#### 1. 初始化 MySQL

```bash
# 1. 创建表结构
mysql -h mysql_dev -P 3306 -u root -p scrm_next < mysql_tables_schema.sql

# 2. 导入测试数据
mysql -h mysql_dev -P 3306 -u root -p scrm_next < mysql_mock_data.sql
```

#### 2. 初始化 ClickHouse

```bash
# 1. 创建表结构
clickhouse-client --host ck_dev --port 8123 --query "$(cat clickhouse_tables_schema.sql)"

# 2. 导入测试数据
clickhouse-client --host ck_dev --port 8123 --query "$(cat clickhouse_mock_data.sql)"
```

### 生产环境部署

**生产环境只需要执行表结构文件：**

- `mysql_tables_schema.sql`
- `clickhouse_tables_schema.sql`

然后导入真实业务数据。

## 📊 数据说明

### MySQL 表结构

包含以下业务表：

**核心系统表（9 张）**：

- `sys_user` - 系统用户表
- `sys_role` - 系统角色表
- `sys_permission` - 系统权限表
- `sys_user_role` - 用户角色关联表
- `sys_role_permission` - 角色权限关联表
- `sys_operation_log` - 系统操作日志表
- `sys_config` - 系统配置表
- `product_line` - 产品线表
- `product_version` - 产品版本表

**业务扩展表（6 张）**：

- `user_login_records` - 用户登录记录表
- `user_segments` - 用户分群表
- `user_segment_members` - 用户分群成员表
- `product_usage_stats` - 产品使用统计表
- `data_permission_config` - 数据权限配置表
- `feature_config` - 功能配置表

### ClickHouse 表结构

包含以下分析表：

- `user_behavior_events` - 用户行为事件表
- `feature_usage_stats` - 功能使用统计表
- `user_path_analysis` - 用户路径分析表
- `funnel_conversion_data` - 漏斗转化数据表
- `realtime_stats_summary` - 实时统计汇总表
- `user_activity_stats` - 用户活跃度统计表
- `product_usage_stats` - 产品使用统计表

### 模拟数据内容

- **MySQL 模拟数据**: 包含用户分群、功能配置、登录记录等基础业务数据
- **ClickHouse 模拟数据**: 包含用户行为事件、功能使用统计、路径分析等分析数据

## ⚠️ 注意事项

1. **开发阶段**: 可以直接删除旧表重新创建，所有表都使用 `DROP TABLE IF EXISTS`
2. **数据库名称**: MySQL 和 ClickHouse 都使用 `scrm_next` 数据库
3. **执行顺序**: 先执行表结构文件，再执行模拟数据文件
4. **依赖关系**: 模拟数据文件依赖表结构文件，请按顺序执行

## 🔄 自动初始化

项目启动时会自动：

- 验证 MySQL 连接和表结构
- 初始化 ClickHouse 表结构（如果连接成功）

如需完整的测试数据，请手动执行模拟数据文件。

---

**更新时间**: 2025-07-02  
**版本**: v1.0
