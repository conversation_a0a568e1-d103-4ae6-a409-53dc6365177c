package com.foxit.crm.modules.behavioranalysis.test;

import com.foxit.crm.modules.behavioranalysis.domain.repository.UserPathAnalysisRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户路径分析仓储配置测试
 * 验证Bean注入和@Primary注解配置
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@SpringBootTest
@ActiveProfiles("test")
public class UserPathAnalysisRepositoryConfigTest {

    @Autowired
    private UserPathAnalysisRepository defaultRepository;

    @Autowired
    @Qualifier("userPathAnalysisRepositoryReal")
    private UserPathAnalysisRepository realRepository;

    @Autowired
    @Qualifier("userPathAnalysisRepositoryMock")
    private UserPathAnalysisRepository mockRepository;

    @Test
    public void testRepositoryInjection() {
        // 验证所有Repository都成功注入
        assertNotNull(defaultRepository, "默认Repository应该被注入");
        assertNotNull(realRepository, "真实数据Repository应该被注入");
        assertNotNull(mockRepository, "模拟数据Repository应该被注入");
    }

    @Test
    public void testPrimaryAnnotation() {
        // 验证@Primary注解生效，默认注入的是真实数据实现
        assertEquals(realRepository.getClass(), defaultRepository.getClass(),
                "默认注入应该是真实数据实现（@Primary）");

        System.out.println("默认注入实现: " + defaultRepository.getClass().getSimpleName());
        System.out.println("真实数据实现: " + realRepository.getClass().getSimpleName());
        System.out.println("模拟数据实现: " + mockRepository.getClass().getSimpleName());
    }

    @Test
    public void testRepositoryInstancesAreDifferent() {
        // 验证不同的Repository实例是独立的
        assertNotEquals(realRepository, mockRepository,
                "真实数据实现和模拟数据实现应该是不同的实例");

        // 验证真实数据实现包含"Real"字样
        assertTrue(realRepository.getClass().getSimpleName().contains("Real"),
                "真实数据实现类名应该包含'Real'");

        // 验证模拟数据实现不包含"Real"字样
        assertFalse(mockRepository.getClass().getSimpleName().contains("Real"),
                "模拟数据实现类名不应该包含'Real'");
    }

    @Test
    public void testBeanNames() {
        // 验证Bean名称配置正确
        assertNotNull(realRepository, "userPathAnalysisRepositoryReal Bean应该存在");
        assertNotNull(mockRepository, "userPathAnalysisRepositoryMock Bean应该存在");

        // 验证类型匹配
        assertTrue(realRepository instanceof UserPathAnalysisRepository,
                "真实数据实现应该实现UserPathAnalysisRepository接口");
        assertTrue(mockRepository instanceof UserPathAnalysisRepository,
                "模拟数据实现应该实现UserPathAnalysisRepository接口");
    }
}
