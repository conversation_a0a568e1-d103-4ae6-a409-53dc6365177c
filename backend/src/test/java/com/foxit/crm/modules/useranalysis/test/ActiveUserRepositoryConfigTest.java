package com.foxit.crm.modules.useranalysis.test;

import com.foxit.crm.modules.useranalysis.domain.repository.ActiveUserRepository;
import com.foxit.crm.modules.useranalysis.infrastructure.repository.ActiveUserRepositoryRealImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 活跃用户Repository配置测试
 * 验证真实数据实现的Bean注入配置是否正确
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
class ActiveUserRepositoryConfigTest {

    @Autowired
    private ActiveUserRepository defaultRepository; // 默认注入，应该是真实数据实现

    @Autowired
    @Qualifier("activeUserRepositoryReal")
    private ActiveUserRepository realRepository; // 真实数据实现

    @Test
    void testBeanConfiguration() {
        log.info("开始测试ActiveUser Repository Bean配置");

        // 检查Bean是否注入成功
        assertNotNull(defaultRepository, "默认Repository注入失败");
        assertNotNull(realRepository, "真实数据Repository注入失败");

        // 检查默认注入是否为真实数据实现（@Primary注解效果）
        assertTrue(defaultRepository instanceof ActiveUserRepositoryRealImpl,
                "默认注入应该是真实数据实现");

        // 检查指定Bean是否为对应的实现
        assertTrue(realRepository instanceof ActiveUserRepositoryRealImpl,
                "activeUserRepositoryReal应该是真实数据实现");

        // 检查默认注入和指定注入是否为同一类型（@Primary效果）
        assertSame(defaultRepository.getClass(), realRepository.getClass(),
                "默认注入和真实数据实现应该是同一类型");

        log.info("默认注入实现: {}", defaultRepository.getClass().getSimpleName());
        log.info("真实数据实现: {}", realRepository.getClass().getSimpleName());

        log.info("ActiveUser Repository Bean配置测试通过！");
    }

    @Test
    void testRepositoryInstanceType() {
        log.info("开始测试Repository实例类型");

        // 验证默认注入的Repository类型
        String defaultType = defaultRepository.getClass().getSimpleName();
        String realType = realRepository.getClass().getSimpleName();

        assertEquals("ActiveUserRepositoryRealImpl", defaultType.replaceAll("\\$\\$.*", ""));
        assertEquals("ActiveUserRepositoryRealImpl", realType.replaceAll("\\$\\$.*", ""));

        log.info("Repository实例类型测试通过！");
    }

    @Test
    void testPrimaryAnnotationEffect() {
        log.info("开始测试@Primary注解效果");

        // @Primary注解应该使得真实数据实现成为默认注入
        assertSame(defaultRepository.getClass(), realRepository.getClass(),
                "@Primary注解应该使真实数据实现成为默认选择");

        log.info("@Primary注解效果测试通过！");
    }
}
