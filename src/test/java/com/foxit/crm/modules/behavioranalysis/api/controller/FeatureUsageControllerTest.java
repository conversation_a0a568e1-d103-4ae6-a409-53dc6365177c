package com.foxit.crm.modules.behavioranalysis.api.controller;

import com.foxit.crm.modules.behavioranalysis.application.service.FeatureUsageService;
import com.foxit.crm.modules.behavioranalysis.domain.repository.FeatureUsageRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 功能使用分析控制器测试
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@ExtendWith(MockitoExtension.class)
class FeatureUsageControllerTest {

    @Mock
    private FeatureUsageService featureUsageService;

    @InjectMocks
    private FeatureUsageController featureUsageController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(featureUsageController).build();
    }

    @Test
    void testGetPublicUsageStatistics() throws Exception {
        // 准备测试数据
        FeatureUsageRepository.FeatureUsageAnalysisSummary mockSummary = 
            new FeatureUsageRepository.FeatureUsageAnalysisSummary(
                50L, // totalFeatures
                35L, // activeFeatures
                10000L, // totalUsage
                1500L, // uniqueUsers
                200.0, // avgUsagePerFeature
                6.67, // avgUsagePerUser
                Arrays.asList("search", "filter", "export"), // topFeatures
                LocalDateTime.now()
            );

        when(featureUsageService.getFeatureUsageAnalysisSummary(any())).thenReturn(mockSummary);

        // 执行测试
        mockMvc.perform(get("/behavior/feature/public/usage-statistics")
                .param("startDate", "2025-01-01")
                .param("endDate", "2025-01-31")
                .param("includeComparison", "true")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.totalFeatures").value(50))
                .andExpect(jsonPath("$.data.activeFeatures").value(35))
                .andExpect(jsonPath("$.data.totalUsage").value(10000))
                .andExpect(jsonPath("$.data.uniqueUsers").value(1500));
    }

    @Test
    void testGetPublicFeatureList() throws Exception {
        // 准备测试数据
        List<FeatureUsageRepository.FeatureInfo> mockFeatureList = Arrays.asList(
            new FeatureUsageRepository.FeatureInfo(
                "search", "搜索功能", "CORE", "全局搜索功能", "BASIC", true, 1, LocalDateTime.now()
            ),
            new FeatureUsageRepository.FeatureInfo(
                "filter", "筛选功能", "CORE", "数据筛选功能", "BASIC", true, 2, LocalDateTime.now()
            )
        );

        when(featureUsageService.getFeatureList(any(), any())).thenReturn(mockFeatureList);

        // 执行测试
        mockMvc.perform(get("/behavior/feature/public/list")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].featureId").value("search"))
                .andExpect(jsonPath("$.data[0].featureName").value("搜索功能"));
    }

    @Test
    void testGetPublicRealTimeStats() throws Exception {
        // 准备测试数据
        List<FeatureUsageRepository.FeatureStats> mockTopFeatures = Arrays.asList(
            new FeatureUsageRepository.FeatureStats("search", "搜索功能", 500L, 200L, 85.5, LocalDateTime.now()),
            new FeatureUsageRepository.FeatureStats("filter", "筛选功能", 300L, 150L, 75.0, LocalDateTime.now())
        );

        FeatureUsageRepository.FeatureRealTimeStats mockStats = 
            new FeatureUsageRepository.FeatureRealTimeStats(
                1000L, // totalUsage
                300L, // uniqueUsers
                3.33, // avgUsagePerUser
                mockTopFeatures,
                LocalDateTime.now()
            );

        when(featureUsageService.getRealTimeFeatureStats(any(), any(), any())).thenReturn(mockStats);

        // 执行测试
        mockMvc.perform(get("/behavior/feature/public/realtime-stats")
                .param("featureIds", "search", "filter")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.totalUsage").value(1000))
                .andExpect(jsonPath("$.data.uniqueUsers").value(300))
                .andExpect(jsonPath("$.data.topFeatures").isArray())
                .andExpect(jsonPath("$.data.topFeatures.length()").value(2));
    }

    @Test
    void testGetPublicUsageStatisticsWithInvalidDateRange() throws Exception {
        // 测试无效的日期范围
        mockMvc.perform(get("/behavior/feature/public/usage-statistics")
                .param("startDate", "invalid-date")
                .param("endDate", "2025-01-31")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testGetPublicFeatureListWithProductLineFilter() throws Exception {
        // 准备测试数据
        List<FeatureUsageRepository.FeatureInfo> mockFeatureList = Arrays.asList(
            new FeatureUsageRepository.FeatureInfo(
                "product_search", "产品搜索", "PRODUCT", "产品线专用搜索", "ADVANCED", true, 1, LocalDateTime.now()
            )
        );

        when(featureUsageService.getFeatureList(any(), any())).thenReturn(mockFeatureList);

        // 执行测试
        mockMvc.perform(get("/behavior/feature/public/list")
                .param("productLineIds", "1", "2")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1))
                .andExpect(jsonPath("$.data[0].featureId").value("product_search"));
    }
}
