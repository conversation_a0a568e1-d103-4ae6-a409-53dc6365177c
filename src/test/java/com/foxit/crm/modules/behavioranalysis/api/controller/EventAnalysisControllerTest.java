package com.foxit.crm.modules.behavioranalysis.api.controller;

import com.foxit.crm.common.exception.Result;
import com.foxit.crm.modules.behavioranalysis.application.dto.EventAnalysisResponse;
import com.foxit.crm.modules.behavioranalysis.application.service.EventAnalysisService;
import com.foxit.crm.modules.behavioranalysis.domain.repository.EventAnalysisRepository;
import com.foxit.crm.modules.useranalysis.domain.valueobject.TimeRange;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 事件分析控制器测试
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@ExtendWith(MockitoExtension.class)
class EventAnalysisControllerTest {

    @Mock
    private EventAnalysisService eventAnalysisService;

    @InjectMocks
    private EventAnalysisController eventAnalysisController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(eventAnalysisController).build();
    }

    @Test
    void testGetPublicEventTrends() throws Exception {
        // 准备测试数据
        EventAnalysisResponse mockResponse = EventAnalysisResponse.builder()
                .analysisType("EVENT_TREND")
                .totalEvents(1000L)
                .uniqueUsers(500L)
                .build();

        when(eventAnalysisService.getEventTrendAnalysis(any())).thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(get("/behavior/event/public/trends")
                .param("startDate", "2025-01-01")
                .param("endDate", "2025-01-31")
                .param("granularity", "DAY")
                .param("eventIds", "login", "register")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.analysisType").value("EVENT_TREND"))
                .andExpect(jsonPath("$.data.totalEvents").value(1000))
                .andExpect(jsonPath("$.data.uniqueUsers").value(500));
    }

    @Test
    void testGetPublicEventList() throws Exception {
        // 准备测试数据
        List<EventAnalysisRepository.EventInfo> mockEventList = Arrays.asList(
                new EventAnalysisRepository.EventInfo("login", "用户登录", "USER", "用户登录事件", true),
                new EventAnalysisRepository.EventInfo("register", "用户注册", "USER", "用户注册事件", true)
        );

        when(eventAnalysisService.getEventList(any(), any())).thenReturn(mockEventList);

        // 执行测试
        mockMvc.perform(get("/behavior/event/public/list")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].eventId").value("login"))
                .andExpect(jsonPath("$.data[0].eventName").value("用户登录"));
    }

    @Test
    void testGetEventTrendsWithAuthentication() throws Exception {
        // 准备测试数据
        EventAnalysisResponse mockResponse = EventAnalysisResponse.builder()
                .analysisType("EVENT_TREND")
                .totalEvents(2000L)
                .uniqueUsers(800L)
                .build();

        when(eventAnalysisService.getEventTrendAnalysis(any())).thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(get("/behavior/event/trends")
                .param("startDate", "2025-01-01")
                .param("endDate", "2025-01-31")
                .param("granularity", "DAY")
                .param("eventIds", "login", "register", "purchase")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.analysisType").value("EVENT_TREND"))
                .andExpect(jsonPath("$.data.totalEvents").value(2000))
                .andExpect(jsonPath("$.data.uniqueUsers").value(800));
    }

    @Test
    void testGetEventTrendsWithInvalidDateRange() throws Exception {
        // 测试无效的日期范围
        mockMvc.perform(get("/behavior/event/public/trends")
                .param("startDate", "2025-01-31")
                .param("endDate", "2025-01-01") // 结束日期早于开始日期
                .param("granularity", "DAY")
                .param("eventIds", "login")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testGetEventTrendsWithMissingParameters() throws Exception {
        // 测试缺少必需参数
        mockMvc.perform(get("/behavior/event/public/trends")
                .param("startDate", "2025-01-01")
                // 缺少 endDate 参数
                .param("granularity", "DAY")
                .param("eventIds", "login")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }
}
